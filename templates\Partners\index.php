<?php 
$courseCountry =  ucfirst($this->request->getParam('country'));
$this->assign('title', 'Yoga centers & ashrams in '. $courseCountry);
$this->assign('meta_desc', 'Find yoga centers, ashrams, schools anywhere in '. ucfirst($this->request->getParam('country')).'.'); ?>

 <?php
ob_start();
echo '<script type="application/ld+json">' . json_encode($allPartnerSchema, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . '</script>';
$this->assign('json_ld_schema', ob_get_clean());
?>
<?php
$ogTags = <<<HTML
<meta property="og:title" content="Yoga in India - Find Yoga Courses, Retreats & Teacher Training" />
<meta property="og:description" content="Find Yoga Courses, Retreats & Teacher Training" />
<meta property="og:url" content="{$this->Url->build(null, ['fullBase' => true])}" />
<meta property="og:type" content="website" />
HTML;
$this->assign('og_tags', $ogTags);
?>

<link rel="stylesheet" href="<?= $this->Url->webroot('css/center.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/filter.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/card.css') ?>">
<script src="<?= $this->Url->webroot('js/center.js') ?>"></script>
<script src="<?= $this->Url->webroot('js/filter-list.js') ?>"></script>
<script src="https://rawgit.com/ckrack/scrollsnap-polyfill/develop/dist/scrollsnap-polyfill.bundled.js"></script>
<section class="course-list-container overflow-y-auto max-h-screen snap-y snap-mandatory md:overflow-visible md:max-h-none md:snap-none">
    <div class="px-6 md:px-10 lg:px-25 xl:pl-40 xl:pr-21 2xl:px-60">
         <nav class="flex breadcrumb-nav desktop-view" aria-label="Breadcrumb">
               <ol class="pt-4 flex items-center space-x-1 md:space-x-0 rtl:space-x-reverse">
                <li>
                    <a href="<?= $this->Url->build(['controller' => 'Home', 'action' => 'index']) ?>" 
                    class="flex items-center text-sm text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                        Home
                    </a>
                </li>
                <li class="flex items-center">
                    <svg class="relative start-[1px] rtl:rotate-180 w-[5px] h-3 text-[#1C1B1F] mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                    </svg>
                    <a href="#" class="flex items-center text-sm text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                        Center
                    </a>
                </li>
                <li class="flex items-center" aria-current="page">
            </ol>
        </nav>
        <h1 class="heading desktop-view">Yoga Centers in <?= ucfirst($this->request->getParam('country')) ?></h1>
        <div class="search-content-wrapper" x-data="loadData()"  x-init="init(); exposeForExternal()" x-ref="alpineCourseData">
            <div class="mobile-filter snap-start" x-show="isMobile">
                <div class="filter-container">
                    <div class="mt-[15px] flex items-center justify-between">
                        <h1 class="heading">Yoga Centers in <?= $courseCountry ?></h1>
                    </div>
                    <div class="flex items-center justify-between h-[50px] px-[10px]">
                        <span class="font-medium"><span x-text="total"></span> <span x-text="total === 1 ? 'Result' : 'Results'"></span></span>
                          <!-- Left: Sorting -->
                        <!-- <div class="sorting">
                            <select x-model="selectedSort" @change="fetchData()" class="h-[35px] px-2 border border-gray-300 rounded">
                                <option value="">Sort By</option>
                                <option value="popular">Most Popular</option> 
                                <option value="rating">Highest Rated</option>
                                <option value="newest">Newest First</option>
                            </select>
                        </div> -->
                        <!-- Right: Filter icon and label -->
                        <div class="flex items-center space-x-2 cursor-pointer filter-wrapper" @click="showFilters">
                            <!-- <img src="<?= $this->Url->webroot('img/filter-icon.png') ?>" class="w-[20px] h-[20px]" alt="filter icon">
                            <span>Filters</span> -->
                            <!-- Filter count badge -->
                            <!-- <template x-if="filterCount > 0">
                                <span class="ml-1 inline-flex items-center justify-center text-xs font-semibold text-white bg-default-600 rounded-full w-5 h-5">
                                    <span x-text="filterCount"></span>
                                </span>
                            </template> -->
                        </div>
                    </div>
                    <div class="sort-filter-container">
                        <div class="sort-filter-wrapper">
                            <div class="sort"><i class="fas fa-sort"></i> Sort By</div>
                            <div class="filter filter-wrapper" @click="showFilters">
                                <span>
                                    <i class="fas fa-filter"></i> Filter
                                    <!-- Filter count badge -->
                                    <span>
                                        <template x-if="filterCount > 0">
                                            <span class="ml-1 inline-flex items-center justify-center text-xs font-semibold text-white bg-default-600 rounded-full w-5 h-5">
                                                <span x-text="filterCount" class="count"></span>
                                            </span>
                                        </template>
                                    </span>
                                </span>
                            </div>
                        </div>
                        <div class="sort-option">
                            <ul>
                                <li class="list-head" @click="selectedSort = ''; fetchData()"  value="">Sort By</li>
                                <li value="featured" @click="selectedSort = 'featured'; fetchData()"  >Featured</li>
                                <li value="rating" @click="selectedSort = 'oldest'; fetchData()"  >Oldest</li>
                                <li value="newest" @click="selectedSort = 'newest'; fetchData()" >Newest First</li>
                            </ul>
                        </div>
                    </div>
                    <div class="filters">
                        <div class="filters-container bg-white">
                            <div class="mx-auto pr-0 course-filter">
                                <div class="close flex items-center justify-between">
                                    <span>Filters</span>
                                    <!-- <button><i class="fas fa-times"></i></button> -->
                                     <!-- Clear All Link -->
                                    <template x-if="Object.values(selectedFilters).some(f => 
                                        Array.isArray(f) 
                                            ? f.some(v => v !== 'all' && v.trim() !== '') 
                                            : f && f !== 'all' && f.trim() !== ''
                                        )">
                                        <a href="javascript:void(0)"
                                            class="text-blue-700 hover:underline font-medium filter-clear-all whitespace-nowrap"
                                            @click="clearAllFilters">
                                            Clear All
                                        </a>
                                    </template>
                                </div>
                                <div class="flex items-center justify-between px-2 space-x-2">
                                    <!-- Search Box -->
                                    <div class="mobile-search relative flex-1">
                                        <input type="search" name="search" placeholder="Search" class="bg-white w-full pr-8 search" x-model="search" @input.debounce.500ms="pageNumber = 0;" />
                                        <i class="fas fa-search absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500" x-show="search.length === 0" x-transition></i>
                                    </div>
                                </div>
                                <div class="flex" x-data="{tab: 1}">
                                    <div class="flex flex-col justify-start w-[44%] h-[100vh] overflow-scroll bg-[#EFEFEF] pb-[180px]">
                                        <div class="text-sm border-b-2 border-slate-200 tab" :class="{'z-20 transform tab-active font-bold': tab === 1}" @click.prevent="tab = 1">
                                            <div class="tab-content w-auto">
                                                <p class="mt-0 mb-0 text-[#000]">Center Type</p>
                                            </div>
                                          
                                        </div>
                                        <div class="text-sm border-b-2 border-slate-200 tab" :class="{'z-20 transform tab-active font-bold': tab === 2}" @click.prevent="tab = 2">
                                            <div class="tab-content w-auto">
                                                <p class="mt-0 mb-0 text-[#000]">Yoga Style</p>
                                            </div>
                                        
                                        </div>
                                         <div class="text-sm border-b-2 border-slate-200 tab" :class="{'z-20 transform tab-active font-bold': tab === 3}" @click.prevent="tab = 3">
                                            <div class="tab-content w-auto">
                                                <p class="mt-0 mb-0 text-[#000]">Special Need</p>
                                            </div>
                                        </div>
                                        <div class="text-sm border-b-2 border-slate-200 tab" :class="{'z-20 transform tab-active font-bold': tab === 4}" @click.prevent="tab = 4" @click.prevent="tab = 4">
                                            <div class="tab-content w-auto">
                                                <p class="mt-0 mb-0 text-[#000]">Where</p>
                                            </div>
                                            
                                        </div>
                                        <div class="text-sm border-b-2 border-slate-200 tab" :class="{'z-20 transform tab-active font-bold': tab === 5}" @click.prevent="tab = 5" @click.prevent="tab = 5">
                                            <div class="tab-content w-auto">
                                                <p class="mt-0 mb-0 text-[#000]">When</p>
                                            </div>
                                          
                                        </div>
                                        <div class="text-sm border-b-2 border-slate-200 tab" :class="{'z-20 transform tab-active font-bold': tab === 6}" @click.prevent="tab = 6" @click.prevent="tab = 6">
                                            <div class="tab-content w-auto">
                                                <p class="mt-0 mb-0 text-[#000]">Language</p>
                                            </div>
                                           
                                        </div>
                                    </div>
                                    <div class="w-[79%] 2xl:w-102 ml-[20px] tab-options">
                                        <div class="space-y-6 h-[100vh] overflow-scroll pb-[180px]" x-show="tab === 1">
                                        <p>
                                            <label class="cursor-pointer">
                                                <input type="checkbox"
                                                    value="all"
                                                    :checked="selectedTypes.length === 0 || selectedTypes.includes('all')"
                                                    @change="handleFilterChange($data, 'selectedTypes', 'all', event)" 
                                                > 
        
                                            All Yoga Centers
                                            </label>
                                            </p>
                                            <template x-if="Object.keys(filterLabels.selectedTypes).length">
                                                <template x-for="(label, slug) in filterLabels.selectedTypes" :key="slug">
                                                        <p class="flex items-center">
                                                        <label class="custom-checkbox">
                                                            <input type="checkbox"
                                                                :value="slug"
                                                                x-model="selectedTypes"
                                                                @change="handleFilterChange($data, 'selectedTypes', slug, $event)">
                                                                <span class="checkmark"></span>
                                                                <span x-text="label"></span>
                                                            
                                                                <span class="list-filter-count"
                                                                x-text="'(' + (filterCounts.partner_types[slug] || 0) + ')'"></span>
                                                        </label>
                                                    </p>
                                                </template>
                                            </template>
                                        </div>
                        
                                        <div class="space-y-6 h-[100vh] overflow-scroll pb-[180px]" x-show="tab === 2" x-data="{
                                                styles: window.yogaStyles
                                            }">
                                            <p>
                                                <label class="cursor-pointer">
                                                    <input
                                                        type="checkbox"
                                                        value="all"
                                                        :checked="selectedStyles.length === 0 || selectedStyles.includes('all')"
                                                        @change="handleFilterChange($data, 'selectedStyles', 'all', event)"
                                                    >
                                                All Yoga Styles
                                                </label>
                                            </p>
                                            <template x-for="([slug, style], index) in Object.entries(styles).slice(0, showAll ? undefined : 4)" :key="slug">
                                                <p>
                                                    <label class="cursor-pointer">
                                                        <input
                                                            type="checkbox"
                                                            :value="style"
                                                            x-model="selectedStyles"
                                                            :checked="selectedStyles.includes(style)"
                                                            @change="handleFilterChange($data, 'selectedStyles', style, event)"
                                                        >
                                                        <span x-text="style"></span>
                                                        <span class="list-filter-count" x-text="'(' + (filterCounts.yoga_styles[`${style}`] || 0) + ')'"></span>
                                                    </label>
                                                </p>
                                            </template>

                                            <a href="javascript:void(0)" @click="showAll = !showAll" class="btn-view">
                                                <span x-text="showAll ? 'Show Less' : 'View All'"></span>
                                            </a>
                                        </div>
                                        <div class="space-y-6 h-[100vh] overflow-scroll pb-[180px]" x-show="tab === 3">
                                            <p>  
                                                <label class="cursor-pointer">    
                                                <input
                                                    type="checkbox"
                                                    value="all"
                                                    :checked="selectedNeeds.length === 0 || selectedNeeds.includes('all')"
                                                    @change="handleFilterChange($data, 'selectedNeeds', 'all', event)"
                                                >
                                                All Special Needs
                                                </label>
                                            </p>
                                        <template x-if="Object.keys(filterLabels.selectedNeeds).length">
                                            <template x-for="(label, slug) in filterLabels.selectedNeeds" :key="slug">
                                                    <p class="flex items-center">
                                                    <label class="custom-checkbox">
                                                        <input type="checkbox"
                                                            :value="slug"
                                                            x-model="selectedNeeds"
                                                            @change="handleFilterChange($data, 'selectedNeeds', slug, $event)">
                                                            <span class="checkmark"></span>
                                                            <span x-text="label"></span>
                                                        
                                                            <span class="list-filter-count"
                                                            x-text="'(' + (filterCounts.special_needs[slug] || 0) + ')'"></span>
                                                    </label>
                                                </p>
                                            </template>
                                        </template>
                                        </div>
                                        <!-- <div class="space-y-6 h-[100vh] overflow-scroll pb-[180px]" x-show="tab === 4">
                                            <p>
                                                <label class="cursor-pointer">
                                                    <input type="checkbox" name="siteWhere" value="all"  
                                                    :checked="selectedWhere.includes('all')"
                                                    @change="handleFilterChange($data, 'selectedWhere', 'all', event)"> Anywhere
                                                </label>
                                            </p>
                                            <p>
                                                <label class="cursor-pointer">
                                                    <input type="checkbox" name="siteWhere" value="on_site"   
                                                    :checked="selectedWhere.includes('on_site')"@change="handleFilterChange($data,'selectedWhere', 'on_site', event)"> On-site
                                                </label>
                                                <span class="list-filter-count"  x-text="'(' + (filterCounts.site_types['on_site'] || 0) + ')'"></span>
                                            </p>
                                            <p>
                                                <input class="bg-white" type="text" name="location" placeholder="Enter District, City, State..." x-model="selectedLocation" 
                                                x-init="selectedLocation='<?= h($locationUrl) ?>';  fetchData();"
                                                    @change="pageNumber = 0; fetchData()"></p>
                                            <p>
                                                <label class="cursor-pointer">
                                                    <input type="checkbox" name="mode" value="live" x-model="selectedMode"
                                                    @change="handleFilterChange($data, 'selectedMode', 'live', event)"
                                                > Online - Live
                                                </label>
                                                <span class="list-filter-count" x-text="'(' + (filterCounts.modes['live'] || 0) + ')'"></span>
                                            </p>
                                            <p>
                                                <label class="cursor-pointer">
                                                    <input type="checkbox" name="mode" value="vod"  x-model="selectedMode"
                                                    @change="handleFilterChange($data, 'selectedMode', 'vod', event)"
                                                    > Online - Video On Demand
                                                </label>
                                                <span class="list-filter-count" x-text="'(' + (filterCounts.modes['vod'] || 0) + ')'"></span>
                                            </p>
                                        </div> -->
                                        <div class="space-y-6 h-[100vh] overflow-scroll pb-[180px]" x-show="tab === 4">
                                            <p>
                                                <label class="cursor-pointer">
                                                    <input type="checkbox" name="siteWhere" value="all"
                                                        :checked="selectedWhere.includes('all')"
                                                        @change="handleFilterChange($data, 'selectedWhere', 'all', event)">Anywhere
                                                </label>
                                            </p>

                                            <p>
                                                <label class="cursor-pointer">
                                                    <input class="bg-white" type="text" name="location"
                                                        placeholder="Enter District, City, State..."
                                                        x-model="selectedLocation" x-init="selectedLocation = '<?= h($locationUrl) ?>';  fetchData();"
                                                        @change="pageNumber = 0; fetchData()">
                                                </label>
                                            </p>
                                            <template x-for="mode in modalities" :key="mode.id">
                                                <p>
                                                    <label class="cursor-pointer">
                                                        <input type="checkbox"
                                                            name="mode"
                                                            :value="mode.id"
                                                            x-model="selectedMode"
                                                            @change="handleFilterChange($data, 'selectedMode', mode.id, $event)">
                                                        <span x-text="mode.name"></span>
                                                        <span class="list-filter-count"
                                                            x-text="'(' + (filterCounts.modes[mode.id] || 0) + ')'"></span>
                                                    </label>
                                                </p>
                                            </template>
                                        </div>
                                        <div class="space-y-6 h-[100vh] overflow-scroll pb-[180px]" x-show="tab === 5">
                                            <!-- <p><input type="checkbox" checked>Anytime</p> -->
                                            <!-- <p class="title">When</p> -->
                                            <?php if(!empty($months)){ 
                                                foreach($months as $month){
                                            ?>
                                            <p>
                                                <label class="cursor-pointer">
                                                    <input type="checkbox" value="<?= $month ?>" name="start_month" x-model="selectedCourseDate"  @change="handleFilterChange($data, 'selectedCourseDate', '<?= $month ?>', event)"> <?= $month ?>
                                                </label>
                                                <span class="list-filter-count" x-text="'(' + (filterCounts.course_dates['<?= $month ?>'] || 0) + ')'"></span>
                                            </p>
                                            <?php }
                                            } ?>
                                        </div>
                        
                                        <div class="space-y-6 h-[100vh] overflow-scroll pb-[180px]" x-show="tab === 6">
                                            <p>
                                                <label class="cursor-pointer">
                                                    <input type="checkbox" value="all" x-model="selectedLanguage"   @change="handleFilterChange($data, 'selectedLanguage', 'all', event)"  :checked="selectedLanguage.length === 0 || selectedLanguage.includes('all')"> All Languages
                                                </label>
                                                </p>
                                            <?php if(!empty($languages)){ 
                                                foreach($languages as $language){
                                            ?>
                                            <p>
                                                <label class="cursor-pointer">
                                                    <input type="checkbox" value="<?= $language ?>" name="language" x-model="selectedLanguage" @change="handleFilterChange($data, 'selectedLanguage', '<?= $language ?>', event)"> <?= $language ?>
                                                </label>
                                                <span x-text="'(' + (filterCounts.languages['<?= $language ?>'] || 0) + ')'"></span>
                                                </p>
                                            <?php }
                                            } ?>
                                        </div>
                                    </div>
                                </div>
                                <!-- <div class="text-center mt-4">
                                    <button
                                        @click="applyMobileFilters"
                                        class="text-white px-4 py-2 rounded filter-apply-btn mb-2">
                                        Apply Filters
                                    </button>
                                </div> -->
                                <div class="apply-close-footer">
                                    <div class="apply-close-wrapper">
                                        <div class="close">
                                            <button>close</button>
                                        </div>
                                        <div class="apply-filter">
                                            <button @click="applyMobileFilters"
                                                class="px-4 py-2 rounded filter-apply-btn mb-2">
                                                Apply
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="list-search">
                <div class="flex items-start">
                    <div class="left-details">
                        <div class="flex flex-col space-y-2 desktop-view">
                            <div class="sorting flex items-center justify-between" x-model="selectedSort" @change="fetchData()" >
                                <span class="font-medium"><span x-text="total"></span> <span x-text="total === 1 ? 'Result' : 'Results'"></span></span>    
                                <select>
                                    <option value="">Sort By</option>
                                    <option value="popular">Most View</option> 
                                    <option value="newest">New Listing</option>
                                    <option value="oldest">Old Listing</option>
                                    <option value="rating">High Rated</option>
                                </select>
                            </div>
                        </div>
                        <div class="search-content flex flex-wrap gap-2 gap-y-3" x-show="!isMobile">
                            <template x-for="(filters, category) in selectedFilters" :key="category">
                                <template x-if="filters.length > 0 && filters.some(f => f !== 'all' && f.trim() !== '')">
                                    <template x-for="(filter, index) in filters" :key="index">
                                        <template x-if="filter !== 'all' && filter.trim() !== ''">
                                            <span>
                                                <span x-text="getFilterLabel(category, filter)"></span>
                                                <i class="fas fa-times text-xs ml-2 cursor-pointer"
                                                @click="removeFilter(category, index)"></i>
                                            </span>
                                        </template>
                                    </template>
                                </template>
                            </template>
                        </div>
                        <div x-show="isLoading" x-cloak id="loader" class="text-gray-500 spinner-loader"></div>

                        <template x-if="!isLoading && filteredData.length === 0 && mobileData.length === 0">
                            <div class="text-gray-500">No data available.</div>
                        </template>
                        <div class="featured-yoga course-card-container grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 gap-4 desktop-view" x-show="!isMobile">
                            <!-- Display Paginated Items -->
                            <!-- <ul class="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"> -->
                                <template x-for="item in filteredData" :key="item.id" x-data="{
                                baseUrl: '<?= $this->Url->build('/') ?>',
                                lang: '<?= $this->request->getParam('lang') ?>'
                            }">
                                <a :href="`${baseUrl}${lang}/yoga-centers/` + 
                                    (item.country?.name?.replace(/\s+/g, '-') || 'india') + 
                                    '/' +
                                     (item.region_name?.toLowerCase().replace(/\s+/g, '-') || 'unknown-region') +
                                     '/' +
                                    (item.state?.name?.replace(/\s+/g, '-') || '') + 
                                    '/' + 
                                    (item.city?.name?.replace(/\s+/g, '-') || '') + 
                                    '/' + 
                                    (item.slug?.toLowerCase().replace(/\s+/g, '-') || '')">

                                <!-- <li class="bg-white rounded-[10px] shadow" id="`${item.id}`"> -->
                                    <div class="course-card bg-white rounded-[10px] center-card card-container">
                                        <img class="h-[157px] w-100 object-cover yoga-img" :src="`${item.logo_url}`" alt="Yoga Image" />
                                        <div class="text-sm card-body">
                                            <p class="info line-clamp-2"><i class='fas fa-map-marker-alt'></i> 
                                            <span x-text="
                                            [
                                                item.city?.name,
                                                item.state?.name
                                            ].filter(Boolean).join(', ')
                                            "></span></p>
                                            <h3 class="text-gray-900 leading-none yoga-name line-clamp-2" x-html="item.name +`<span class='rating-wrapper'><span class='rating'>4.5</span><i class='fas fa-star'></i></span>`"></h3>
                                            <p class="text-gray-600 yoga-description line-clamp-4" x-html="item.short_description"></p>
                                            <p class="time" x-html="`<i class='fas fa-calendar-alt'></i> `+ (item.is_open == 1 ? 'Open' : 'Closed')"></p>
                                            <p class="text-gray-600 mode line-clamp-1" x-html="`<img src='<?= $this->Url->webroot('img/yoga-class.png') ?>' alt='Yoga class' />`+item.styles"></p>
                                            <p class="text-gray-600 lang" x-html="`<i class='fas fa-globe'></i>`+item.languages"></p>
                                           
                                        </div>
                                    </div>
                                <!-- </li> -->
                                </a>
                                </template>
                            <!-- </ul> -->
                        </div>
                        <!-- Pagination Controls -->
                        <div class="flex justify-start space-x-2 pagination" x-show="!isLoading && filteredData.length > 0">
                            <button
                            class="prev bg-gray-200 hover:bg-gray-300"
                            @click="prevPage"
                            :disabled="currentPage === 1"
                            >
                            <svg class="h-8 w-8 text-[#A3C4A9]" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="15 18 9 12 15 6"></polyline>
                            </svg>
                            </button>
                        
                            <template x-for="page in totalPages" :key="page">
                            <button
                                class="px-3 py-1 rounded"
                                :class="page === currentPage ? 'bg-[#A3C4A9] text-[#FFFFFF] active' : 'bg-[#FFFFFF] text-[#A3C4A9] hover:bg-[#A3C4A9] hover:text-[#ffffff]'"
                                @click="goToPage(page)"
                                x-text="page"
                            ></button>
                            </template>
                        
                            <button
                            class="next bg-gray-200 hover:bg-gray-300"
                            @click="nextPage"
                            :disabled="currentPage === totalPages"
                            >
                            <svg class="h-8 w-8 text-[#A3C4A9]" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polyline points="9 18 15 12 9 6"></polyline>
                            </svg>
                            </button>
                        </div>
                    </div>
                        
                    <aside class="right-filters">
                        <div x-data="{ search: '' }"  class="filters-container">
                            <div class="course-filter">
                                 <div class="flex items-center justify-between space-x-4 mb-3">
                                    <span class="text-gray-700 font-medium">Filters</span>
                                    <template x-if="filterCount > 0">
                                        <span class="inline-flex items-center justify-center text-xs font-semibold text-white bg-default-600 rounded-full w-5 h-5" style="margin-right:70px;">
                                            <span x-text="filterCount"></span>
                                        </span>
                                    </template>
                                    <template x-if="Object.values(selectedFilters).some(f => 
                                        Array.isArray(f) 
                                            ? f.some(v => v !== 'all' && v.trim() !== '') 
                                            : f && f !== 'all' && f.trim() !== ''
                                        )">
                                        <a href="javascript:void(0)" class="text-blue-700 hover:underline font-medium filter-clear-all"  @click="clearAllFilters">Clear All</a>
                                    </template>
                                </div>
                                <div class="filter-group">
                                    <input type="search" name="search" placeholder="Search Here" class="bg-white search" x-model="search" @input.debounce.500ms="pageNumber = 0; fetchData()"/>
                                    <i class="fas fa-search" 
                                    x-show="search.length === 0"
                                    x-transition
                                    ></i>
                                </div>
                                <p class="title">Yoga Center</p>
                                <div class="yoga-types">
                                    <p>  
                                    <label class="custom-checkbox">    
                                        <input
                                            type="checkbox"
                                            value="all"
                                            :checked="selectedTypes.length === 0 || selectedTypes.includes('all')"
                                            @change="handleFilterChange($data, 'selectedTypes', 'all', event)"
                                        > All Yoga Centers
                                    </label></p>
                                    <template x-if="Object.keys(filterLabels.selectedTypes).length">
                                        <template x-for="(label, slug) in filterLabels.selectedTypes" :key="slug">
                                                <p class="flex items-center">
                                                <label class="custom-checkbox">
                                                    <input type="checkbox"
                                                        :value="slug"
                                                        x-model="selectedTypes"
                                                        @change="handleFilterChange($data, 'selectedTypes', slug, $event)">
                                                        <span class="checkmark"></span>
                                                        <span x-text="label"></span>
                                                    
                                                        <span class="list-filter-count"
                                                        x-text="'(' + (filterCounts.partner_types[slug] || 0) + ')'"></span>
                                                </label>
                                            </p>
                                        </template>
                                    </template>
                                </div>
                            </div>
                            <div class="yoga-filter">
                                <p class="title">Yoga Style</p>
                                <div class="yoga-types">
                                    <p>
                                        <input
                                            type="checkbox"
                                            value="all"
                                            :checked="selectedStyles.length === 0 || selectedStyles.includes('all')"
                                            @change="handleFilterChange($data, 'selectedStyles', 'all', event)"
                                        >
                                        All Yoga Styles
                                    </p>
                                    <template x-for="([slug, style], index) in Object.entries(styles).slice(0, showAll ? undefined : 4)" :key="slug">
                                    <p>
                                        <input
                                            type="checkbox"
                                            :value="style"
                                            x-model="selectedStyles"
                                            :checked="selectedStyles.includes(style)"
                                            @change="handleFilterChange($data, 'selectedStyles', style, event)"
                                        >
                                        <span x-text="style"></span>
                                    <span class="list-filter-count" x-text="'(' + (filterCounts.yoga_styles[`${style}`] || 0) + ')'"></span>
                                    </p>
                                    </template>
                                    <a href="javascript:void(0)" @click="showAll = !showAll" class="btn-view">
                                        <span x-text="showAll ? 'Show Less' : 'View All'"></span>
                                    </a>
                                </div>
                            </div>
                            <div class="special-filter">
                                <p class="title">Special Need</p>
                                <div class="yoga-types">
                                    <p>  <input
                                        type="checkbox"
                                        value="all"
                                        :checked="selectedNeeds.length === 0 || selectedNeeds.includes('all')"
                                        @change="handleFilterChange($data, 'selectedNeeds', 'all', event)"
                                    >
                                         All Special Needs</p>
                                    <template x-if="Object.keys(filterLabels.selectedNeeds).length">
                                        <template x-for="(label, slug) in filterLabels.selectedNeeds" :key="slug">
                                            <p class="flex items-center">
                                                <label class="custom-checkbox">
                                                    <input type="checkbox"
                                                        :value="slug"
                                                        x-model="selectedNeeds"
                                                        @change="handleFilterChange($data, 'selectedNeeds', slug, $event)">
                                                        <span class="checkmark"></span>
                                                        <span x-text="label"></span>
                                                    
                                                        <span class="list-filter-count"
                                                        x-text="'(' + (filterCounts.special_needs[slug] || 0) + ')'"></span>
                                                </label>
                                            </p>
                                        </template>
                                    </template>
                                </div>
                            </div>
                            <!-- <div class="where-filter">
                                <p class="title">Where</p>
                                <div class="yoga-types">
                                    <p>
                                        <input type="checkbox" name="siteWhere" value="all" x-model="selectedWhere" @change="handleFilterChange($data, 'selectedWhere', 'all', event)"> Anywhere
                                    </p>

                                    <p>
                                        <input type="checkbox" name="siteWhere" value="on_site"  x-model="selectedWhere" @change="handleFilterChange($data, 'selectedWhere', 'on_site', event)"> On-site
                                        <span class="list-filter-count"  x-text="'(' + (filterCounts.site_types['on_site'] || 0) + ')'"></span>
                                    </p>

                                    <p>
                                        <input class="bg-white" type="text" name="location" placeholder="Enter City, State..." 
                                        x-model="selectedLocation"
                                        x-init="selectedLocation='<?= h($locationUrl) ?>';  fetchData();"
                                        @change="pageNumber = 0; fetchData()">
                                    </p>
                                    <p>
                                        <input type="checkbox" name="mode" value="live" x-model="selectedMode"
                                        @change="handleFilterChange($data, 'selectedMode', 'live', event)"
                                        > Online - Live
                                        <span class="list-filter-count" x-text="'(' + (filterCounts.modes['live'] || 0) + ')'"></span>
                                    </p>
                                    <p>
                                        <input type="checkbox" name="mode" value="vod"  x-model="selectedMode"
                                        @change="handleFilterChange($data, 'selectedMode', 'vod', event)"
                                        > Online - Video On Demand
                                        <span class="list-filter-count" x-text="'(' + (filterCounts.modes['vod'] || 0) + ')'"></span>
                                    </p>
                                </div>
                            </div> -->
                             <div class="where-filter">
                                <p class="title">Where</p>
                                <div class="yoga-types">
                                    <p class="flex items-center">
                                        <label class="custom-checkbox"><input type="checkbox" name="siteWhere"
                                                value="all" x-model="selectedWhere"
                                                @change="handleFilterChange($data, 'selectedWhere', 'all', event)">
                                                <span class="checkmark"></span>
                                            Anywhere
                                        </label>
                                    </p>

                                    <p class="flex items-center">
                                        <label class="">
                                            <input class="bg-white" type="text"
                                            name="location" placeholder="Enter District, City, State..."
                                            x-model="selectedLocation"
                                            x-init="selectedLocation = '<?= h($locationUrl) ?>';  fetchData();"
                                            @change="pageNumber = 0; fetchData()"><span class="checkmark"></span>
                                        </label>
                                    </p>
                                    <template x-for="mode in modalities" :key="mode.id">
                                        <p>
                                            <label class="cursor-pointer">
                                                <input type="checkbox"
                                                    name="mode"
                                                    :value="mode.id"
                                                    x-model="selectedMode"
                                                    @change="handleFilterChange($data, 'selectedMode', mode.id, $event)">
                                                <span x-text="mode.name"></span>
                                                <span class="list-filter-count"
                                                    x-text="'(' + (filterCounts.modes[mode.id] || 0) + ')'"></span>
                                            </label>
                                        </p>
                                    </template>
                                </div>
                            </div>
                            <!-- <div class="when-filter">
                                <p class="title">Opened</p>
                                <div class="yoga-types">
                                    <div class="filter-date">
                                        <p>
                                            <input type="checkbox" name="start_month" value="all" x-model="selectedCourseDate" @change="handleFilterChange($data, 'selectedCourseDate', 'all', event)"> Anytime
                                        </p>
                                        <?php if(!empty($months)){ 
                                            foreach($months as $month){
                                        ?>
                                        <p>
                                            <input type="checkbox" value="<?= $month ?>" name="start_month" x-model="selectedCourseDate" @change="handleFilterChange($data, 'selectedCourseDate', '<?= $month ?>', event)"> <?= $month ?>
                                            <span class="list-filter-count" x-text="'(' + (filterCounts.course_dates['<?= $month ?>'] || 0) + ')'"></span>
                                        </p>
                                        <?php }
                                        } ?>
                                    </div>
                                </div>
                            </div> -->

                            <div class="when-filter">
                                <p class="title">When</p>
                                <div class="yoga-types">
                                    <div class="filter-date">
                                        <?php if (!empty($months)) {
                                            foreach ($months as $month) {
                                                ?>
                                                <p class="flex items-center">
                                                    <label class="custom-checkbox"><input type="checkbox" value="<?= $month ?>"
                                                            name="start_month" x-model="selectedCourseDate"
                                                            @change="handleFilterChange($data, 'selectedCourseDate', '<?= $month ?>', event)">
                                                        <?= $month ?>
                                                        <span class="checkmark"></span>
                                                        <span class="list-filter-count" x-text="'(' + (filterCounts.course_dates['<?= $month ?>'] || 0) + ')'"></span>
                                                    </label>
                                                </p>
                                            <?php }
                                        } ?>
                                    </div>
                                </div>
                            </div>

                            <!-- <div class="lang-filter">
                                <p class="title">Language</p>
                                <div class="yoga-types">
                                    <p>
                                    <input type="checkbox" value="all" x-model="selectedLanguage" @change="handleFilterChange($data, 'selectedLanguage', 'all', event)"
                                    :checked="selectedLanguage.length === 0 || selectedLanguage.includes('all')"> All Languages</p>
                                    <?php if(!empty($languages)){ 
                                        foreach($languages as $language){
                                    ?>
                                    <p>
                                        <input type="checkbox" value="<?= $language ?>" name="language" x-model="selectedLanguage" @change="handleFilterChange($data, 'selectedLanguage', '<?= $language ?>', event)"> <?= $language ?>
                                        <span x-text="'(' + (filterCounts.languages['<?= $language ?>'] || 0) + ')'"></span>
                                    </p>
                                    <?php }
                                    } ?>
                                </div>
                            </div> -->

                            <div class="lang-filter">
                                <p class="title">Language</p>
                                <div class="yoga-types">
                                    <p class="flex items-center">
                                        <label class="custom-checkbox">
                                            <input type="checkbox" value="all" x-model="selectedLanguage"
                                                @change="handleFilterChange($data, 'selectedLanguage', 'all', event)"
                                                :checked="selectedLanguage.length === 0 || selectedLanguage.includes('all')">
                                            <span class="checkmark"></span>
                                            All Languages
                                        </label>
                                    </p>
                                    <?php if (!empty($languages)) {
                                        foreach ($languages as $language) {
                                            ?>
                                            <p class="flex items-center">
                                                <label class="custom-checkbox">
                                                    <input type="checkbox" value="<?= $language ?>" name="language"
                                                        x-model="selectedLanguage"
                                                        @change="handleFilterChange($data, 'selectedLanguage', '<?= $language ?>', event)">
                                                    <?= $language ?>
                                                    <span class="checkmark"></span>
                                                    <span class="list-filter-count" x-text=" '(' + (filterCounts.languages['<?= $language ?>'] || 0) + ')'"></span>
                                                </label>
                                            </p>
                                        <?php }
                                    } ?>
                                </div>
                            </div>

                        </div>
                        <div class="popular">
                            <p class="title">Most Popular</p>
                            <a href="#" class="btn-popular">Yoga Centers in Goa</a>
                            <a href="#" class="btn-popular">Yoga Centers in Rishikesh</a>
                            <a href="#" class="btn-popular">Yoga Centers in Kerala</a>
                        </div>
                    </aside>
                </div>
                <div id="course-card-container" class="course-card-container featured-yoga flex flex-col gap-4 mobile-view" x-show="isMobile" x-data="{
                            baseUrl: '<?= $this->Url->build('/') ?>',
                            lang: '<?= $this->request->getParam('lang') ?>'
                        }">
                    <template x-for="item in mobileData" :key="item.id" >
                        <a :href="`${baseUrl}${lang}/yoga-centers/` + 
                            (item.country?.name?.replace(/\s+/g, '-') || 'india') + 
                            '/region/' + 
                            (item.state?.name?.replace(/\s+/g, '-') || '') + 
                            '/' + 
                            (item.city?.name?.replace(/\s+/g, '-') || '') + 
                            '/' + 
                            (item.slug || '')">
                        <div class="card-container course-card featured-slider relative w-full bg-white rounded-[10px] snap-start">
                            <img class="h-[157px] w-100 object-cover yoga-img" :src="`${item.logo_url}`" alt="Yoga Image" />
                            <div class="card-body text-sm">
                                <p class="info line-clamp-2"><i class='fas fa-map-marker-alt'></i> 
                                <span x-text="
                                [
                                    item.city?.name,
                                    item.state?.name
                                ].filter(Boolean).join(', ')
                                "></span></p>
                                <h3 class="text-gray-900 leading-none yoga-name" x-html="item.name +`<span class='rating-wrapper'><span class='rating'>4.5</span><i class='fas fa-star'></i></span>`"></h3>
                                <p class="text-gray-600 yoga-description line-clamp-3" x-html="item.short_description"></p>
                                <p class="time" x-html="`<i class='fas fa-calendar-alt'></i> `+ (item.is_open == 1 ? 'Open' : 'Closed')"></p>
                                <p class="text-gray-600 mode line-clamp-1" x-html="`<img src='<?= $this->Url->webroot('img/yoga-class.png') ?>' alt='yoga class' />`+item.styles"></p>
                                <p class="text-gray-600 lang" x-html="`<i class='fas fa-globe'></i>`+item.languages"></p>
                                
                            </div>
                        </div>
                        </a>
                    </template>
                </div>
                <div class="text-center pb-3">
                    <button  @click="nextPage()" 
                        x-show="isMobile && mobileData.length < total" class="px-3 py-1 rounded bg-[#D87A61] text-[#FFFFFF]">
                        Load More
                    </button>
                </div>
            </div>
        </div>
    </div>
    <img src="<?= $this->Url->webroot('img/flower.png') ?>" alt="Flower image" class="section-img">
</section>
<script>
window.yogaStyles = <?= json_encode($yoga_styles, JSON_HEX_TAG | JSON_HEX_AMP | JSON_HEX_APOS | JSON_HEX_QUOT) ?>;
window.filterLabels = <?= json_encode($filterLabels, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_AMP | JSON_HEX_QUOT) ?>;
window.modalities = <?= json_encode($modalities, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_AMP | JSON_HEX_QUOT) ?>;

var siteUrl = "<?= $siteUrl ?>";
var limit = "<?= $limit ?>";
var totalRecords = "<?= isset($totalRecords) ? (int)$totalRecords : 0 ?>;";
var selectedLang = "<?= $lang ?>";
var selectedCountry = "<?= $country ?>";
var region = "<?= $region ?>";

// when getting data from URL set as array //
var selectedTypeFromUrl = <?= json_encode($partnerTypeFilter, JSON_HEX_TAG | JSON_HEX_AMP | JSON_HEX_APOS | JSON_HEX_QUOT) ?>;
var selectedPartnerTypes = [];

if (selectedTypeFromUrl) {
    if (Array.isArray(selectedTypeFromUrl)) {
        selectedPartnerTypes = selectedTypeFromUrl;
    } else if (typeof selectedTypeFromUrl === 'string') {
        selectedPartnerTypes = selectedTypeFromUrl.split(',').map(s => s.trim()).filter(Boolean);
    }
}
</script>