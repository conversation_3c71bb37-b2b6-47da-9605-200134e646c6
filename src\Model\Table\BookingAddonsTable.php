<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * BookingAddons Model
 *
 * @property \App\Model\Table\BookingsTable&\Cake\ORM\Association\BelongsTo $Bookings
 *
 * @method \App\Model\Entity\BookingAddon newEmptyEntity()
 * @method \App\Model\Entity\BookingAddon newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\BookingAddon> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\BookingAddon get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\BookingAddon findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\BookingAddon patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\BookingAddon> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\BookingAddon|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\BookingAddon saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\BookingAddon>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\BookingAddon>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\BookingAddon>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\BookingAddon> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\BookingAddon>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\BookingAddon>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\BookingAddon>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\BookingAddon> deleteManyOrFail(iterable $entities, array $options = [])
 */
class BookingAddonsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('booking_addons');
        $this->setDisplayField('addon_title');
        $this->setPrimaryKey('id');

        $this->belongsTo('Bookings', [
            'foreignKey' => 'booking_id',
            'joinType' => 'INNER',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('booking_id')
            ->notEmptyString('booking_id');

        $validator
            ->integer('booking_item_id')
            ->notEmptyString('booking_item_id');

        $validator
            ->scalar('addon_title')
            ->maxLength('addon_title', 255)
            ->requirePresence('addon_title', 'create')
            ->notEmptyString('addon_title');

        $validator
            ->integer('addon_price')
            ->requirePresence('addon_price', 'create')
            ->notEmptyString('addon_price');

        $validator
            ->integer('quantity')
            ->requirePresence('quantity', 'create')
            ->notEmptyString('quantity');

        $validator
            ->scalar('currency')
            ->maxLength('currency', 10)
            ->requirePresence('currency', 'create')
            ->notEmptyString('currency');

        $validator
            ->decimal('total_price')
            ->requirePresence('total_price', 'create')
            ->notEmptyString('total_price');

        $validator
            ->dateTime('created_at')
            ->notEmptyDateTime('created_at');

        $validator
            ->dateTime('modified_at')
            ->notEmptyDateTime('modified_at');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['booking_id'], 'Bookings'), ['errorField' => 'booking_id']);

        return $rules;
    }
}