<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * BookingItems Model
 *
 * @property \App\Model\Table\BookingsTable&\Cake\ORM\Association\BelongsTo $Bookings
 * @property \App\Model\Table\DiscountsTable&\Cake\ORM\Association\BelongsTo $Discounts
 *
 * @method \App\Model\Entity\BookingItem newEmptyEntity()
 * @method \App\Model\Entity\BookingItem newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\BookingItem> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\BookingItem get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\BookingItem findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\BookingItem patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\BookingItem> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\BookingItem|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\BookingItem saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\BookingItem>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\BookingItem>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\BookingItem>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\BookingItem> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\BookingItem>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\BookingItem>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\BookingItem>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\BookingItem> deleteManyOrFail(iterable $entities, array $options = [])
 */
class BookingItemsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('booking_items');
        $this->setDisplayField('title');
        $this->setPrimaryKey('id');

        $this->belongsTo('Bookings', [
            'foreignKey' => 'booking_id',
            'joinType' => 'INNER',
        ]);
        // $this->belongsTo('Discounts', [
        //     'foreignKey' => 'discount_id',
        // ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('booking_id')
            ->notEmptyString('booking_id');

        $validator
            ->scalar('title')
            ->maxLength('title', 255)
            ->requirePresence('title', 'create')
            ->notEmptyString('title');

        $validator
            ->scalar('first_name')
            ->maxLength('first_name', 255)
            ->requirePresence('first_name', 'create')
            ->notEmptyString('first_name');

        $validator
            ->scalar('last_name')
            ->maxLength('last_name', 255)
            ->requirePresence('last_name', 'create')
            ->notEmptyString('last_name');

        $validator
            ->email('email')
            ->requirePresence('email', 'create')
            ->notEmptyString('email');

        $validator
            ->scalar('phone')
            ->maxLength('phone', 255)
            ->requirePresence('phone', 'create')
            ->notEmptyString('phone');

        $validator
            ->integer('age')
            ->requirePresence('age', 'create')
            ->notEmptyString('age');

        $validator
            ->scalar('food')
            ->maxLength('food', 255)
            ->requirePresence('food', 'create')
            ->notEmptyString('food');

        $validator
            ->scalar('residency')
            ->maxLength('residency', 255)
            ->requirePresence('residency', 'create')
            ->notEmptyString('residency');

        $validator
            ->scalar('currency')
            ->maxLength('currency', 100)
            ->requirePresence('currency', 'create')
            ->notEmptyString('currency');

        $validator
            ->integer('base_price_id')
            ->requirePresence('base_price_id', 'create')
            ->notEmptyString('base_price_id');

        $validator
            ->scalar('base_price_name')
            ->maxLength('base_price_name', 255)
            ->requirePresence('base_price_name', 'create')
            ->notEmptyString('base_price_name');

        $validator
            ->decimal('base_price_amount')
            ->requirePresence('base_price_amount', 'create')
            ->notEmptyString('base_price_amount');

        $validator
            ->decimal('base_price_currency')
            ->requirePresence('base_price_currency', 'create')
            ->notEmptyString('base_price_currency');

        $validator
            ->decimal('hourly_rate')
            ->allowEmptyString('hourly_rate');

        $validator
            ->decimal('exchange_rate')
            ->allowEmptyString('exchange_rate');

        $validator
            ->integer('discount_id')
            ->allowEmptyString('discount_id');

        $validator
            ->decimal('discount_value')
            ->allowEmptyString('discount_value');

        $validator
            ->decimal('tax_amount')
            ->allowEmptyString('tax_amount');

        $validator
            ->decimal('tax_rate')
            ->allowEmptyString('tax_rate');

        $validator
            ->decimal('sub_total')
            ->allowEmptyString('sub_total');

        $validator
            ->decimal('grand_total')
            ->allowEmptyString('grand_total');

        $validator
            ->scalar('status')
            ->allowEmptyString('status');

        $validator
            ->dateTime('created_at')
            ->notEmptyDateTime('created_at');

        $validator
            ->dateTime('modified_at')
            ->notEmptyDateTime('modified_at');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['booking_id'], 'Bookings'), ['errorField' => 'booking_id']);
        // $rules->add($rules->existsIn(['discount_id'], 'Discounts'), ['errorField' => 'discount_id']);

        return $rules;
    }
}