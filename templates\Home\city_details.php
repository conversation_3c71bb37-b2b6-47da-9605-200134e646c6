<?php
// $city = [
//     'name' => 'Idar',
//     'state' => 'Gujarat',
//     'type' => 'Destination',
//     'country_id' => 'India',
//     'description' => 'Idar is a beautiful town known for its scenic hills and historical significance.',
//     'banner_image' => 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=1200&q=80',
//     'information' => [
//         'introduction' => '<p><strong>Idar</strong> is a town in Sabarkantha district, Gujarat, India, famous for its hills and fort.</p>',
//         'excursions' => '<ul><li>Idar Fort</li><li>Polo Forest</li><li>Rani ki Vav</li></ul>',
//         'getting_to' => '<p>Nearest airport is Ahmedabad. Well connected by road and rail.</p>',
//         'more_details' => '<p>Best visited in winter. Famous for stone quarries.</p>',
//         'places_to_see' => '<p>Idar Fort, Jain Temples, Sharneshwar Temple.</p>',
//         'finding_accommodation' => '<p>Hotels and guest houses available in town.</p>',
//         'other_resources' => '<p>Tourist information center at bus stand.</p>',
//         'best_time_to_visit' => '<p>October to March.</p>',
//         'where_to_eat' => '<p>Local restaurants serve Gujarati cuisine.</p>'
//     ],
//     'near_type' => 'Beach',
//     'lat' => 23.8396,
//     'lng' => 73.0033,
//     // Add multiple locations as an array of ['lat' => ..., 'lng' => ..., 'label' => ...]
//     'locations' => [
//         ['lat' => 23.8396, 'lng' => 73.0033, 'label' => 'Idar (Main)'],
//         ['lat' => 23.8500, 'lng' => 73.0100, 'label' => 'Idar Fort'],
//         ['lat' => 23.8300, 'lng' => 73.0000, 'label' => 'Polo Forest'],
//     ]
// };
?>
<?php $this->append('style'); ?>
    <meta charset="UTF-8">
    <title><?= h($city['meta_title']) ?> - City Details</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .city-banner {
            width: 100%;
            height: 320px;
            object-fit: cover;
            border-radius: 12px;
            margin-bottom: 1.5rem;
        }

        .city-title {
            font-size: 2.2rem;
            font-weight: 700;
            color: #222;
        }

        .city-description {
            font-size: 1.1rem;
            color: #444;
            margin-bottom: 1.5rem;
        }

        .feature-accordion .accordion-button {
            font-weight: 600;
            color: #0d6efd;
        }

        .accordion-body {
            color: #333;
            font-size: 1.05rem;
            background: #fff;
        }

        .city-info-card {
            position: sticky;
            top: 20vh;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            padding: 1.2rem;
            max-width: 320px;
        }

        .city-info-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .city-info-list li {
            display: flex;
            align-items: center;
            margin-bottom: 0.7rem;
        }

        .icon {
            width: 28px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: #0d6efd;
            margin-right: 0.6rem;
            font-size: 1.2rem;
        }

        .label {
            font-weight: 600;
            color: #555;
            min-width: 70px;
        }

        .value {
            color: #222;
            margin-left: 0.3rem;
        }

        #city-map {
            width: 100%;
            height: 340px;
            border-radius: 12px;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }
        .collapse {
            visibility: inherit !important;
        }
        
        /* Breadcrumb Styles */
        .breadcrumb-nav {
            margin-bottom: 1rem;
        }
        
        .breadcrumb-nav a {
            text-decoration: none;
            transition: color 0.2s ease;
        }
        
        .breadcrumb-nav a:hover {
            text-decoration: none;
        }
        
        @media (max-width: 991px) {
            .city-info-card {
                position: static;
                max-width: 100%;
                box-shadow: none;
            }
        }
    </style>

<?php $this->end(); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('css/card.css') ?>">
<div class="container py-4">
    <!-- Breadcrumbs -->
    <nav class="flex breadcrumb-nav desktop-view" aria-label="Breadcrumb">
        <ol class="pt-4 flex items-center space-x-1 md:space-x-0 rtl:space-x-reverse">
            <li>
                <a href="<?= $this->Url->build(['controller' => 'Home', 'action' => 'index']) ?>" 
                class="flex items-center text-sm text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                    Home
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="rtl:rotate-180 w-[5px] h-3 text-[#1C1B1F] mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                    </svg>
                    <a href="<?= $this->Url->build(['controller' => 'Home', 'action' => 'destinationListPage']) ?>" 
                    class="flex items-center text-sm text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                        Yoga in <?= h(ucfirst($breadcrumbData['country'])) ?>
                    </a>
                </div>
            </li>
            <?php
            /*
            if (!empty($breadcrumbData['country'])): ?>
            <li>
                <div class="flex items-center">
                    <svg class="rtl:rotate-180 w-[5px] h-3 text-[#1C1B1F] mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                    </svg>
                    <span class="flex items-center text-sm text-gray-700 dark:text-gray-400">
                       Yoga in <?= h(ucfirst($breadcrumbData['country'])) ?>
                    </span>
                </div>
            </li>
            <?php endif; 
            */
            ?>
            <?php if (!empty($breadcrumbData['region'])): ?>
            <li>
                <div class="flex items-center">
                    <svg class="rtl:rotate-180 w-[5px] h-3 text-[#1C1B1F] mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                    </svg>
                    <span class="flex items-center text-sm text-gray-700 dark:text-gray-400">
                        <?= h(ucwords(str_replace('-', ' ', $breadcrumbData['region']))) ?>
                    </span>
                </div>
            </li>
            <?php endif; ?>
            <?php if (!empty($breadcrumbData['state'])): ?>
            <li>
                <div class="flex items-center">
                    <svg class="rtl:rotate-180 w-[5px] h-3 text-[#1C1B1F] mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                    </svg>
                    <span class="flex items-center text-sm text-gray-700 dark:text-gray-400">
                        <?= h(ucwords(str_replace('-', ' ', $breadcrumbData['state']))) ?>
                    </span>
                </div>
            </li>
            <?php elseif (!empty($city['StateName'])): ?>
            <!-- Fallback for old URLs -->
            <li>
                <div class="flex items-center">
                    <svg class="rtl:rotate-180 w-[5px] h-3 text-[#1C1B1F] mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                    </svg>
                    <span class="flex items-center text-sm text-gray-700 dark:text-gray-400">
                        <?= h($city['StateName']) ?>
                    </span>
                </div>
            </li>
            <?php endif; ?>
            <li aria-current="page">
                <div class="flex items-center">
                    <svg class="rtl:rotate-180 w-[5px] h-3 text-[#1C1B1F] mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                    </svg>
                    <span class="flex items-center text-sm text-gray-700 dark:text-gray-400">
                        <?= h($city['name']) ?>
                    </span>
                </div>
            </li>
        </ol>
    </nav>

    <img src="https://nuhealth.me/assets/images/offerings/offerings-banner-desktop.png" alt="Banner" class="city-banner shadow-sm">

    <div class="row mb-4">
        <div class="col-12">
            <h1 class="city-title">Yoga in <?= h($city['name']) ?></h1>
            <div class="city-description"><?= h($city['description']) ?></div>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <h5 class="mt-4 mb-2">Location Map</h5>
            <div id="city-map"></div>
        </div>
    </div>
    <div class="row g-4">
        <div class="col-lg-8">
            <div class="accordion feature-accordion" id="featureAccordion">
                <?php $idx = 0; 
                $information = is_string($city['information']) ? json_decode($city['information'], true) : $city['information'];
                if ($information && is_array($information)):
                foreach ($information as $key => $val): ?>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="heading<?= $idx ?>">
                            <button class="accordion-button <?= $idx > 0 ? 'collapsed' : '' ?>" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?= $idx ?>" aria-expanded="<?= $idx === 0 ? 'true' : 'false' ?>" aria-controls="collapse<?= $idx ?>">
                                <?= h(ucwords(str_replace('_', ' ', $key))) ?>
                            </button>
                        </h2>
                        <div id="collapse<?= $idx ?>" class="accordion-collapse collapse <?= $idx === 0 ? 'show' : '' ?>" aria-labelledby="heading<?= $idx ?>" data-bs-parent="#featureAccordion">
                            <div class="accordion-body feature-body-content">
                                <?= $val // Do NOT escape here, allow HTML ?>
                            </div>
                        </div>
                    </div>
                <?php $idx++; endforeach; endif; ?>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="city-info-card">
                <ul class="city-info-list">
                    <li><span class="icon"><i class="bi bi-geo-alt"></i></span><span class="label">State:</span> <span class="value text-capitalize"><?= h($city['StateName']) ?></span></li>
                    <li><span class="icon"><i class="bi bi-flag"></i></span><span class="label">Country:</span> <span class="value text-capitalize"><?= h($city['CountryName']) ?></span></li>
                    <li><span class="icon"><i class="bi bi-building"></i></span><span class="label">Location:</span> <span class="value text-capitalize"><?= h($city['type']) ?></span></li>
                    <li><span class="icon"><i class="bi bi-star"></i></span><span class="label">Near:</span> <span class="value text-capitalize"><?= h($city['near_type']) ?></span></li>
                </ul>
            </div>
        </div>
    </div>





<?php $featured_courses_mobile = []; ?>
<section class="featured-yoga py-5 px-6 md:py-5 lg:py-5 xl:py-5 2xl:py-5 px-6 md:px-10 lg:px-25 xl:px-40">
    <img src="<?= $this->Url->webroot('img/flower.png') ?>" class="footer-flower" alt="flower" />
    <h2>Featured Yoga Courses in India</h2>
    <p class="mb-4">Believed to have its deepest roots in Yoga about 5000 years ago, India has its pristine presence and
        glory as the land of Yoga. Yoga has not only helped its aspirant.</p>
    <div class="mx-auto featured-yoga-center">
        <!-- <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 desktop-view"> -->
        <div x-data="courseSlider()" x-init="init()" class="relative w-full desktop-view feature-yoga-home">
            <!-- Prev Button -->
            <button @click="prev" :disabled="current === 0" class="absolute start-[-50px] top-[50%] z-10 bg-white px-2 py-1 disabled:opacity-50 disabled:cursor-not-allowed prev">
                <img src="<?= $this->Url->webroot('img/arrow-back-black.png') ?>" alt="Arrow Back">
            </button>
            <!-- Next Button -->
            <button @click="next" :disabled="current >= maxIndex" class="absolute right-[-50px] top-[50%] z-10 bg-white px-2 py-1 disabled:opacity-50 disabled:cursor-not-allowed next">
                <img src="<?= $this->Url->webroot('img/arrow-next-black.png') ?>" alt="Arrow Next">
            </button>
            <div class=" overflow-hidden">
                <div class="flex transition-transform duration-500 ease-linear space-x-4 w-[92%] relative start-[20px]" :style="`transform: translateX(-${current * (100 / itemsPerView)}%);`">
                    <?php if (!empty($featured_courses)) {
                        foreach ($featured_courses as $course) {
                            $course_date = '';
                            if (count($course->course_batches) > 0 && isset($course->course_batches[0])):
                                $course_date = $course->course_batches[0]->start_date->format('d-m-Y');
                                if(count($course->course_batches) > 1){
                                $course_date .= ' <small>+' . (count($course->course_batches) - 1) . ' more</small>';
                                }
                            endif;

                            $modeparts = '';

                            if (!empty($course->modalities) && is_array($course->modalities)) {
                                $names = array_column($course->modalities, 'name');
                                $modeNames = [];

                                // Online types
                                if (in_array('Online Live', $names)) {
                                    $modeNames[] = 'Online - Live';
                                }
                                if (in_array('Online VOD', $names)) {
                                    $modeNames[] = 'Online - VOD';
                                }
                                if (in_array('Hybrid', $names)) {
                                    $modeNames[] = 'Hybrid';
                                }

                                // On-Site
                                $onSite = in_array('On Site', $names);
                                if ($onSite) {
                                    $location = $course->city->name ?? '';
                                    $modeNames[] = !empty($location) ? h($location) : 'On-Site';
                                }

                                $count = count($modeNames);

                                // Determine icon:
                                if ($count > 1) {
                                    $icon = "<img src=".$this->Url->webroot('img/home_work.png') ."></img>";
                                } elseif ($onSite) {
                                    $icon = "<i class='fas fa-map-marker-alt'></i>";
                                } else {
                                    $icon = "<i class='fas fa-laptop-code'></i>";
                                }

                                if (!empty($modeNames)) {
                                    $modeparts = $icon . ' ' . implode(' | ', $modeNames);
                                }
                            }

                            $featured_courses_mobile[] = [
                                'imgSrc' => $course->image_url,
                                'imgAlt' => !empty($course->partner) ? $course->partner->name : '',
                                'info' => !empty($course->partner) ? '@ ' . $course->partner->name : '',
                                'title' => h($course->name),
                                'desc' => h($course->short_description),
                                'date' => $course_date,
                                'mode' => $modeparts ? $modeparts : "<i class='fas fa-laptop-code'></i>",
                                'lang' => $course->language,
                                'slug' => urlencode($course->slug),
                                'country' => $course->country->name,
                                'state'  => $course->state->name,
                                'city' => $course->city->name,
                                'duration' => $course->duration_details
                            ];
                        
                            $parts = [];
                            $base = $this->Url->build('/');
                            $lang = h($this->request->getParam('lang'));
                            $parts[] = "{$base}{$lang}/yoga-courses";

                            if (!empty($course->country)) {
                                $parts[] = h($course->country->name);
                            }

                            if (!empty($course->state->region)) {
                                $parts[] = h($course->state->region->name);
                            }

                            if (!empty($course->state)) {
                            //   $parts[] = 'region';
                                $parts[] = h($course->state->name);
                            }

                            if (!empty($course->city)) {
                                $parts[] = h($course->city->name);
                            }

                            if (!empty($course->slug)) {
                                $parts[] =h($course->slug);
                            }

                            $url = implode('/', array_map(function($part) {
                                return strtolower(str_replace(' ', '-', $part));
                            }, $parts));
                        ?>

                            <a href="<?= $url ?>" class="flex-none w-full sm:w-1/2 lg:w-1/4 slide">
                                <!-- Card 1 -->
                                <div class="bg-white rounded-lg card-container flex flex-col min-h-[450px] h-[600px]"
                                    id="<?= $course->id; ?>">
                                    <img src="<?= $course->image_url; ?>" alt="Yoga teacher training in india"
                                        class="w-full h-56 object-cover yoga-img">
                                    <div class="card-body">
                                        <p class="info line-clamp-2"><?= !empty($course->partner) ? '@ ' . $course->partner->name : '' ?></p>
                                        <h3 class="yoga-name">
                                            <span class="line-clamp-2">
                                            <?= h($course->name) ?></span>
                                            <span class="rating-wrapper">
                                                <span class="rating">4.5</span>
                                                <i class="fas fa-star"></i>
                                            </span>
                                        </h3>

                                        <p class="text-gray-600 line-clamp-4 yoga-description"><?= h($course->short_description) ?></p>

                                        <p class="time"><i class="fas fa-calendar-alt"></i>
                                            <span><?= $course_date ?></span>
                                        </p>

                                        <p class="mode"><?= $modeparts ?  $modeparts : "<i class='fas fa-laptop-code'></i>"; ?></p>
                                        <p class="lang"><i class="fas fa-globe"></i> <?= $course->language ?></p>
                                    </div>
                                </div>
                            </a>
                        <?php }
                    } ?>
                </div>
            </div>
        </div>
        <div x-data="{  
            autoplayIntervalTime: 4000,          
            slides: <?= htmlspecialchars(json_encode($featured_courses_mobile, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE), ENT_QUOTES, 'UTF-8'); ?>,           
            currentSlideIndex: 1,
            isPaused: false,
            autoplayInterval: null,

            startX: 0,
            endX: 0,
            handleSwipeStart(event) {
                this.startX = event.type.includes('mouse') ? event.clientX : event.touches[0].clientX;
            },
            handleSwipeEnd(event) {
                this.endX = event.type.includes('mouse') ? event.clientX : event.changedTouches[0].clientX;
                const diff = this.startX - this.endX;

                if (Math.abs(diff) > 50) {
                    if (diff > 0) {
                        this.next();
                    } else {
                        this.previous();
                    }
                }
            },

            previous() {                
                if (this.currentSlideIndex > 1) {                    
                    this.currentSlideIndex = this.currentSlideIndex - 1                
                } else {           
                    this.currentSlideIndex = this.slides.length                
                }            
            },            
            next() {                
                if (this.currentSlideIndex < this.slides.length) {                    
                    this.currentSlideIndex = this.currentSlideIndex + 1                
                } else {                   
                    this.currentSlideIndex = 1                
                }            
            },  
            autoplay() {
                this.autoplayInterval = setInterval(() => {
                    if (! this.isPaused) {
                        this.next()
                    }
                }, this.autoplayIntervalTime)
            },
            setAutoplayInterval(newIntervalTime) {
                clearInterval(this.autoplayInterval)
                this.autoplayIntervalTime = newIntervalTime
                this.autoplay()
            },
            generateSeoLink(item) {
                const parts = [baseUrl + lang + '/yoga-courses'];

                if (item.country) {
                    parts.push(item.country.replace(/\s+/g, '-').toLowerCase());
                }
            
                if (item.state) {
                    parts.push('region', item.state.replace(/\s+/g, '-').toLowerCase());
                }

                if (item.city) {
                    parts.push(item.city.replace(/\s+/g, '-').toLowerCase());
                }

                if (item.slug) {
                    parts.push(item.slug);
                }

                return parts.join('/');
            }
        }" x-init="" class="relative w-full featured-slider mobile-view">

            <!-- SLIDES -->
            <div class="card-container relative w-full mb-[20px] min-h-[634px] overflow-hidden"
                @touchstart="handleSwipeStart" @touchend="handleSwipeEnd" @mousedown="handleSwipeStart"
                @mouseup="handleSwipeEnd">
                <div class="flex transition-transform duration-500 ease-linear" :style="`transform: translateX(-${(currentSlideIndex - 1) * 100}%);`">
                    <template x-for="(slide, index) in slides" :key="index">
                            <!-- for clickable -->
                            <a :href="generateSeoLink(slide)" class="link w-full flex-shrink-0">
                                <div class="course-card bg-white rounded-lg h-auto">
                                    <img class="w-full h-56 object-cover rounded-md yoga-img" x-bind:src="slide.imgSrc"
                                        x-bind:alt="slide.imgAlt">
                                    <div class="card-body">
                                        <p class="info line-clamp-2 h-[40px]" x-text="slide.info"></p>
                                        <h3 class="text-xl font-semibold yoga-name line-clamp-2 h-[68px]">
                                            <span x-html="slide.title" class="line-clamp-2"></span>
                                            <span class='rating-wrapper'><span class='rating'>4.5</span> <i class='fas fa-star'></i></span>
                                        </h3>
                                        <p class="text-gray-600 mt-2 line-clamp-4 yoga-description line-clamp-4 h-[72px]" x-text="slide.desc"></p>
                                        <p class="time" x-html="`<i class='fas fa-calendar-alt'></i> `+slide.date"></p>
                                        <p class="mode" x-html="slide.mode"></p>
                                        <p class="lang" x-html="`<i class='fas fa-globe'></i> `+slide.lang"></p>
                                    </div>
                                </div>
                            <!-- </div> -->
                        </a>
                    </template>
                </div>
            </div>

            <!-- indicators -->
            <!-- <div class="indicator absolute rounded-radius bottom-3 md:bottom-5 left-1/2 z-20 flex -translate-x-1/2 gap-4 md:gap-3 bg-transparent px-1.5 py-1 md:px-2" role="group" aria-label="slides" >
                <template x-for="(slide, index) in slides">
                    <button class="size-3 rounded-full transition bg-on-surface dark:bg-on-surface-dark" x-on:click="currentSlideIndex = index + 1" x-bind:class="[currentSlideIndex === index + 1 ? 'bg-[#D87A61]' : 'bg-[#D87A61]/40']" x-bind:aria-label="'slide ' + (index + 1)"></button>
                </template>
            </div> -->
            <button type="button" @click="previous()" :disabled="currentSlideIndex === 1" class="absolute top-[36%] start-[10px] end-[0px] z-30 flex items-center justify-center px-2 py-1 cursor-pointer group focus:outline-none disabled:opacity-50 prev">
                <img src="<?= $this->Url->webroot('img/arrow-back-black.png') ?>" alt="Arrow Back">
            </button>
            <button type="button" @click="next()" :disabled="currentSlideIndex === slides.length" class="absolute top-[36%] end-[10px] z-30 flex items-center justify-center px-2 py-1 cursor-pointer group focus:outline-none disabled:opacity-50 next">
                <img src="<?= $this->Url->webroot('img/arrow-next-black.png') ?>" alt="Arrow Next">
            </button>
        </div>
        <div class="courses-view text-center">
            <button class="mt-4 text-[#D87A61] px-4 py-2" x-data
                :data-url="'<?= $this->Url->build([
                        'lang' => $this->request->getParam('lang'),
                        'controller' => 'Courses',
                        'action' => 'index',
                        'country' => $this->request->getParam('country') ?? 'india'
                    ]) ?>'"
                @click="window.location.href = $el.dataset.url">View All</a>
        </div>
    </div>
</section>







    <div class="row">
        <section class="featured-yoga featured-yoga-center py-5 px-6 md:px-10 lg:px-25 xl:px-40 relative">
            <img src="<?= $this->Url->webroot('img/flower.png') ?>" class="footer-flower" alt="flower" />
            <h2>Popular Yoga Centers and Ashrams</h2>
            <div class="mx-auto">
                <!-- Slider Controls -->
                <button type="button" id="featured-prev" class="absolute top-[41%] md:top-[50%] start-[30px] md:start-[105px] z-30 px-2 py-1 cursor-pointer group focus:outline-none prev" onclick="featuredPrev();">
                    <img src="<?= $this->Url->webroot('img/arrow-back-black.png') ?>" alt="Arrow Back">
                </button>
                <button type="button" id="featured-next" class="absolute top-[41%] md:top-[50%] end-[30px] md:end-[100px] z-30 px-2 py-1 cursor-pointer group focus:outline-none next" onclick="featuredNext();">
                    <img src="<?= $this->Url->webroot('img/arrow-next-black.png') ?>" alt="Arrow Next" class="relative">
                </button>

                <div id="featured-carousel" class="relative w-full overflow-hidden">
                    <div id="featured-slider" class="relative rounded-lg flex items-center w-full">
                        <?php foreach ($featured_partners as $partner): 
                        $base = $this->Url->build('/');
                        $lang = h($this->request->getParam('lang'));
                        $seoUrlParts = [
                            $base . $lang . '/yoga-courses'
                        ];
                        if (!empty($partner['country'])) {
                            $seoUrlParts[] = strtolower(str_replace(' ', '-', $partner['country']));
                        }
                        if (!empty($partner['state'])) {
                            $seoUrlParts[] = 'region/' . strtolower(str_replace(' ', '-', $partner['state']));
                        }
                        if (!empty($partner['city'])) {
                            $seoUrlParts[] = strtolower(str_replace(' ', '-', $partner['city']));
                        }
                        if (!empty($partner['slug'])) {
                            $seoUrlParts[] = $partner['slug'];
                        }
                        $seoUrl = implode('/', $seoUrlParts);
                        ?>
                        <div class="duration-700 ease-in-out featured-center-carousel mr-[0px] md:mr-[30px] lg:mr-[30px] xl:mr-[30px]">
                            <div class="bg-white rounded-lg course-card card-container flex flex-col min-h-[450px] h-[600px]">
                                <a href="<?= h($seoUrl) ?>">
                                <img src="<?= h($partner['imgSrc']) ?>" alt="<?= h($partner['imgAlt']) ?>" class="w-full h-56 object-cover yoga-img">
                                <div class="card-body">
                                
                                    <p class="info line-clamp-2"><i class='fas fa-map-marker-alt'></i> <?= h($partner['info']) ?></p>
                                    <h3 class="yoga-name">
                                        <span class="line-clamp-2"><?= h($partner['title']) ?></span>
                                        <span class="rating-wrapper">
                                            <span class="rating">4.5</span>
                                            <i class="fas fa-star"></i>
                                        </span>
                                    </h3>
                                    <p class="text-gray-600 line-clamp-4 yoga-description"><?= h($partner['desc']) ?></p>
                                    <p class="time"><i class='fas fa-calendar-alt'></i> <?= h($partner['is_open']) ?></p>
                                    <p class="text-gray-600 mode line-clamp-1">
                                        <img src='<?= $this->Url->webroot('img/yoga-class.png') ?>' alt='Yoga class'/>
                                        <?= h($partner['styles']) ?>
                                    </p>
                                    <p class="text-gray-600 lang">
                                        <i class='fas fa-globe'></i> <?= h($partner['lang']) ?>
                                    </p>
                                </div>
                                </a>
                            </div> 
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <div class="courses-view text-center">
                    <!-- <button class="view text-[#D87A61] px-4 py-2 mt-" x-data
                        :data-url="'<?= $this->Url->build([
                                'lang' => $this->request->getParam('lang'),
                                'controller' => 'Partners',
                                'action' => 'index',
                                'country' => $this->request->getParam('country') ?? 'india'
                            ]) ?>'"
                        @click="window.location.href = $el.dataset.url">View All</button> -->
                    <button class="mt-4 text-[#D87A61] px-4 py-2" x-data :data-url="'<?= $this->Url->build(['lang' => $this->request->getParam('lang'), 'controller' => 'Partners', 'action' => 'index']) ?>'" @click="window.location.href = $el.dataset.url">View All</button>
                </div>
            </div>
        </section>

        
    </div>



 <!-- New Featured teacher slider starts  -->
    <div x-data="carouselTeachers()" x-init="init()" class="relative w-full">
        <h2>Featured Teachers</h2>
        <!-- Navigation Arrows -->
        <button @click="prev" :disabled="currentSlide === 0" class="absolute left-[-17px] lg:left-[-50px] top-[55%] lg:top-[40%] -translate-y-1/2 z-10 bg-white px-2 py-1 disabled:opacity-50 disabled:cursor-not-allowed prev">
            <img src="<?= $this->Url->webroot('img/arrow-back-black.png') ?>" alt="Arrow Back">
        </button>
        
        <button @click="next" :disabled="currentSlide >= slides.length - visibleItems" class="absolute right-[-5px] lg:right-[-35px] top-[55%] lg:top-[40%] -translate-y-1/2 z-10 bg-white px-2 py-1 disabled:opacity-50 disabled:cursor-not-allowed next">
            <img src="<?= $this->Url->webroot('img/arrow-next-black.png') ?>" alt="Arrow next">
        </button>
        <div class="relative w-full overflow-hidden" @touchstart="handleSwipeStart" @touchend="handleSwipeEnd" @mousedown="handleSwipeStart" @mouseup="handleSwipeEnd">
            <div class="flex gap-x-4 transition-transform duration-500 ease-in-out feature-carousel-container mb-[30px]" :style="`transform: translateX(-${currentSlide * (100 / visibleItems)}%)`">
                <template x-for="(item, i) in slides" :key="i">
                    <div class="flex-[0_0_calc(50%-1rem)] lg:flex-[0_0_calc(20%-1rem)] flex-shrink-0 text-center box-border">
                        <a :href="generateSeoLink(item)" class="w-full aspect-square overflow-hidden rounded-lg">
                            <img :src="item.src" class="w-full h-full object-cover rounded-lg round-image" :alt="item.name" />
                        </a>
                        <p class="mt-2 text-[#283148] teacher-name info line-clamp-1" x-text="item.name" :title="item.name"></p>
                        <p class="mt-2 text-[#666666] profession info line-clamp-2 h-[46px]" x-text="item.caption"></p>
                        <a class="mt-2 btn-view text-[#D87A61] px-4 py-2">Read More</a>
                    </div>
                </template>
            </div>
            <!-- Indicators -->
            <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 flex space-x-2 btn-indicate">
                <template x-for="(item, i) in Math.ceil(slides.length - visibleItems + 1)">
                    <span @click="currentSlide = i" class="cursor-pointer w-3 h-3 rounded-full" :class="{'bg-[#A3C4A9]': i === currentSlide, 'bg-[#97bb9d9e]': i !== currentSlide}"></span>
                </template>
            </div>
        </div>
    </div>








</div>
<?php $this->append('script'); ?>
<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script>
    // Remove the jQuery code that sets .feature-body-content via JS, as we now render HTML directly in PHP
    const featured_teachers = <?= json_encode($featured_teachers, JSON_UNESCAPED_UNICODE) ?>;
    const masters = <?= (!empty($imported['masters'])) ? json_encode($imported['masters'], JSON_UNESCAPED_UNICODE) : "[]" ?>

    function carouselTeachers() {
        return {
            slides: typeof featured_teachers !== 'undefined' ? featured_teachers : [],
            currentSlide: 0,
            visibleItems: 5,
            autoplayInterval: null,
            autoplaySpeed: 3000, // Number of slides visible at a time
            startX: 0,
            endX: 0,
            handleSwipeStart(event) {
                this.startX = event.type.includes('mouse') ? event.clientX : event.touches[0].clientX;
            },
            handleSwipeEnd(event) {
                this.endX = event.type.includes('mouse') ? event.clientX : event.changedTouches[0].clientX;
                const diff = this.startX - this.endX;

                if (Math.abs(diff) > 50) {
                    if (diff > 0) {
                        this.next();
                    } else {
                        this.prev();
                    }
                }
            },
            init() {
                this.setResponsiveItems();
                window.addEventListener('resize', this.setResponsiveItems);
            },

            setResponsiveItems() {
                const width = window.innerWidth;
                if (width < 992) {         // Tailwind's sm breakpoint (mobile)
                    this.visibleItems = 2;
                } else {
                    this.visibleItems = 5; // desktop
                }
            },
            
            next() {
                // Max slides before looping back
                // let maxSlides = this.slides.length - this.visibleItems;
                // if (this.currentSlide < maxSlides) {
                //     this.currentSlide++;
                // } else {
                //     this.currentSlide = 0; // Loop to first slide
                // }
                if (this.currentSlide < this.slides.length - this.visibleItems) {
                    this.currentSlide++;
                }
            },
            prev() {
                // let maxSlides = this.slides.length - this.visibleItems;
                // if (this.currentSlide > 0) {
                //     this.currentSlide--;
                // } else {
                //     this.currentSlide = maxSlides; // Loop to last visible set
                // }
                if (this.currentSlide > 0) {
                    this.currentSlide--;
                }
            },
            startAutoplay() {
                this.autoplayInterval = setInterval(() => this.next(), this.autoplaySpeed);
            },
            resetAutoplay() {
                clearInterval(this.autoplayInterval);
                this.startAutoplay();
            },
            generateSeoLink(item) {
                const parts = [baseUrl + lang + '/yoga-centers'];

                if (item.country) {
                    parts.push(item.country.replace(/\s+/g, '-').toLowerCase());
                }

                if (item.state) {
                    parts.push('region', item.state.replace(/\s+/g, '-').toLowerCase());
                }

                if (item.city) {
                    parts.push(item.city.replace(/\s+/g, '-').toLowerCase());
                }

                if (item.slug) {
                    parts.push(item.slug);
                }

                return parts.join('/');
            }
        }
    }

        function courseSlider() {
        return {
        current: 0,
        itemsPerView: 4,
        total: 0,

        get maxIndex() {
            return Math.max(this.total - this.itemsPerView, 0);
        },

        init() {
            this.updateItemsPerView();
            this.total = document.querySelectorAll('.slide').length;

            window.addEventListener('resize', () => {
            this.updateItemsPerView();
            if (this.current > this.maxIndex) {
                this.current = this.maxIndex;
            }
            });
        },

        updateItemsPerView() {
            const w = window.innerWidth;
            if (w < 640) this.itemsPerView = 1;
            else if (w < 1024) this.itemsPerView = 2;
            else this.itemsPerView = 4;
        },

        prev() {
            if (this.current > 0) this.current--;
        },

        next() {
            if (this.current < this.maxIndex) this.current++;
        }
        }
    }





    
    // Map Initialization
    document.addEventListener('DOMContentLoaded', function () {
        // Use multiple locations if available
        var locations = <?= json_encode($city['locations'] ?? []) ?>;
        var defaultLat = <?= json_encode($city['lat']) ?>;
        var defaultLng = <?= json_encode($city['lng']) ?>;
        var defaultLabel = <?= json_encode($city['name']) ?>;

        var mapCenter = locations.length > 0 ? [locations[0].lat, locations[0].lng] : [defaultLat, defaultLng];
        var map = L.map('city-map').setView(mapCenter, 18);

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            maxZoom: 19,
            attribution: '&copy; OpenStreetMap contributors'
        }).addTo(map);

        if (locations.length > 0) {
            locations.forEach(function(loc, idx) {
                var popupText = loc.name ? loc.name : (loc.label || defaultLabel);
                var marker = L.marker([loc.lat, loc.lng]).addTo(map)
                    .bindPopup(popupText);
                if (idx === 0) {
                    marker.openPopup();
                    marker.setIcon(L.icon({
                        iconUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png',
                        iconSize: [25, 41],
                        iconAnchor: [12, 41],
                        popupAnchor: [1, -34],
                        shadowUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png',
                        shadowSize: [41, 41],
                        className: 'highlight-marker'
                    }));
                }
            });
        } else {
            L.marker([defaultLat, defaultLng]).addTo(map)
                .bindPopup(defaultLabel)
                .openPopup();
        }
    });
</script>
<?php $this->end(); ?>
</body>
</html>

