<?php

namespace App\Controller;

use App\Controller\AppController;
use Cake\Core\Configure;
use Cake\Http\Client;
use Cake\Http\Exception\BadRequestException;
use Cake\Log\Log;
use Cake\Routing\Router;
use Cake\I18n\DateTime;

class PartnersController extends AppController
{
    protected $Countries;
    protected $States;
    protected $Cities;
    protected $PartnerRequests;
    protected $Users;
    protected $Courses;
    protected $Partners;
    protected $CourseTypes;
    protected $CourseGalleries;
    protected $YogaStyles;
    protected $PartnerTypes;
    protected $SpecialNeeds;
    protected $MasterData;
    protected $Modalities;
    protected $paginationCount;

    public function initialize(): void
    {
        parent::initialize();
        $this->paginationCount = $this->viewBuilder()->getVar('paginationCount');
        $this->Countries = $this->fetchTable('Countries');
        $this->States = $this->fetchTable('States');
        $this->Cities = $this->fetchTable('Cities');
        $this->PartnerRequests = $this->fetchTable('PartnerRequests');
        $this->Users = $this->fetchTable('Users');
        $this->Courses = $this->fetchTable('Courses');
        $this->Partners = $this->fetchTable('Partners');
        $this->CourseTypes = $this->fetchTable('CourseTypes');
        $this->CourseGalleries = $this->fetchTable('CourseGalleries');
        $this->YogaStyles = $this->fetchTable('YogaStyles');
        $this->MasterData  = $this->fetchTable('MasterData');
        $this->PartnerTypes = $this->fetchTable('PartnerTypes');
        $this->SpecialNeeds = $this->fetchTable('SpecialNeeds');
        $this->Modalities = $this->fetchTable('Modalities');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);

        $this->Authentication->addUnauthenticatedActions(['index', 'submit', 'getStates', 'getCities', 'checkPartnerDuplicatePhone', 'checkPartnerDuplicateEmail', 'generateCaptcha', 'verifyCaptcha', 'checkGd', 'checkFonts', 'details', 'partnerWithUs']);
    }

    public function partnerWithUs()
    {
        $countries = $this->Countries->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->toArray();

        $this->set(compact('countries'));
    }

    public function submit()
    {
        $this->request->allowMethod(['post']);

        $data = $this->request->getData();
        $captchaToken = $data['g-recaptcha-response'] ?? '';

        // Validate CAPTCHA first
        // if (!$this->verifyRecaptcha($captchaToken)) {
        //     $errorMessage = 'Captcha verification failed. Please try again.';

        //     if ($this->request->is('ajax')) {
        //         return $this->response
        //             ->withType('application/json')
        //             ->withStringBody(json_encode([
        //                 'success' => false,
        //                 'message' => $errorMessage
        //             ]));
        //     }

        //     $this->Flash->error(__($errorMessage), ['key' => 'webpartner_error']);
        //     return $this->redirect(['action' => 'index']);
        // }

        // AJAX request
        if ($this->request->is('ajax')) {
            $response = [
                'success' => false,
                'message' => 'Failed to process your request'
            ];

            try {
                $data['status'] = 'A';
                $data['is_approved'] = '0';
                $data['created_at'] = date('Y-m-d H:i:s');

                $partnerRequest = $this->PartnerRequests->newEntity($data);

                if ($this->PartnerRequests->save($partnerRequest)) {
                    // $this->_sendConfirmationEmail($data);

                    $response = [
                        'success' => true,
                        'message' => 'Thank you for your interest. We will contact you soon.'
                    ];
                } else {
                    $response['message'] = 'Validation errors occurred';
                    $response['errors'] = $partnerRequest->getErrors();
                }
            } catch (\Exception $e) {
                $response['message'] = 'An error occurred while processing your request';
                \Cake\Log\Log::error('Partner request error: ' . $e->getMessage());
            }

            return $this->response
                ->withType('application/json')
                ->withStringBody(json_encode($response));
        }

        // Non-AJAX request
        try {
            $data['status'] = 'A';
            $data['is_approved'] = '0';
            $data['created_at'] = date('Y-m-d H:i:s');

            $partnerRequest = $this->PartnerRequests->newEntity($data);

            if ($this->PartnerRequests->save($partnerRequest)) {
                // $this->_sendConfirmationEmail($data);

                $this->Flash->success(__('Thank you for your interest. We will contact you soon.'), ['key' => 'webpartner_success']);
            } else {
                $this->Flash->error(__('There was an error processing your request. Please try again.'), ['key' => 'webpartner_error']);
            }
        } catch (\Exception $e) {
            $this->Flash->error(__('An error occurred. Please try again.'));
            \Cake\Log\Log::error('Partner request error: ' . $e->getMessage());
        }

        return $this->redirect(['action' => 'partnerWithUs']);
    }

    protected function _sendConfirmationEmail($data)
    {
        try {
            // Get location names for the email
            $locationData = [];

            if (!empty($data['country_id'])) {
                $country = $this->Countries->get($data['country_id']);
                $locationData['country_name'] = $country->name;
            }

            if (!empty($data['state_id'])) {
                $state = $this->States->get($data['state_id']);
                $locationData['state_name'] = $state->name;
            }

            if (!empty($data['city_id'])) {
                $city = $this->Cities->get($data['city_id']);
                $locationData['city_name'] = $city->name;
            }

            // Merge location data with form data
            $emailData = array_merge($data, $locationData);

            // Load the GlobalComponent
            $this->loadComponent('Global');

            // Use the send_email method from GlobalComponent
            $result = $this->Global->send_email(
                $data['email'],
                '<EMAIL>',
                'Thank you for your request to partner with Yoga.in',
                'partner_request',
                $emailData,
                null,
                ['<EMAIL>']
            );

            Log::debug('Email send result: ' . json_encode($result));
            if ($result) {
                \Cake\Log\Log::info('Partner request confirmation email sent to: ' . $data['email']);
                return true;
            } else {
                \Cake\Log\Log::error('Failed to send partner request confirmation email using GlobalComponent');
                return false;
            }
        } catch (\Exception $e) {
            \Cake\Log\Log::error('Failed to send partner request confirmation email: ' . $e->getMessage());
            return false;
        }
    }


    private function verifyRecaptcha($token)
    {
        $secretKey = Configure::read('ReCaptcha.secretKey');
        $client = new Client();
        $response = $client->post('https://www.google.com/recaptcha/api/siteverify', [
            'secret' => $secretKey,
            'response' => $token,
        ]);

        $body = $response->getJson();
        return isset($body['success']) && $body['success'] === true;
    }

    public function getStates()
    {
        $this->autoRender = false;

        if ($this->request->is('ajax')) {
            $countryId = $this->request->getQuery('country_id');

            if (!$countryId) {
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode([]));
            }

            $states = $this->States->find()
                ->select(['id', 'name'])
                ->where(['country_id' => $countryId, 'status' => 'A'])
                ->orderAsc('name')
                ->all()
                ->toArray();

            return $this->response->withType('application/json')
                ->withStringBody(json_encode($states));
        }

        throw new BadRequestException('Invalid request');
    }

    public function getCities()
    {
        $this->autoRender = false;

        if ($this->request->is('ajax')) {
            $stateId = $this->request->getQuery('state_id');

            if (!$stateId) {
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode([]));
            }

            $cities = $this->Cities->find()
                ->select(['id', 'name'])
                ->where(['state_id' => $stateId, 'status' => 'A'])
                ->orderAsc('name')
                ->all()
                ->toArray();

            return $this->response->withType('application/json')
                ->withStringBody(json_encode($cities));
        }

        throw new BadRequestException('Invalid request');
    }

    public function checkPartnerDuplicateEmail()
    {
        $this->request->allowMethod(['post']);
        $email = $this->request->getData('email');
        $currentId = $this->request->getData('id');
        $isDuplicate = false;

        $query = $this->PartnerRequests->find()
            ->where(['email' => $email])
            ->where(['status !=' => 'D']);

        if (!empty($currentId)) {
            $query->where(['id !=' => $currentId]);
        }

        if ($query->count() > 0) {
            $isDuplicate = true;
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode(['isDuplicate' => $isDuplicate]));
    }
    public function checkPartnerDuplicatePhone()
    {
        $this->request->allowMethod(['post']);

        $phoneNumber = str_replace(' ', '', trim($this->request->getData('phone_number')));
        $country_code = str_replace(' ', '', trim($this->request->getData('country_code')));
        $currentId = $this->request->getData('id');
        $isDuplicate = false;

        // Use expression to apply REPLACE() in SQL to remove spaces from DB field
        $query = $this->PartnerRequests->find();
        $query->where(function ($exp, $q) use ($phoneNumber, $country_code) {
            return $exp
                ->eq("REPLACE(PartnerRequests.mobile, ' ', '')", $phoneNumber)
                ->eq('country_code', $country_code)
                ->notEq('status', 'D');
        });

        if (!empty($currentId)) {
            $query->andWhere(['PartnerRequests.id !=' => $currentId]);
        }

        if ($query->count() > 0) {
            $isDuplicate = true;
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode(['isDuplicate' => $isDuplicate]));
    }
    public function generateCaptcha()
    {
        $this->autoRender = false;

        try {
            // Check if GD is installed
            if (!extension_loaded('gd')) {
                throw new \Exception('GD Library is not installed');
            }

            // Generate random code
            $code = substr(str_shuffle("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"), 0, 6);

            // Store in session
            $this->getRequest()->getSession()->write('CAPTCHA_CODE', $code);

            // Create image with error checking
            $width = 170;
            $height = 50;

            $image = @imagecreatetruecolor($width, $height);
            if (!$image) {
                throw new \Exception('Failed to create image');
            }

            // Allocate colors
            $background = imagecolorallocate($image, 255, 255, 255);
            $textColor = imagecolorallocate($image, 0, 0, 0);
            $noiseColor = imagecolorallocate($image, 100, 120, 180);

            // Fill background
            imagefill($image, 0, 0, $background);

            // Add random lines
            for ($i = 0; $i < 6; $i++) {
                imageline(
                    $image,
                    mt_rand(0, $width),
                    mt_rand(0, $height),
                    mt_rand(0, $width),
                    mt_rand(0, $height),
                    $noiseColor
                );
            }

            // Add random dots
            for ($i = 0; $i < 1000; $i++) {
                imagesetpixel($image, mt_rand(0, $width), mt_rand(0, $height), $noiseColor);
            }

            // Try multiple font paths
            $fontPaths = [
                WWW_ROOT . 'fonts/arial.ttf' => 'Webroot Arial',
                WWW_ROOT . 'fonts/ARIAL.TTF' => 'Webroot Arial (caps)',
                '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf' => 'Linux DejaVu',
                '/usr/share/fonts/TTF/DejaVuSans.ttf' => 'Linux DejaVu Alt',
                'C:/Windows/Fonts/arial.ttf' => 'Windows Arial',
                '../../webroot/fonts/ARIAL.TTF' => 'Relative Arial'
            ];

            $debug[] = "\nChecking font paths:";
            $fontPath = null;
            $fontName = null;

            foreach ($fontPaths as $path => $name) {
                $debug[] = "$name ($path): " . (file_exists($path) ? "EXISTS" : "not found");
                if (file_exists($path)) {
                    $fontPath = $path;
                    $fontName = $name;
                    break;
                }
            }

            $debug[] = "\nSelected font: " . ($fontName ?? 'NONE');
            $debug[] = "Font path: " . ($fontPath ?? 'NONE');

            // Get environment-specific font size
            $env = env('APP_ENV', 'development');
            $fontSize = Configure::read("Captcha.fontSize.$env") ??
                Configure::read('Captcha.defaultSize') ??
                25;

            $debug[] = "Environment: $env";
            $debug[] = "Font size: $fontSize";

            if (!$fontPath) {
                $debug[] = "No TTF font found, using basic text";
                imagestring($image, 5, 30, 15, $code, $textColor);
            } else {
                // Calculate text size and position
                $angle = 0;

                // Get text dimensions
                $bbox = imagettfbbox($fontSize, $angle, $fontPath, $code);
                if ($bbox === false) {
                    throw new \Exception('Failed to calculate text dimensions');
                }

                // Calculate text position to center it
                $textWidth = $bbox[2] - $bbox[0];
                $textHeight = $bbox[1] - $bbox[7];
                $x = ($width - $textWidth) / 2;
                $y = ($height + $textHeight) / 2;

                $debug[] = "\nFont metrics:";
                $debug[] = "Font size: $fontSize";
                $debug[] = "Text width: $textWidth";
                $debug[] = "Text height: $textHeight";
                $debug[] = "X position: $x";
                $debug[] = "Y position: $y";

                $result = imagettftext(
                    $image,
                    $fontSize,
                    $angle,
                    (int)$x,
                    (int)$y,
                    $textColor,
                    $fontPath,
                    $code
                );

                if ($result === false) {
                    throw new \Exception('Failed to add text to image');
                }
            }


            // Clear any previous output
            ob_clean();

            header('Content-Type: image/png');
            header('Cache-Control: no-cache, no-store, must-revalidate');
            header('Pragma: no-cache');
            header('Expires: 0');

            // Output image
            $success = imagepng($image);
            if (!$success) {
                throw new \Exception('Failed to output PNG image');
            }

            imagedestroy($image);
        } catch (\Exception $e) {

            $errImage = imagecreatetruecolor(170, 50);
            $bgColor = imagecolorallocate($errImage, 255, 200, 200);
            $textColor = imagecolorallocate($errImage, 255, 0, 0);
            imagefill($errImage, 0, 0, $bgColor);
            imagestring($errImage, 3, 5, 5, 'CAPTCHA Error', $textColor);
            header('Content-Type: image/png');
            imagepng($errImage);
            imagedestroy($errImage);
        }
    }

    public function checkGd()
    {
        $this->autoRender = false;

        // Check GD extension
        if (extension_loaded('gd')) {
            echo "GD is installed!\n";
            echo "GD Version: " . gd_info()['GD Version'] . "\n";

            // Show all GD features
            print_r(gd_info());
        } else {
            echo "GD is NOT installed!";
        }
        exit;
    }

    // Add a new debug method to check font paths
    public function checkFonts()
    {
        $this->autoRender = false;

        echo "<pre>";
        echo "Font Path Check:\n\n";

        $fontPaths = [
            WWW_ROOT . 'fonts/arial.ttf' => 'Webroot Arial',
            WWW_ROOT . 'fonts/ARIAL.TTF' => 'Webroot Arial (caps)',
            '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf' => 'Linux DejaVu',
            '/usr/share/fonts/TTF/DejaVuSans.ttf' => 'Linux DejaVu Alt',
            'C:/Windows/Fonts/arial.ttf' => 'Windows Arial',
            '../../webroot/fonts/ARIAL.TTF' => 'Relative Arial'
        ];

        foreach ($fontPaths as $path => $name) {
            echo "$name:\n";
            echo "  Path: $path\n";
            echo "  Exists: " . (file_exists($path) ? "YES" : "NO") . "\n";
            if (file_exists($path)) {
                echo "  Size: " . filesize($path) . " bytes\n";
                echo "  Permissions: " . substr(sprintf('%o', fileperms($path)), -4) . "\n";
            }
            echo "\n";
        }

        echo "\nServer Information:\n";
        echo "OS: " . PHP_OS . "\n";
        echo "Web Server User: " . get_current_user() . "\n";
        echo "Current Directory: " . getcwd() . "\n";
        echo "WWW_ROOT: " . WWW_ROOT . "\n";

        echo "</pre>";
        exit;
    }

    public function details(string $country, string $region, string $state, string $city, string $slug)
    {
        try {
            $Partners = $this->fetchTable('Partners');
            $Courses = $this->fetchTable('Courses');
            $CourseTypes = $this->fetchTable('CourseTypes');
            $CourseGalleries = $this->fetchTable('CourseGalleries');
            $Countries = $this->fetchTable('Countries');
            $States = $this->fetchTable('States');
            $Cities = $this->fetchTable('Cities');
            $SpecialNeeds = $this->fetchTable('SpecialNeeds');
            $YogaStyles = $this->fetchTable('YogaStyles');

            $partner = $Partners->find()
                ->where(['Partners.status' => 'A', 'Partners.slug' => trim($slug)])
                ->contain([
                    'PartnerTypes' => ['fields' => ['id', 'name']],
                    'PartnerGalleries' => function ($q) {
                        return $q->where(['PartnerGalleries.status' => 'A']);
                    },
                    'Courses' => function ($q) {
                        return $q
                            ->where(['Courses.status' => 'A'])
                            ->contain([
                                'CourseTypes' => ['fields' => ['id', 'name']],
                                'SpecialNeeds' => ['fields' => ['id', 'name']],
                                'YogaStyles' => ['fields' => ['id', 'name']],
                                'CourseGalleries' => function ($q) {
                                    return $q
                                        ->select(['id', 'course_id', 'media', 'media_type'])
                                        ->where(['CourseGalleries.status' => 'A']);
                                }
                            ]);
                    },
                    'Countries',
                    'States',
                    'Cities'
                ])
                ->first();
            
            if (!empty($partner->courses)) {
                $courseTypes = [];
                $accommodationTypes = [];
                $foodOptions = [];
                $languages = [];

                foreach ($partner->courses as $course) {
                    if (!empty($course->course_type)) {
                        $courseTypes[$course->course_type->id] = $course->course_type->name;
                    }

                    if (!empty($course->accommodation_type)) {
                        $accommodationTypes = array_merge(
                            $accommodationTypes,
                            array_map('trim', explode(',', $course->accommodation_type))
                        );
                    }

                    if (!empty($course->food_options)) {
                        $foodOptions = array_merge(
                            $foodOptions,
                            array_map('trim', explode(',', $course->food_options))
                        );
                    }

                    if (!empty($course->language)) {
                        $courseLangs = array_map('trim', explode(',', $course->language));
                        foreach ($courseLangs as $lang) {
                            if (!empty($lang)) {
                                $languages[] = $lang;
                            }
                        }
                    }
                }

                $accommodationTypes = array_unique($accommodationTypes);
                $foodOptions = array_unique($foodOptions);
                $languages = array_unique($languages);

                $partner->all_course_types = !empty($courseTypes) ? implode(', ', array_values($courseTypes)) : '';
                $partner->all_accommodation_types = !empty($accommodationTypes) ? implode(', ', $accommodationTypes) : '';
                $partner->all_food_options = !empty($foodOptions) ? implode(', ', $foodOptions) : '';
                $partner->all_languages = !empty($languages) ? implode(', ', $languages) : '';
            }

            if (!$partner) {
                throw new BadRequestException(__('Partner not found.'));
            }

            // $yoga_styles = $this->MasterData->getMasterDataById($partner->courses[0]->yoga_styles[0]->id);
            $yoga_styles = null;
            if (!empty($partner->courses) &&
                isset($partner->courses[0]->yoga_styles) &&
                !empty($partner->courses[0]->yoga_styles) &&
                isset($partner->courses[0]->yoga_styles[0]->id)) {
                
                $yoga_styles = $this->MasterData->getMasterDataById($partner->courses[0]->yoga_styles[0]->id);
            }

            $country = $state = $city = null;

            if (!empty($partner->country_id)) {
                $country = $Countries->find()->where(['id' => $partner->country_id])->first();
            }

            if (!empty($partner->state_id)) {
                $state = $States->find()->where(['id' => $partner->state_id])->first();
            }

            if (!empty($partner->city_id)) {
                $city = $Cities->find()->where(['id' => $partner->city_id])->first();
            }

            $morePartners = $Partners->find()
                ->where([
                    'Partners.status' => 'A',
                    'Partners.id !=' => $partner->id,
                ])
                ->contain([
                    'PartnerTypes' => function ($q) {
                        return $q->select(['PartnerTypes.id', 'PartnerTypes.name']);
                    }
                ])
                ->select([
                    'Partners.id',
                    'Partners.name',
                    'Partners.slug',
                    'Partners.image',
                    'Partners.address',
                    'Partners.description',
                ])
                ->limit(3)
                ->all();

            $moreCourses = $Courses->find()
                ->where([
                    'Courses.status' => 'A',
                    'Courses.partner_id' => $partner->id
                ])
                ->contain([
                    'CourseTypes' => function ($q) {
                        return $q->select(['CourseTypes.id', 'CourseTypes.name']);
                    },
                    'Partners' => function ($q) {
                        return $q->select(['Partners.id', 'Partners.name']);
                    },
                    'Cities' => function ($q) {
                        return $q->select(['Cities.id', 'Cities.name']);
                    },
                    'Countries' => function ($q) {
                        return $q->select(['Countries.id', 'Countries.name']);
                    },
                    'States' => function ($q) {
                        return $q->select(['States.id', 'States.name']);
                    },
                    'CourseBatches' => function ($q) {
                        return $q
                            ->select([
                                'CourseBatches.id',
                                'CourseBatches.course_id',
                                'CourseBatches.start_date',
                                'CourseBatches.end_date',
                            ]);
                    }
                ])
                ->select([
                    'Courses.id',
                    'Courses.name',
                    'Courses.slug',
                    'Courses.banner_image',
                    // 'Courses.start_date',
                    // 'Courses.end_date',
                    'Courses.mode',
                    'Courses.language',
                    'Courses.short_description',
                    'Courses.course_type_id',
                ])
                ->limit(3)
                ->all();
                 
                $ogTags=$this->generateOgTags($partner);
                $schemaJson=$this->generatePartenerSchema($partner);
            // Set all variables for the view
            $this->set(compact('partner', 'moreCourses', 'country', 'state', 'city','yoga_styles', 'ogTags', 'schemaJson'));
        } catch (\Exception $e) {
            throw $e;
            $this->Flash->error(__('An error occurred while loading the partner details.'));
            return $this->redirect(['controller' => 'Home', 'action' => 'index']);
        }
    }

    public function generateAllPartnerSchema($partners)
    {
        $items = [];

        foreach ($partners as $index => $partner) {
            $lang = 'en'; // Adjust dynamically if needed
            $partnerSlug = $this->slugify($partner->slug);
            $countrySlug = $this->slugify($partner->country->name ?? '');
            $regionSlug = ($partner->state && $partner->state->region) ? $this->slugify($partner->state->region->name ) : '';
            $citySlug = $this->slugify($partner->city->name ?? '');
            $stateSlug = $this->slugify($partner->state->name ?? '');
            // Build the SEO URL: /en/yoga-centers/country/region/city/partner-slug
            $seoUrl = Router::url("/$lang/yoga-centers/$countrySlug/$regionSlug/$stateSlug/$citySlug/$partnerSlug", true);

            $items[] = [
                '@type' => 'ListItem',
                'position' => $index + 1,
                'url' => $seoUrl
            ];
        }

        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'ItemList',
            'name' => 'Explore Yoga Centers Worldwide',
            'description' => 'Discover certified yoga centers and retreats from trusted partners across the world.',
            'url' => Router::url(null, true),
            'numberOfItems' => count($items),
            'itemListElement' => $items
        ];

        return $schema;
    }

    public function slugify($string)
    {
        $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $string)));
        return urlencode($slug); // ensures safe URL usage
    }
    public function generateAllPartnerSchema_old($partners)
    {
        // $schema = [
        //     "@context" => "https://schema.org",
        //     "@type" => "CollectionPage",
        //     "name" => "Yoga Centers Directory",
        //     "description" => "Find certified yoga centers by country, region, or city.",
        //     "url" => Router::url($this->request->getRequestTarget(), true),
        //     "mainEntity" => []
        // ];

        // foreach ($partners as $partner) {
        //     $schema['mainEntity'][] = [
        //         "@type" => "LocalBusiness",
        //         "name" => $partner->name,
        //         "description" => $partner->short_description,
        //         "url" => Router::url(['controller' => 'Partners', 'action' => 'view', $partner->slug, '_full' => true]),
        //         "telephone" => $partner->phone ?? null,
        //         "email" => $partner->email ?? null,
        //         "address" => [
        //             "@type" => "PostalAddress",
        //             "addressCountry" => $partner->country->name ?? null,
        //             "addressRegion" => $partner->state->name ?? null,
        //             "addressLocality" => $partner->city->name ?? null,
        //             "streetAddress" => $partner->street_address ?? null,
        //         ],
        //         "geo" => [
        //             "@type" => "GeoCoordinates",
        //             "latitude" => $partner->latitude ?? null,
        //             "longitude" => $partner->longitude ?? null
        //         ],
        //         "sameAs" => array_filter([
        //             $partner->website,
        //             $partner->facebook_url,
        //             $partner->instagram_url,
        //             $partner->youtube_url
        //         ])
        //     ];
        // }
        // return $schema;

        
         return $partnersSchema;

    }

    public function generatePartenerSchema($partner)
    {        
         $partner = $partner ?? null;

        $schema = [
            "@context" => "https://schema.org",
            "@type" => "LocalBusiness",
            "name" => $partner->name,
            "description" => strip_tags($partner->short_description ?? ''),
            //"url" => $this->Url->build(null, true),
            "image" => !empty($partner->image) ? $this->Url->build('/uploads/partners/' . $partner->image, true) : null,
            "email" => $partner->email ?? '',
            "telephone" => $partner->phone ?? '',
            "address" => [
                "@type" => "PostalAddress",
                "streetAddress" => $partner->street_address ?? '',
                "addressLocality" => $partner->city->name ?? '',
                "addressRegion" => $partner->state->name ?? '',
                "addressCountry" => $partner->country->name ?? '',
            ],
            "geo" => [
                "@type" => "GeoCoordinates",
                "latitude" => $partner->latitude ?? '',
                "longitude" => $partner->longitude ?? '',
            ],
            "sameAs" => array_filter([
                $partner->website ?? null,
                $partner->facebook_url ?? null,
                $partner->instagram_url ?? null,
                $partner->youtube_url ?? null,
            ])
        ];
         return $schema;

    }


     public function generateOgTags($partner)
    {
         $ogTags = [
                'og:locale' => 'en_US',
                'og:title' => $partner->meta_title ?? $partner->name,
                'og:description' => $partner->meta_description ?? strip_tags(substr($partner->short_description, 0, 200)),
                'og:type' => 'article',
                'og:updated_time' => $partner->modified_at , 
                // 'og:url' => Router::url(['controller' => 'Courses', 'action' => 'view', $course->slug], true),
                'og:image' => !empty($partner->image) 
                    ? Router::url('/img/partners/' . $partner->image, true)
                    : Router::url('/img/default-course.png', true),
                 // Twitter Card
                'twitter:card' => 'summary_large_image',
                'twitter:site' => '@yourBrandHandle', // Replace with your brand's Twitter
                'twitter:title' => $partner->meta_title ?? $partner->name,
                'twitter:description' => $partner->meta_description ?? strip_tags(substr($partner->short_description, 0, 200)),
                'twitter:image' => !empty($partner->image)
                    ? Router::url('/img/partners/' . $partner->image, true)
                    : Router::url('/img/default-course.png', true),    
                
            ];
            return $ogTags;
    }

    public function index(string $country = null, string $region = null, string $state = null, string $city = null, string $slug = null)
    {

       
        $locationUrl = $city ?? $state ?? $this->request->getQuery('location');
        $partnerTypeFilter = $this->request->getQuery('type');
        $specialNeedFilter = $this->request->getQuery('specialNeeds');

        if ($this->request->getParam('type')) {
            $partnerTypeFilter = $this->request->getParam('type');
            $locationUrl= '';
        }

        $modeList = $this->Modalities->selectInputOptions();
        $modalities = [];
        foreach ($modeList as $key => $value) {
            $modalities[] = ['id' => $key, 'name' => $value];
        }

        $filters = [
            'search'      => $this->request->getQuery('search'),
            'partner_type'=> $partnerTypeFilter,
            'yoga_style' => $this->request->getQuery('style'),
            'location'   => $locationUrl,
            'course_date'=> $this->request->getQuery('courseDate'),
            'language'   => $this->request->getQuery('language'),
            'site'       => $this->request->getQuery('site'),
            'mode'       => $this->request->getQuery('mode'),
            'sort'       => $this->request->getQuery('sort'),
            'special_needs' => $this->request->getQuery('specialNeeds'),
            'region'    => $this->request->getQuery('region')
        ];

        $siteUrl = Router::url('/', true);
        $limit = $this->request->getQuery('limit') ?? $this->paginationCount;

        $query = $this->Partners->getList($filters);
       
        $filterCounts = $this->Partners->getFilterCounts($filters);

        $totalRecords = $query->count();

        $listData = $this->paginate($query, [
            'limit' => $limit,
            'page' => $this->request->getQuery('page') ?? 1,
            'order' => ['Courses.created_at' => 'desc']
        ]);


        if (!empty($listData)) {
            foreach ($listData as $data) {
                $styles = [];
                $languages = [];
                if (!empty($data->courses)) {
                    foreach ($data->courses as $course) {
                        if (!empty($course->yoga_styles)) {
                            foreach ($course->yoga_styles as $style) {
                                $styles[$style->id] = $style->name;
                            }
                        }

                        if (!empty($course->language)) {
                            $languages[] = $course->language;
                        }
                    }
                }
                $data->description = strip_tags($data->description);
                $data->styles = implode(', ', array_values($styles));
                $data->languages = implode(', ', array_unique($languages));
            }
        }

        $partner_types = $this->PartnerTypes->getSlugNameList();
        unset($partner_types['teachers']); // Teacher are separate//
        $yoga_styles = $this->MasterData->getYogaStyleList();
        $special_needs = $this->MasterData->getspecialNeedList();
        $modes = $this->Modalities->selectInputOptions();

        // $start = new DateTime(); // current date
        // $end   = (clone $start)->modify('+1 year');

        // $months = [];
        // while ($start <= $end) {
        //     $months[] = $start->format('F Y'); // e.g., "April 2025"
        //     $start = (clone $start)->modify('first day of next month');
        // }

        $monthMap = [
            'Jan' => 'January', 'Feb' => 'February', 'Mar' => 'March', 'Apr' => 'April',
            'May' => 'May', 'Jun' => 'June', 'Jul' => 'July', 'Aug' => 'August',
            'Sep' => 'September', 'Oct' => 'October', 'Nov' => 'November', 'Dec' => 'December'
        ];

        // Fetch all unique operating_months from the DB
        $rawMonths = $this->Partners->find()
            ->select(['operating_months'])
            ->where(['operating_months IS NOT' => null])
            ->distinct()
            ->enableHydration(false)
            ->all()
            ->toList();

        // Flatten and map to full month names
        $allMonths = [];
        foreach ($rawMonths as $row) {
            $monthsArr = explode(',', $row['operating_months']);
            foreach ($monthsArr as $month) {
                $month = trim($month);
                if (!empty($month) && isset($monthMap[$month])) {
                    $allMonths[$month] = $monthMap[$month];
                }
            }
        }

        // Now reorder starting from current month
        $currentMonthShort = date('M'); // e.g., 'Aug'
        $calendarOrder = array_keys($monthMap);

        // Find current month position
        $currentIndex = array_search($currentMonthShort, $calendarOrder);

        // Reorder months starting from current month
        $reorderedKeys = array_merge(
            array_slice($calendarOrder, $currentIndex),
            array_slice($calendarOrder, 0, $currentIndex)
        );

        // Filter and build final list
        $months = [];
        foreach ($reorderedKeys as $short) {
            if (isset($allMonths[$short])) {
                $months[] = $allMonths[$short];
            }
        }

        $languages = Configure::read('Language');
        $lang   = $this->request->getParam('lang');
        $country = $this->request->getParam('country') ?? 'india';

        $filterLabels = [
            'selectedTypes' => $partner_types,
            'selectedStyles'=> $yoga_styles,
            'selectedNeeds' => $special_needs,
            'selectedMode' => $modes
        ];

        // schema for partners
        $allPartnerSchema=$this->generateAllPartnerSchema($listData);
        $this->set(compact('listData', 'siteUrl', 'limit', 'totalRecords', 'partner_types', 'yoga_styles', 'months', 'languages', 'special_needs', 'filterCounts', 'lang', 'country', 'locationUrl', 'partnerTypeFilter', 'region', 'filterLabels','allPartnerSchema', 'modeList', 'modalities'));

        // Check if JSON is requested
        if ($this->request->accepts('application/json')) {
            $this->viewBuilder()->setClassName('Json');

            $pagination = $this->request->getAttribute('paging')['Partners'] ?? [];

            $this->viewBuilder()->setOption('serialize', ['listData', 'pagination', 'totalRecords', 'filterCounts', 'lang', 'country']);

            return;
        }
    }
}
