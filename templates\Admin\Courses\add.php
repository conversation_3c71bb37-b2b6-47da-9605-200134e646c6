<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Offer $offer
 */
?>
<?php $this->append('style'); ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
</head>
<?php $this->end(); ?>

<div class="main-content bg-container">
    <div class="row">
        <div class="col-12 col-xl-12">
            <div class="card card-primary">
                <div class="d-flex justify-content-between align-items-center">
                    <ul class="breadcrumb breadcrumb-style m-0">
                        <li class="breadcrumb-item">Partner Offerings</li>
                        <li class="breadcrumb-item">
                            <a href="<?= $this->Url->build(['controller' => 'Courses', 'action' => 'index']) ?>">Partner Offerings</a>
                        </li>
                        <li class="breadcrumb-item">Add Partner Offering</li>
                    </ul>

                    <a href="javascript:void(0);" class="d-flex align-items-center category-back-button  breadcrumb m-0" id="back-button-mo" onclick="history.back();">
                        <span class="rotate me-2">⤣</span>
                        <small class="fw-bold" data-translate="back"><?= __("BACK") ?></small>
                    </a>
                </div>

                <div class="card-header">
                    <h4>Add Partner Offering</h4>
                </div>
               
                <div class="card-body">
                     <?= $this->Flash->render() ?>
                    <ul class="nav nav-tabs" id="courseTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="basic-tab" data-bs-toggle="tab" href="#basic" role="tab">Basic Information</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="details-tab" data-bs-toggle="tab" href="#details" role="tab">Details </a>
                        </li>
                         <li class="nav-item">
                            <a class="nav-link" id="teachers-tab" data-bs-toggle="tab" href="#teachers" role="tab">Teachers </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link" id="batches-tab" data-bs-toggle="tab" href="#batches" role="tab">Schedule & Bacthes</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="pricing-tab" data-bs-toggle="tab" href="#pricing" role="tab">Pricing</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="media-tab" data-bs-toggle="tab" href="#media" role="tab"> Media</a>
                        </li>
                        <!-- <li class="nav-item">
                            <a class="nav-link" id="review-tab" data-bs-toggle="tab" href="#review" role="tab">Review & Rating</a>
                        </li> -->
                        <li class="nav-item">
                            <a class="nav-link" id="seo-tab" data-bs-toggle="tab" href="#seo" role="tab">SEO</a>
                        </li>
                    </ul>
                    <?php echo $this->Form->create(null, ['id' => 'courseForm','url' => ['controller' => 'Courses', 'action' => 'save'], 'novalidate' => true, 'type' => 'file']); ?>
                    <input type="hidden" id="record_id" value="">
                    <div class="tab-content p-3 border border-top-0" id="courseTabContent">
                        <!-- Basic Info -->
                        <div class="tab-pane fade show active" id="basic" role="tabpanel">
                            <div class="form-group row">
                                <label for="name" class="col-sm-2 col-form-label">Name<span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('name', [
                                        'type' => 'text',
                                        'class' => 'form-control',
                                        'id' => 'course-name',
                                        'placeholder' => __('Name'),
                                        'label' => false,
                                        'required' => false
                                    ]); ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="slug" class="col-sm-2 col-form-label">Slug<span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('slug', [
                                        'type' => 'text',
                                        'class' => 'form-control',
                                        'id' => 'course-slug',
                                        'placeholder' => __('Slug'),
                                        'label' => false,
                                        'required' => false
                                    ]); ?>
                                </div>
                            </div>
                        
                            <div class="form-group row">
                                <label for="course_type_id" class="col-sm-2 col-form-label">Type<span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('course_type_id', [
                                        'type' => 'select',
                                        'options' => $coursetypes,
                                        'empty' => __('Select Course Type'),
                                        'class' => 'form-control form-select',
                                        'label' => false,
                                        'id' => 'course_type_id'
                                    ]); ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="offered_by" class="col-sm-2 col-form-label">Offered By<span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('partner_id', [
                                        'type' => 'select',
                                        'options' => $partners,
                                        'empty' => __('Select Center'),
                                        'class' => 'form-control form-select',
                                        'label' => false,
                                        'id' => 'partner_id'
                                    ]); ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="short_description" class="col-sm-2 col-form-label">Short Description<span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('short_description', [
                                        'type' => 'textarea',
                                        'rows' => '2',
                                        'class' => 'form-control',
                                        'id' => 'short_description',
                                        'placeholder' => __('Short Description'),
                                        'label' => false,
                                    ]); ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="language" class="col-sm-2 col-form-label">Language<span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('language', [
                                        'type' => 'select',
                                        'options' => $languages,
                                        'empty' => __('Select Language'),
                                        'class' => 'form-control form-select mode-checkbox',
                                        'label' => false,
                                        'id' => 'language',
                                    ]); ?>
                                </div>
                            </div>
                           <div class="form-group row">
                                <label for="yoga_style_id" class="col-sm-2 col-form-label">Yoga Styles<span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('yoga_style_id', [
                                        'style'=>'height: 100px',
                                        'type' => 'select',
                                        'id' => 'yoga_style_id',
                                        'options' => $yogaStyles,
                                        'class' => 'select2-multi',
                                        // 'empty' => __('Select Yoga Style'),
                                        'label' => false,
                                        'multiple' => true
                                    ]) ?>
                                </div>
                            </div>
                           <div class="form-group row">
                                <label for="special_need_id" class="col-sm-2 col-form-label">Special Needs<span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('special_need_id', [
                                        'style'=>'height: 100px',
                                        'type' => 'select',
                                        'id' => 'special_need_id',
                                        'options' => $specialNeeds,
                                        'class' => 'select2-multi',
                                        // 'empty' => __('Select Special Need'),
                                        'label' => false,
                                        'multiple' => true
                                    ]) ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="special_need_id" class="col-sm-2 col-form-label">Techniques<span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('techniques', [
                                        'style'=>'height: 100px',
                                        'type' => 'select',
                                        'id' => 'techniques',
                                        'options' => $techniques,
                                        'class' => 'select2-multi',
                                        // 'empty' => __('Select Techniques'),
                                        'label' => false,
                                        'multiple' => true
                                    ]) ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="level" class="col-sm-2 col-form-label">Level<span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('level', [
                                        'style'=>'height: 100px',
                                        'type' => 'select',
                                        'options' => $level,
                                        // 'empty' => __('Select Level'),
                                        'class' => 'select2-multi',
                                        'id' => 'level',
                                        'label' => false,
                                        'multiple' => true
                                    ]); ?>
                                </div>
                            </div>
                             <div class="form-group row">
                                <label for="modality" class="col-sm-2 col-form-label">Modality</label>
                                 <div class="col-sm-6 main-field">
                                        <?= $this->Form->control('modality', [
                                        'type' => 'select',
                                        'options' => $modalities,
                                        // 'empty' => __('Select Modality'),
                                        'class' => 'select2-multi',
                                        'label' => false,
                                        'multiple' => true
                                    ]); ?>
                                </div>
                            </div>
                             <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Country</label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('country_id', [
                                        'type' => 'select',
                                        'options' => $countries,  // Fetch from database
                                        'empty' => 'Select Country',
                                        'class' => 'form-control form-select',
                                        'label' => false,
                                        'id' => 'country_id'
                                    ]) ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">State</label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('state_id', [
                                        'type' => 'select',
                                        'options' => $states,
                                        'empty' => 'Select State',
                                        'class' => 'form-control form-select',
                                        'label' => false,
                                        'id' => 'state_id'
                                    ]) ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">City</label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('city_id', [
                                        'type' => 'select',
                                        'options' => $cities,
                                        'empty' => 'Select City',
                                        'class' => 'form-control form-select',
                                        'label' => false,
                                        'id' => 'city_id'
                                    ]) ?>
                                </div>
                            </div>
                            <!-- <div class="mb-3 col-md-6">
                                <label class="form-label">Rgion</label>
                                <?= $this->Form->control('region_id', [
                                    'type' => 'select',
                                    'options' => [],
                                    'empty' => 'Select Region',
                                    'class' => 'form-control form-select',
                                    'label' => false,
                                    'id' => 'region_id'
                                ]) ?>
                            </div> -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Locality</label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('locality_id', [
                                        'type' => 'select',
                                        'options' => [],
                                        'empty' => 'Select Locality',
                                        'class' => 'form-control form-select',
                                        'label' => false,
                                        'id' => 'locality_id'
                                    ]) ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Address</label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('address', [
                                        'type' => 'textarea',
                                        'rows' => '2',
                                        'class' => 'form-control',
                                        'id' => 'address',
                                        'placeholder' => __('Address'),
                                        'label' => false,
                                    ]); ?>
                                </div>
                            </div>
                              <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Zip Code</label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('zip_code', [
                                        'type' => 'text',
                                        'class' => 'form-control',
                                        'id' => 'zip_code',
                                        'placeholder' => __('Zip Code'),
                                        'label' => false,
                                    ]); ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Latitude</label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('latitude', [
                                        'type' => 'text',
                                        'class' => 'form-control',
                                        'id' => 'latitude',
                                        'placeholder' => __('Latitude'),
                                        'label' => false,
                                    ]); ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Longitude</label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('longitude', [
                                        'type' => 'text',
                                        'class' => 'form-control',
                                        'id' => 'longitude',
                                        'placeholder' => __('Longitude'),
                                        'label' => false,
                                    ]); ?>
                                </div>
                            </div>
                           <div class="form-group row">
                                <label for="image" class="col-sm-2 col-form-label">Women Only</label>
                                <div class="col-sm-2 main-field">
                                    <?= $this->Form->checkbox('is_women_only', [
                                        'class' => 'form-check-input',
                                        'id' => 'women_only',
                                    ]); ?>
                                </div>
                            
                                <label for="image" class="col-sm-2 col-form-label">Is Featured</label>
                                <div class="col-sm-2 main-field">
                                    <?= $this->Form->checkbox('is_featured', [
                                        'class' => 'form-check-input',
                                        'id' => 'is_featured',
                                    ]); ?>
                                </div>
                            </div>
                          
                            <div class="form-group row">
                                <label for="is_accommodation_included" class="col-sm-2 col-form-label">Accommodation Included</label>
                                <div class="col-sm-2 main-field">
                                    <?= $this->Form->checkbox('is_accommodation_included', [
                                        'class' => 'form-check-input',
                                        'id' => 'accommodation_included',
                                    ]); ?>
                                </div>
                                <label for="is_food_included" class="col-sm-2 col-form-label">Food Included</label>
                                <div class="col-sm-2 main-field">
                                    <?= $this->Form->checkbox('is_food_included', [
                                        'class' => 'form-check-input',
                                        'id' => 'food_included',
                                    ]); ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="accommodation_options" class="col-sm-2 col-form-label">Accommodation Options</label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->input('accommodation_options', [
                                        'type' => 'hidden',
                                        'id' => 'accommodation_options',
                                        'label' => false
                                    ]); ?>
                                    <div class="tag-input-container" id="accommodation-tag-container">
                                        <div class="tag-input-wrapper">
                                            <div class="tags-display" id="accommodation-tags-display"></div>
                                            <input type="text"
                                                class="tag-input-field"
                                                id="accommodation-tag-input"
                                                placeholder="Type keyword and press Enter..."
                                                autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="food_options" class="col-sm-2 col-form-label">Food Options</label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->input('food_options', [
                                        'type' => 'hidden',
                                        'id' => 'food_options',
                                        'label' => false
                                    ]); ?>
                             
                                    <div class="tag-input-container" id="food-tag-container">
                                        <div class="tag-input-wrapper">
                                            <div class="tags-display" id="food-tags-display"></div>
                                            <input type="text"
                                                class="tag-input-field"
                                                id="food-tag-input"
                                                placeholder="Type keyword and press Enter..."
                                                autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div> 
                             <div class="form-group row">
                                <label for="image" class="col-sm-2 col-form-label">Has Certification</label>
                                <div class="col-sm-2 main-field">
                                    <?= $this->Form->checkbox('has_certification', [
                                        'class' => 'form-check-input',
                                        'id' => 'has_certification',
                                    ]); ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="image" class="col-sm-2 col-form-label"> Banner Image</label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('banner_image', [
                                        'type' => 'file',
                                        'class' => 'form-control',
                                        'id' => 'image',
                                        'placeholder' => __('Banner Image Upload'),
                                        'label' => false,
                                        'accept' => 'image/*',
                                        'data-translate-placeholder' => 'categoryImageUploadPlaceholder'
                                    ]); ?>
                                    <small class="text-muted"><?= __("Only .jpg, .jpeg, .png files are allowed. Max size: 2MB") ?></small>
                                </div>
                            </div>
                              <div class="form-group row">
                                <label for="status" class="col-sm-2 col-form-label">Status</label>
                                <div class="col-sm-6 main-field">
                                   <?= $this->Form->control('status', [
                                        'type' => 'select',
                                        'class' => 'form-control form-select',
                                        'id' => 'status',
                                        'options' => [
                                            'A' => __('Active'),
                                            'I' => __('Inactive')
                                        ],
                                        'label' => false
                                    ]); ?>
                                </div>
                            </div>
                        </div>
                        <!-- Location -->
                        <div class="tab-pane fade" id="details" role="tabpanel">
                            <?= $this->element('Admin/Courses/details') ?>
                        </div>
                        <div class="tab-pane fade" id="teachers" role="tabpanel">
                            <?= $this->element('Admin/Courses/add_teacher_form', ['i' => '__INDEX__']) ?>
                        </div>
                        <div class="tab-pane fade" id="batches" role="tabpanel">
                            <!-- <div class="accordion" id="batchAccordion"> -->
                                <?php //foreach ($course->course_batches as $i => $batch): ?>
                                   <div id="batch-template" >
                                        <?= $this->element('Admin/Courses/course_batch_form', ['i' => '__INDEX__']) ?>
                                    </div>
                                <?php //  endforeach; ?>
                            <!-- </div> -->
                        </div>
                        <!-- pricing -->
                        <div class="tab-pane fade" id="pricing" role="tabpanel">
                            <?= $this->element('Admin/Courses/course_base_price_form', ['currencies' => $currencies]) ?>
                            <?= $this->element('Admin/Courses/add_course_addons', ['addonTypes' => $addonTypes]) ?>
                        </div>

                        <div class="tab-pane fade" id="media" role="tabpanel">
                            <div class="form-group row">
                                <label for="media" class="col-sm-2 col-form-label"> Upload Media</label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('media[]', [
                                            'type' => 'file',
                                            'class' => 'form-file',
                                            'label' => false,
                                            'multiple' => 'multiple',
                                            'id' => 'imageInput',
                                            'accept' => 'image/*,video/*',
                                            'placeholder' => __("Upload images and videos")
                                        ]); ?>
                                    <small class="text-muted"><?= __("Only .jpg, .jpeg, .png, .gif, .webp, .mp4, .mov, .mkv, .avi files are allowed. Max size: 2MB") ?></small>
                                    <div id="previewContainer">
                                        <ul id="imagePreviewContainer" class="list-unstyled d-flex flex-wrap gap-2 mt-2"></ul>
                                    </div>
                                </div>
                            </div>
                           <div class="form-group row align-items-start">
                                <label for="video_link" class="col-sm-2 col-form-label">Video Link</label>

                                <!-- Input Field Column -->
                                <div class="col-sm-6">
                                    <div id="video-links-wrapper">
                                        <div class="video-link-group mb-2 d-flex gap-2">
                                            <input type="text" name="video_url[]" class="form-control video-link-input" placeholder="Video Link 1">
                                        </div>
                                    </div>
                                </div>

                                <!-- Add More Button Column -->
                                <div class="col-sm-2">
                                    <button type="button" id="add-video-link" class="btn btn-secondary btn-sm w-100">Add More</button>
                                </div>
                            </div>
                        </div>
                        <!-- SEO Metadata -->
                        <div class="tab-pane fade" id="seo" role="tabpanel">
                           <?= $this->element('Admin/Courses/add_seo_data') ?>
                        </div>
                    </div>
                      
                    <div class="text-end mt-3">
                        <button name="action" value="save" class="btn btn-primary submitBtn">Save</button>
                        <button name="action" value="validate" class="btn btn-primary submitBtn">Validate & Save</button>
                    </div>
                    <?= $this->Form->end(); ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->append('script'); ?>
<?= $this->element('Admin/Courses/script') ?>
<script>
document.getElementById('course-name').addEventListener('input', function () {
    const slug = generateSlug(this.value);
    document.getElementById('course-slug').value = slug;
});

function generateSlug(text) {
    return text
        .toString()
        .toLowerCase()
        .trim()
        .replace(/[^a-z0-9\s-]/g, '')     // Remove all non-alphanumeric chars except space and hyphen
        .replace(/\s+/g, '-')             // Replace spaces with hyphens
        .replace(/-+/g, '-');             // Replace multiple hyphens with single one
}

    const formAction = "<?= $this->Url->build(['controller'=> 'Courses', 'action' => 'add']) ?>";
    let allFiles = [];

    document.getElementById('imageInput').addEventListener('change', function(event) {
        let newFiles = Array.from(event.target.files);

        const allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'mp4', 'mov', 'mkv', 'avi'];
        const maxSizeMB = 2;
        const maxSizeBytes = maxSizeMB * 1024 * 1024;
        let invalidFiles = [];

        newFiles.forEach(file => {
            let extension = file.name.split('.').pop().toLowerCase();
            let fileSize = file.size;

            if (!allowedExtensions.includes(extension)) {
                invalidFiles.push({
                    file: file.name,
                    reason: '<?= __('Invalid file type. Only .jpg, .jpeg, .png, .gif,.webp ,.mp4, .mov, .mkv, .avi files are allowed.') ?>'
                });
            } else if (fileSize > maxSizeBytes) {
                invalidFiles.push({
                    file: file.name,
                    reason: `<?= __('File size exceeds the maximum limit of 2MB.') ?>`
                });
            }
        });

        if (invalidFiles.length > 0) {
            let html = "<ul>";
            invalidFiles.forEach(function(invalidFile) {
                html += `<li>${invalidFile.file} - ${invalidFile.reason}</li>`;
            });
            html += '</ul>';

            const wrapper = document.createElement('div');
            wrapper.innerHTML = html;

            Swal.fire({
                title: "<?= __("Invalid Files") ?>",
                content: wrapper,
                confirmButtonText: "<?= __("OK") ?>",
                allowOutsideClick: true
            });
            document.getElementById('imageInput').value = "";
            return;
        }

        allFiles = [...allFiles, ...newFiles];
        renderPreviews();
        updateFileInput();
    });

    function renderPreviews() {
        let previewContainer = document.getElementById('imagePreviewContainer');
        previewContainer.innerHTML = '';
        allFiles.forEach((file, index) => {
            let li = document.createElement('li');
            li.classList.add('media-thumbnail', 'position-relative');
            let fileName = file.name;
            let extension = fileName.split('.').pop().toLowerCase();
            let isImage = ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension);
            let isVideo = ['mp4', 'mov', 'mkv', 'avi'].includes(extension);

            let shortName = fileName.length > 20 ? fileName.slice(0, 17) + '...' : fileName;

            let reader = new FileReader();
            reader.onload = function(e) {
                if (isImage) {
                    li.innerHTML = `
                        <img src="${e.target.result}" alt="<?= __("Image Preview") ?>" class="preview-img" style="width: 120px; height: 100px; object-fit: cover;" />
                        <span class="image-name d-block text-truncate" title="${fileName}">${shortName}</span>
                        <button type="button" class="delete-img-btn btn btn-sm btn-danger position-absolute top-0 end-0" data-index="${index}">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                } else if (isVideo) {
                    li.innerHTML = `
                        <video src="${e.target.result}" class="preview-img" controls style="width: 120px; height: 100px; object-fit: cover;"></video>
                        <span class="image-name d-block text-truncate" title="${fileName}">${shortName}</span>
                        <button type="button" class="delete-img-btn btn btn-sm btn-danger position-absolute top-0 end-0" data-index="${index}">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                }
            };
            reader.readAsDataURL(file);
            previewContainer.appendChild(li);
        });
    }

    document.getElementById('imagePreviewContainer').addEventListener('click', function(e) {
        if (e.target.closest('.delete-img-btn')) {
            let index = e.target.closest('.delete-img-btn').getAttribute('data-index');
            allFiles.splice(index, 1);
            renderPreviews();
            updateFileInput();
        }
    });

    function updateFileInput() {
        let dataTransfer = new DataTransfer();
        allFiles.forEach(file => dataTransfer.items.add(file));
        document.getElementById('imageInput').files = dataTransfer.files;
    }
</script>

<?php $this->end(); ?>