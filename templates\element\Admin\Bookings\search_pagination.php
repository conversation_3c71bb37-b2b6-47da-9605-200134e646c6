<?php
/**
 * Reusable Search and Pagination Component for Booking Module
 * 
 * @var \App\View\AppView $this
 * @var array $pagination Pagination data
 * @var string $search Current search term
 * @var string $searchPlaceholder Placeholder text for search input
 * @var string $searchInputId ID for search input
 * @var string $loadDataFunction JavaScript function name for loading data
 * @var bool $showFilters Whether to show filter toggle button
 * @var string $filterToggleId ID for filter toggle button
 * @var string $filterSectionId ID for filter section
 * @var bool $showSearch Whether to show search input
 */

// Set default values
$searchPlaceholder = $searchPlaceholder ?? 'Search bookings, customers, courses...';
$searchInputId = $searchInputId ?? 'searchInput';
$loadDataFunction = $loadDataFunction ?? 'loadData';
$showFilters = $showFilters ?? true;
$showSearch = $showSearch ?? true;
$filterToggleId = $filterToggleId ?? 'bookingFilterToggle';
$filterSectionId = $filterSectionId ?? 'bookingFilterSection';
?>

<style>
    .search-pagination-component {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        margin-bottom: 20px;
    }

    .search-section {
        padding: 20px 25px;
        border-bottom: 1px solid #f0f0f0;
    }

    .search-container {
        position: relative;
    }

    .search-container .form-control {
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        padding: 12px 45px 12px 15px;
        font-size: 14px;
        transition: all 0.3s ease;
    }

    .search-container .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .search-icon {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        pointer-events: none;
    }

    .filter-controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 25px;
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
    }

    .filter-button .btn {
        border-radius: 8px;
        font-weight: 600;
        padding: 10px 20px;
        transition: all 0.3s ease;
    }

    .filter-button .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .pagination-wrapper {
        padding: 20px 25px;
        background: white;
        border-top: 1px solid #f0f0f0;
    }

    .pagination-info {
        color: #6c757d;
        font-size: 14px;
        font-weight: 500;
    }

    .pagination .page-link {
        border: 1px solid #dee2e6;
        color: #667eea;
        padding: 8px 12px;
        margin: 0 2px;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .pagination .page-link:hover {
        background-color: #667eea;
        border-color: #667eea;
        color: white;
        transform: translateY(-1px);
    }

    .pagination .page-item.active .page-link {
        background-color: #667eea;
        border-color: #667eea;
        color: white;
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    }

    .pagination .page-item.disabled .page-link {
        color: #6c757d;
        background-color: #f8f9fa;
        border-color: #dee2e6;
    }

    @media (max-width: 768px) {
        .filter-controls {
            flex-direction: column;
            gap: 15px;
            align-items: stretch;
        }

        .pagination-wrapper {
            padding: 15px 20px;
        }

        .pagination-wrapper .d-flex {
            flex-direction: column;
            gap: 15px;
            text-align: center;
        }

        .pagination .page-link {
            padding: 6px 10px;
            font-size: 13px;
        }
    }
</style>

<div class="search-pagination-component">
    <?php if ($showSearch || $showFilters): ?>
    <!-- Search Section -->
    <div class="search-section">
        <div class="row align-items-center">
            <?php if ($showSearch): ?>
            <div class="col-md-<?= $showFilters ? '6' : '12' ?>">
                <div class="search-container">
                    <input type="text"
                           id="<?= h($searchInputId) ?>"
                           class="form-control"
                           placeholder="<?= h($searchPlaceholder) ?>"
                           value="<?= h($search ?? '') ?>">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            <?php endif; ?>
            <?php if ($showFilters): ?>
            <div class="col-md-<?= $showSearch ? '6' : '12' ?> <?= $showSearch ? 'text-end' : '' ?>">
                <div class="filter-button">
                    <button type="button" class="btn btn-primary" id="<?= h($filterToggleId) ?>">
                        <i class="fas fa-filter"></i> <span id="filterButtonText">Show Filters</span>
                    </button>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Pagination Section -->
    <?php if (isset($pagination) && $pagination['total_items'] > 0): ?>
    <div class="pagination-wrapper">
        <div class="d-flex justify-content-between align-items-center">
            <div class="pagination-info">
                <?php
                $start = ($pagination['current_page'] - 1) * $pagination['per_page'] + 1;
                $end = min($pagination['current_page'] * $pagination['per_page'], $pagination['total_items']);
                ?>
                Showing <?= $start ?> to <?= $end ?> of <?= $pagination['total_items'] ?> entries
            </div>
            
            <nav aria-label="Pagination">
                <ul class="pagination pagination-sm mb-0">
                    <!-- Previous Page -->
                    <?php if ($pagination['has_prev']): ?>
                        <li class="page-item">
                            <a class="page-link" href="javascript:void(0);" onclick="<?= h($loadDataFunction) ?>(<?= $pagination['current_page'] - 1 ?>)">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                    <?php else: ?>
                        <li class="page-item disabled">
                            <span class="page-link"><i class="fas fa-chevron-left"></i></span>
                        </li>
                    <?php endif; ?>

                    <!-- Page Numbers -->
                    <?php
                    $startPage = max(1, $pagination['current_page'] - 2);
                    $endPage = min($pagination['total_pages'], $pagination['current_page'] + 2);
                    
                    for ($i = $startPage; $i <= $endPage; $i++):
                    ?>
                        <li class="page-item <?= $i == $pagination['current_page'] ? 'active' : '' ?>">
                            <a class="page-link" href="javascript:void(0);" onclick="<?= h($loadDataFunction) ?>(<?= $i ?>)">
                                <?= $i ?>
                            </a>
                        </li>
                    <?php endfor; ?>

                    <!-- Next Page -->
                    <?php if ($pagination['has_next']): ?>
                        <li class="page-item">
                            <a class="page-link" href="javascript:void(0);" onclick="<?= h($loadDataFunction) ?>(<?= $pagination['current_page'] + 1 ?>)">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    <?php else: ?>
                        <li class="page-item disabled">
                            <span class="page-link"><i class="fas fa-chevron-right"></i></span>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    <?php if ($showSearch): ?>
    // Search functionality
    const searchInput = document.getElementById('<?= h($searchInputId) ?>');
    let searchTimeout;

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                const searchTerm = searchInput.value.trim();
                if (typeof <?= h($loadDataFunction) ?> === 'function') {
                    <?= h($loadDataFunction) ?>(1, searchTerm);
                } else if (typeof loadDataWithSearch === 'function') {
                    loadDataWithSearch(1, searchTerm);
                }
            }, 500);
        });

        // Handle Enter key
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                clearTimeout(searchTimeout);
                const searchTerm = this.value.trim();
                if (typeof <?= h($loadDataFunction) ?> === 'function') {
                    <?= h($loadDataFunction) ?>(1, searchTerm);
                } else if (typeof loadDataWithSearch === 'function') {
                    loadDataWithSearch(1, searchTerm);
                }
            }
        });
    }
    <?php endif; ?>

    <?php if ($showFilters): ?>
    // Filter toggle functionality
    const filterToggle = document.getElementById('<?= h($filterToggleId) ?>');
    const filterSection = document.getElementById('<?= h($filterSectionId) ?>');
    const filterButtonText = document.getElementById('filterButtonText');

    if (filterToggle && filterSection) {
        filterToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const isVisible = filterSection.classList.contains('show');

            if (isVisible) {
                filterSection.classList.remove('show');
                this.classList.remove('active');
                this.querySelector('i').classList.remove('fa-times');
                this.querySelector('i').classList.add('fa-filter');
                if (filterButtonText) filterButtonText.textContent = 'Show Filters';
            } else {
                filterSection.classList.add('show');
                this.classList.add('active');
                this.querySelector('i').classList.remove('fa-filter');
                this.querySelector('i').classList.add('fa-times');
                if (filterButtonText) filterButtonText.textContent = 'Hide Filters';
            }
        });
    }
    <?php endif; ?>
});
</script>
