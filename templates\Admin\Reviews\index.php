<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Review> $reviews
 * @var array $statistics
 * @var array $filters
 * @var array $statusOptions
 * @var array $reviewTypeOptions
 * @var array $ratingOptions
 */
?>
<?= $this->Html->meta('csrfToken', $this->request->getAttribute('csrfToken')) ?>
<style>

     .search-container {
        min-width: 300px;
    }

    .search-container .form-control {
        font-size: 14px;
        height: 42px;
        transition: all 0.3s ease;
    }

    .search-container .form-control:focus {
        border-color: #667eea !important;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .search-container .input-group-text {
        height: 42px;
        display: flex;
        align-items: center;
    }
    th.no-sort::after,
    th.no-sort::before {
        display: none !important;
    }

    th.no-sort {
        cursor: default !important;
        background-image: none !important;
    }

    td a {
        color: black;
    }

    .badge {
        font-size: 0.75em;
    }

    .review-comment {
        font-family: monospace;
        background: #f8f9fa;
        padding: 2px 6px;
        border-radius: 3px;
        font-weight: bold;
    }
    #filterForm {
        padding-left: 25px;
    }
</style>
<?php $this->append('style'); ?>
<?php $this->end(); ?>

<div class="main-content">
    <section class="section">
        <div class="section-body1">
            <div class="container-fluid">
                <?php
                $successMessage = $this->Flash->render('success');
                $errorMessage = $this->Flash->render('error');
                ?>
                <?php if (!empty($successMessage)): ?>
                    <?= $successMessage ?>
                <?php elseif (!empty($errorMessage)): ?>
                    <?= $errorMessage ?>
                <?php endif; ?>
            </div>
        </div>

        <div class="section-body" id="list">
            <div class="container-fluid">
                <div class="card card-primary">
                    <ul class="breadcrumb breadcrumb-style">
                        <li class="breadcrumb-item">Marketing</li>
                        <li class="breadcrumb-item">Reviews List</li>
                    </ul>


                    <div class="card-header">
                        <div class="d-block d-sm-flex actions">
                            <div class="action-header">
                                <h4>Reviews Management</h4>
                            </div>
                            <div class="filter-options  d-flex align-items-center">
                                 <div class="search-container">
                                    <div class="input-group">
                                        <input type="text"
                                               id="searchInput"
                                               class="form-control"
                                               placeholder="Search..."
                                               value="<?= h($search ?? '') ?>"
                                               style="border: 2px solid #e0e0e0; border-radius: 8px 0 0 8px; padding: 19px 12px!important ;">
                                        <div class="input-group-append">
                                            <span class="input-group-text" style="border: 2px solid #e0e0e0; border-left: none; border-radius: 0 8px 8px 0; background: #f8f9fa;">
                                                <i class="fas fa-search text-muted"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="action-button">
                                  
                                </div>
                                <div class="filter-button">
                                    <div class="btn-group">

                                        <button type="button" class="btn btn-primary filter-toggle">
                                            <i class="fas fa-filter"></i> Filter
                                        </button>

                                    </div>
                                </div>
                                <!-- <div class="download-icon">
                                    <button class="btn btn-primary" id="downloadReviews">
                                        <i class="fas fa-file-download"></i>
                                    </button>
                                </div> -->
                            </div>
                        </div>
                    </div>
                    <!-- Filters -->

                        <div id="filterForm" style="display: <?= empty($this->request->getQuery()) ? 'none' : 'block' ?>;">
                            <?= $this->Form->create(null, ['type' => 'get', 'class' => 'mb-4']) ?>
                            <div class="row">
                                <div class="col-md-3">
                                    <?= $this->Form->control('status', [
                                        'label' => 'Status',
                                        'type' => 'select',
                                        'options' => ['' => 'All'] + $statusOptions,
                                        'default' => $this->request->getQuery('status'),
                                        'class' => 'form-control'
                                    ]) ?>
                                </div>
                                <div class="col-md-3">
                                    <?= $this->Form->control('review_type', [
                                        'label' => 'Review Type',
                                        'type' => 'select',
                                        'options' => ['' => 'All'] + $reviewTypeOptions,
                                        'default' => $this->request->getQuery('review_type'),
                                        'class' => 'form-control'
                                    ]) ?>
                                </div>
                                <div class="col-md-3">
                                    <?= $this->Form->control('rating', [
                                        'label' => 'Rating',
                                        'type' => 'select',
                                        'options' => ['' => 'All'] + $ratingOptions,
                                        'default' => $this->request->getQuery('rating'),
                                        'class' => 'form-control'
                                    ]) ?>
                                </div>
                                <div class="col-md-3 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary me-2">Filter</button>
                                    <a href="<?= $this->Url->build(['action' => 'index']) ?>" class="btn btn-secondary">Reset</a>
                                </div>
                            </div>
                            <?= $this->Form->end() ?>
                        </div>
                    <!-- Filters -->
                    <div class="card-body">
                                <!-- Filters -->
                                <!-- <div class="row mb-3" id="filterForm">
                                    <?= $this->Form->create(null, ['type' => 'get', 'class' => 'row']) ?>
                                    <div class="col-md-3">
                                        <?= $this->Form->control('search', [
                                            'label' => false,
                                            'placeholder' => 'Search reviews...',
                                            'value' => $filters['search'],
                                            'class' => 'form-control'
                                        ]) ?>
                                    </div>
                                    <div class="col-md-2">
                                        <?= $this->Form->control('status', [
                                            'label' => false,
                                            'options' => ['' => 'All Status'] + $statusOptions,
                                            'value' => $filters['status'],
                                            'class' => 'form-control'
                                        ]) ?>
                                    </div>
                                    <div class="col-md-2">
                                        <?= $this->Form->control('review_type', [
                                            'label' => false,
                                            'options' => ['' => 'All Types'] + $reviewTypeOptions,
                                            'value' => $filters['review_type'],
                                            'class' => 'form-control'
                                        ]) ?>
                                    </div>
                                    <div class="col-md-2">
                                        <?= $this->Form->control('rating', [
                                            'label' => false,
                                            'options' => ['' => 'All Ratings'] + $ratingOptions,
                                            'value' => $filters['rating'],
                                            'class' => 'form-control'
                                        ]) ?>
                                    </div>
                                    <div class="col-md-3">
                                        <?= $this->Form->button('Filter', ['class' => 'btn btn-primary']) ?>
                                        <?= $this->Html->link('Clear', ['action' => 'index'], ['class' => 'btn btn-secondary ms-2']) ?>
                                    </div>
                                    <?= $this->Form->end() ?>
                                </div> -->

                        <div id="table-container">
                            <?= $this->element('Admin/Reviews/table_content', compact('reviews')) ?>
                        </div>

                        <div class="table-responsive" style="display: none;">
                            <table class="table table-striped dataTable no-footer display nowrap table-hover border" id="review-table">
                                <thead>
                                    <tr>
                                        <th class="no-sort"><?= __("Actions") ?></th>
                                        <th><?= __("ID") ?></th>
                                        <th><?= __("Customer ID") ?></th>
                                        <th><?= __("Type") ?></th>
                                        <th><?= __("Rating") ?></th>
                                        <th><?= __("Comment") ?></th>
                                        <th><?= __("Status") ?></th>
                                        <th><?= __("Created") ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($reviews as $review): ?>
                                    <tr>
                                        <td class="no-sort">

                                                <a href="<?= $this->Url->build(['action' => 'view', $review->id]) ?>"
                                                   class="" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?= $this->Url->build(['action' => 'edit', $review->id]) ?>"
                                                   class="" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>

                                            <a href="#"
                                            class="delete-review"
                                            data-id="<?= $review->id ?>"
                                            data-customer="<?= h($review->customer_id) ?>"
                                            title="Delete"
                                            role="button">
                                                <i class="fas fa-trash"></i>
                                            </a>

                                        </td>
                                        <td><?= $review->id ?></td>
                                        <td><?= $review->customer_id ?></td>
                                        <td>
                                            <span class="badge bg-<?= $review->review_type === 'course' ? 'primary' : ($review->review_type === 'teacher' ? 'success' : 'info') ?>">
                                                <?= ucfirst(h($review->review_type)) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="rating-display">
                                                <?php for($i = 1; $i <= 5; $i++): ?>
                                                    <?php if($i <= $review->rating): ?>
                                                        <i class="fas fa-star text-warning"></i>
                                                    <?php else: ?>
                                                        <i class="far fa-star text-muted"></i>
                                                    <?php endif; ?>
                                                <?php endfor; ?>
                                                <small class="text-muted">(<?= $review->rating ?>/5)</small>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($review->comment): ?>
                                                <?= $this->Text->truncate(h($review->comment), 50) ?>
                                            <?php else: ?>
                                                <span class="text-muted">No comment</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            $badgeClass = 'bg-warning';
                                            if ($review->status === 'Approved') $badgeClass = 'bg-success';
                                            if ($review->status === 'Rejected') $badgeClass = 'bg-danger';
                                            ?>
                                            <span class="badge <?= $badgeClass ?>">
                                                <?= h($review->status) ?>
                                            </span>
                                        </td>
                                        <td><?= $review->created_at->format('M d, Y') ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                       
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalTitle">Delete Confirmation</h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p class="icon"><i class="fas fa-exclamation-triangle danger"></i></p>
                    <p>Are you sure you want to delete review for customer <strong id="reviewCustomer"></strong>?</p>
                </div>
                <div class="modal-footer br">
                    <button type="button" class="btn btn-primary confirm-delete" id="confirmDeleteBtn">Delete</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $('#review-table').DataTable({
        "paging": false,
        "searching": false,
        "info": false,
        "ordering": true,
        "columnDefs": [
            { "orderable": false, "targets": 0 }
        ]
    });

    // Reset filters
    window.resetFilters = function() {
        window.location.href = '<?= $this->Url->build(['action' => 'index']) ?>';
    };

    // Search functionality with real-time search on keypress
    var searchTimeout;

    $('#searchInput').on('input keyup', function() {
        clearTimeout(searchTimeout);
        var searchTerm = $(this).val().trim();

        // Debounce search - wait 500ms after user stops typing
        searchTimeout = setTimeout(function() {
            performReviewSearch(searchTerm);
        }, 500);
    });

    // Clear search when input is completely empty
    $('#searchInput').on('keyup', function(e) {
        if (e.which === 8 || e.which === 46) { // Backspace or Delete
            var searchTerm = $(this).val().trim();
            if (searchTerm === '') {
                clearTimeout(searchTimeout);
                performReviewSearch('');
            }
        }
    });

    function performReviewSearch(searchTerm) {
        if (searchTerm === undefined) {
            searchTerm = $('#searchInput').val().trim();
        }

        loadReviewDataWithSearch(1, searchTerm);
    }

    function loadReviewDataWithSearch(page, searchTerm) {
        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Reviews', 'action' => 'index']) ?>',
            type: 'GET',
            data: {
                search: searchTerm,
                page: page,
                ajax: 1
            },
            beforeSend: function() {
                $('#table-container').html('<div class="text-center p-4"><i class="fas fa-spinner fa-spin"></i> Loading...</div>');
            },
            success: function(response) {
                $('#table-container').html(response);

                // Update URL without page reload
                var url = new URL(window.location.href);
                if (searchTerm) {
                    url.searchParams.set('search', searchTerm);
                } else {
                    url.searchParams.delete('search');
                }
                if (page > 1) {
                    url.searchParams.set('page', page);
                } else {
                    url.searchParams.delete('page');
                }
                window.history.replaceState({}, '', url.toString());
            },
            error: function() {
                console.error('Failed to load search results');
                $('#table-container').html('<div class="text-center p-4 text-danger">Failed to load results. Please try again.</div>');
            }
        });
    }

    // Load data function for pagination
    function loadReviewData(page = 1) {
        var searchTerm = $('#searchInput').val().trim();
        loadReviewDataWithSearch(page, searchTerm);
    }

    // Make loadReviewData available globally
    window.loadReviewData = loadReviewData;

    // Delete review
    $('.delete-review').click(function() {
        var reviewId = $(this).data('id');
        var reviewCustomer = $(this).data('customer');

        $('#reviewCustomer').text(reviewCustomer);
        $('#confirmDeleteBtn').data('id', reviewId);
        $('#deleteModal').modal('show');
    });

    $('#confirmDeleteBtn').click(function() {
        var reviewId = $(this).data('id');

        // Create a form and submit it
        var form = $('<form>', {
            'method': 'POST',
            'action': '<?= $this->Url->build(['action' => 'delete']) ?>/' + reviewId
        });

        var csrfToken = $('<input>', {
            'type': 'hidden',
            'name': '_csrfToken',
            'value': $('meta[name="csrfToken"]').attr('content')
        });

        form.append(csrfToken);
        $('body').append(form);
        form.submit();

        $('#deleteModal').modal('hide');
    });

    // Download export with current filters
    $('#downloadReviews').click(function() {
        var exportUrl = '<?= $this->Url->build(['action' => 'export']) ?>';
        var currentParams = window.location.search;

        if (currentParams) {
            exportUrl += currentParams;
        }

        window.open(exportUrl, '_blank');
    });

   $('.filter-toggle').click(function () {
    $('#filterForm').slideToggle();
});
});





</script>
<?php $this->end(); ?>
