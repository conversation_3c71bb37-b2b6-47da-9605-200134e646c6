<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\MasterData> $masters
 * @var array $pagination
 */

// Debug: Add a unique identifier to track if this element is rendered multiple times
$elementId = 'table-' . uniqid();
?>

<div class="table-responsive" data-element-id="<?= $elementId ?>">
    <table class="table table-striped dataTable no-footer display nowrap table-hover border" id="master-data-table">
        <thead>
            <tr>
                <th class="no-sort"><?= __("Actions") ?></th>
                <th class="sortable" data-sort="id">
                    <?= __("ID") ?>
                    <?php if (isset($sort) && $sort === 'id'): ?>
                        <i class="fas fa-sort-<?= strtolower($direction) === 'asc' ? 'up' : 'down' ?>"></i>
                    <?php else: ?>
                        <i class="fas fa-sort"></i>
                    <?php endif; ?>
                </th>
                <th class="sortable" data-sort="type">
                    <?= __("Type") ?>
                    <?php if (isset($sort) && $sort === 'type'): ?>
                        <i class="fas fa-sort-<?= strtolower($direction) === 'asc' ? 'up' : 'down' ?>"></i>
                    <?php else: ?>
                        <i class="fas fa-sort"></i>
                    <?php endif; ?>
                </th>
                <th class="sortable" data-sort="title">
                    <?= __("Title") ?>
                    <?php if (isset($sort) && $sort === 'title'): ?>
                        <i class="fas fa-sort-<?= strtolower($direction) === 'asc' ? 'up' : 'down' ?>"></i>
                    <?php else: ?>
                        <i class="fas fa-sort"></i>
                    <?php endif; ?>
                </th>
                <th class="sortable" data-sort="description">
                    <?= __("Description") ?>
                    <?php if (isset($sort) && $sort === 'description'): ?>
                        <i class="fas fa-sort-<?= strtolower($direction) === 'asc' ? 'up' : 'down' ?>"></i>
                    <?php else: ?>
                        <i class="fas fa-sort"></i>
                    <?php endif; ?>
                </th>
                <th class="sortable" data-sort="status">
                    <?= __("Status") ?>
                    <?php if (isset($sort) && $sort === 'status'): ?>
                        <i class="fas fa-sort-<?= strtolower($direction) === 'asc' ? 'up' : 'down' ?>"></i>
                    <?php else: ?>
                        <i class="fas fa-sort"></i>
                    <?php endif; ?>
                </th>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($masters)): ?>
                <?php foreach ($masters as $master): ?>
                    <tr>
                        <td class="no-sort">
                            <!-- View -->
                            <a href="<?= $this->Url->build(['controller' => 'MasterData', 'action' => 'view', $master->id]) ?>"
                               class="" title="View">
                                <i class="fas fa-eye"></i>
                            </a>
                            <!-- Edit -->
                            <a href="<?= $this->Url->build(['controller' => 'MasterData', 'action' => 'edit', $master->id]) ?>"
                               class="" title="Edit">
                                <i class="fas fa-edit"></i>
                            </a>
                            <!-- Delete -->
                            <a href="#"
                               class="delete-master"
                               data-id="<?= $master->id ?>"
                               data-title="<?= h($master->title) ?>"
                               title="Delete"
                               role="button">
                                <i class="fas fa-trash"></i>
                            </a>
                        </td>
                        <td><?= $master->id ?></td>
                        <td>
                            <?= $master->type ?>
                            <!-- <?php
                            $typeLabels = [
                                'yoga_style' => 'Yoga Style',
                                'special_need' => 'Special Need',
                                'accommodation' => 'Accommodation',
                                'food' => 'Food',
                                'technique' => 'Technique',
                                'addon' => 'Addon',
                                'level' => 'Level',
                                'location_type' => 'Location Type',
                                'climate' => 'Climate'
                            ];
                            $typeLabel = $typeLabels[$master->type] ?? 'Unknown';
                            $badgeColor = 'bg-primary';
                            if (in_array($master->type, ['yoga_style', 'technique'])) $badgeColor = 'bg-success';
                            if (in_array($master->type, ['special_need', 'accommodation'])) $badgeColor = 'bg-info';
                            if (in_array($master->type, ['food', 'addon'])) $badgeColor = 'bg-warning';
                            ?>
                            <span class="badge <?= $badgeColor ?>">
                                <?= h($typeLabel) ?>
                            </span> -->
                        </td>
                        <td>
                            <?= h($master->title) ?>
                            <?php if ($master->description && strlen($master->description) > 50): ?>
                                <br><small class="text-muted"><?= $this->Text->truncate(h($master->description), 50) ?></small>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($master->description): ?>
                                <?= $this->Text->truncate(h($master->description), 100) ?>
                            <?php else: ?>
                                <span class="text-muted">No description</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php
                            $badgeClass = 'bg-warning';
                            if ($master->status === 'A') $badgeClass = 'bg-success';
                            if ($master->status === 'I') $badgeClass = 'bg-secondary';
                            if ($master->status === 'D') $badgeClass = 'bg-danger';

                            $statusLabel = 'Unknown';
                            if ($master->status === 'A') $statusLabel = 'Active';
                            if ($master->status === 'I') $statusLabel = 'Inactive';
                            if ($master->status === 'D') $statusLabel = 'Deleted';
                            ?>
                            <span class="badge <?= $badgeClass ?>">
                                <?= h($statusLabel) ?>
                            </span>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr>
                    <td colspan="6" class="text-center">No master data found.</td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<!-- Pagination -->
<?php
// Set default pagination if not provided
if (!isset($pagination)) {
    $pagination = [
        'total_items' => count($masters ?? []),
        'total_pages' => 1,
        'current_page' => 1,
        'limit' => 10,
        'has_more' => false
    ];
}
?>

<!-- Pagination -->
<div class="d-flex justify-content-between align-items-center mt-3">
    <div>
        <?php
        $start = (($pagination['current_page'] - 1) * $pagination['limit']) + 1;
        $end = min($pagination['current_page'] * $pagination['limit'], $pagination['total_items']);
        ?>
        Showing
         <!-- <?= $start ?>  to-->
          <?= $end ?> of <?= $pagination['total_items'] ?> entries
    </div>
    <?php if ($pagination['total_pages'] > 1): ?>
    <div>
        <ul class="pagination">
            <!-- First -->
            <!-- <?php if ($pagination['current_page'] > 1): ?>
                <li class="page-item">
                    <a href="#" class="page-link pagination-link" data-page="1"><< first</a>
                </li>
            <?php endif; ?> -->

            <!-- Previous -->
            <!-- <?php if ($pagination['current_page'] > 1): ?>
                <li class="page-item">
                    <a href="#" class="page-link pagination-link" data-page="<?= $pagination['current_page'] - 1 ?>">< previous</a>
                </li>
            <?php endif; ?> -->

            <!-- Page Numbers -->
            <?php
            $currentPage = $pagination['current_page'];
            $totalPages = $pagination['total_pages'];
            $maxPages = 5;
            $start = max(1, $currentPage - 2);
            $end = min($totalPages, $start + $maxPages - 1);

            if ($end - $start + 1 < $maxPages) {
                $start = max(1, $end - $maxPages + 1);
            }

            for ($i = $start; $i <= $end; $i++): ?>
                <li class="page-item <?= $i == $currentPage ? 'active' : '' ?>">
                    <a href="#" class="page-link pagination-link" data-page="<?= $i ?>"><?= $i ?></a>
                </li>
            <?php endfor; ?>

            <!-- Next -->
            <!-- <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                <li class="page-item">
                    <a href="#" class="page-link pagination-link" data-page="<?= $pagination['current_page'] + 1 ?>">next ></a>
                </li>
            <?php endif; ?> -->

            <!-- Last -->
            <!-- <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                <li class="page-item">
                    <a href="#" class="page-link pagination-link" data-page="<?= $pagination['total_pages'] ?>">last >></a>
                </li>
            <?php endif; ?> -->
        </ul>
    </div>
    <?php endif; ?>
</div>
