<?php $this->assign('title', 'Yoga in India - Find the Best Places to Learn Yoga in India');
$this->assign('meta_desc', 'Experience Yoga in India, the birthplace of Yoga. Learn about different types of yoga and find the best Yoga centers and Ashrams in India.');
?>
<link rel="stylesheet" href="<?= $this->Url->webroot('css/card.css') ?>">

<section class="search-container">
    <div class="px-6 pt-4 pb-5 md:px-10 lg:px-25 xl:px-40">
        <h2>Looking for a</h2>
        <form x-data="{
            location: '', 
            type: '', 
            style: '', 
            stype: '',
            specialNeeds:'',
            actionUrl() {
            return this.stype === 'center' 
                ? '<?=  $this->Url->build([
                        'lang' => $this->request->getParam('lang'),
                        'controller' => 'Partners',
                        'action' => 'index',
                        'country' => $this->request->getParam('country') ?? 'india'
                    ]) ?>' 
                : '<?=  $this->Url->build([
                        'lang' => $this->request->getParam('lang'),
                        'controller' => 'Courses',
                        'action' => 'index',
                        'country' => $this->request->getParam('country') ?? 'india'
                    ]) ?>';
            }
            }" @submit.prevent="$el.submit()" method="get" :action="actionUrl()">
            <div class="flex items-center search-wrapper">
                <div class="search-location">
                    <select class="relative" name="stype" x-model="stype">
                        <option class="text-black py-2 dropdown active:text-black focus:text-black" value="course">Yoga
                            Course</option>
                        <option class="text-black py-2 dropdown active:text-black focus:text-black" value="class">Yoga
                            Class</option>
                        <option class="text-black py-2 dropdown active:text-black focus:text-black" value="center">Yoga
                            Center</option>
                        <option class="text-black py-2 dropdown active:text-black focus:text-black" value="teacher">Yoga
                            Teacher</option>
                    </select>
                </div>
                <div class="search-style flex items-center">
                    <div x-data="{toggle: false}" class="flex items-center">
                        <div id="toggleOff" x-show="!toggle" class="w-[auto] md:w-[460px] lg:w-[460px] xl:w-[460px]">
                            <select class="relative dropdown-container" name="style" x-model="style">
                                <option value="">Yoga Style</option>
                                <?php foreach ($yoga_styles as $style): ?>
                                    <option class="block px-4 py-2 text-gray-700 hover:bg-gray-200" <?= h($style) ?>>
                                        <?= h($style); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div id="toggleOn" x-show="toggle" class="w-[auto] md:w-[460px] lg:w-[460px] xl:w-[460px]">
                            <select class="relative dropdown-container special-need" name="specialNeeds"
                                x-model="specialNeeds">
                                <option class="dropdown" value="">Special Need</option>
                                <?php foreach ($special_needs as $special): ?>
                                    <option class="block px-4 py-2 text-gray-700 hover:bg-gray-200" <?= h($special) ?>>
                                        <?= h($special); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <!-- <div id="toggleOn" x-show="toggle">
                        <select class="relative dropdown-container special-need" name="specialNeeds" x-model="specialNeeds">
                                <option class="dropdown" value="">Special Need</option>
                                <?php foreach ($special_needs as $special): ?>
                                    <option class="block px-4 py-2 text-gray-700 hover:bg-gray-200" <?= h($special) ?>><?= h($special); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div> -->
                        <div class="flex rounded-sm btn-toggle">
                            <label for="toggle" class="flex items-center w-full cursor-pointer">
                                <span class="ms-3 text-sm font-medium text-[#666666]"
                                    x-text="toggle ? 'Special Need' : 'Special Need'"></span>
                                <input type="checkbox" id="toggle" name="toggle" x-model="toggle" value=""
                                    class="sr-only peer">
                                <div
                                    class="relative w-10 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-[#A3C4A9]-300 dark:peer-focus:ring-[#A3C4A9] rounded-full peer dark:bg-gray-600 peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-500 peer-checked:bg-[#A3C4A9]">
                                </div>
                            </label>

                            <!-- <div class="flex rounded-sm btn-toggle">
                                <label for="toggle" class="flex items-center w-full cursor-pointer">
                                    <span :class="toggle ? 'ms-[30px] mr-[0px]' : 'ms-[10px] mr-[0px]'" class="text-sm font-medium text-[#666666]"
                                        x-text="toggle ? 'Yoga Style' : 'Special Need'">
                                    </span>
                                    <input type="checkbox" id="toggle" name="toggle" x-model="toggle" value=""
                                        class="sr-only peer">
                                    <div
                                        class="relative w-10 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-[#A3C4A9]-300 dark:peer-focus:ring-[#A3C4A9] rounded-full peer dark:bg-gray-600 peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-500 peer-checked:bg-[#A3C4A9]">
                                    </div>
                                </label>
                            </div> -->

                        </div>
                    </div>
                </div>
                <div class="search-place">
                    <input name="location" type="text" x-model="location" class="search"
                        placeholder="Where? Goa, Kerala, Mysore...">
                </div>
                <div class="search-all">
                    <!-- <button type="submit" class="btn-search"><i class="fas fa-search"></i><span>Search</span></button> -->
                    <button type="submit" class="btn-search"><img src="<?= $this->Url->webroot('img/search-btn.png') ?>"
                            class="search-btn" alt="search" /><span>Search</span></button>
                </div>
            </div>
    </div>
    <?= $this->Form->end() ?>
</section>
<?php $featured_courses_mobile = []; ?>
<section class="featured-yoga py-5 px-6 md:py-5 lg:py-5 xl:py-5 2xl:py-5 px-6 md:px-10 lg:px-25 xl:px-40">
    <img src="<?= $this->Url->webroot('img/flower.png') ?>" class="footer-flower" alt="flower" />
    <h2>Featured Yoga Courses in India</h2>
    <p class="mb-4">Believed to have its deepest roots in Yoga about 5000 years ago, India has its pristine presence and
        glory as the land of Yoga. Yoga has not only helped its aspirant.</p>
    <div class="mx-auto featured-yoga-center">
        <!-- <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 desktop-view"> -->
        <div x-data="courseSlider()" x-init="init()" class="relative w-full desktop-view feature-yoga-home">
            <!-- Prev Button -->
            <button @click="prev" :disabled="current === 0" class="absolute start-[-50px] top-[50%] z-10 bg-white px-2 py-1 disabled:opacity-50 disabled:cursor-not-allowed prev">
                <img src="<?= $this->Url->webroot('img/arrow-back-black.png') ?>" alt="Arrow Back">
            </button>
            <!-- Next Button -->
            <button @click="next" :disabled="current >= maxIndex" class="absolute right-[-50px] top-[50%] z-10 bg-white px-2 py-1 disabled:opacity-50 disabled:cursor-not-allowed next">
                <img src="<?= $this->Url->webroot('img/arrow-next-black.png') ?>" alt="Arrow Next">
            </button>
            <div class=" overflow-hidden">
                <div class="flex transition-transform duration-500 ease-linear space-x-4 w-[92%] relative start-[20px]" :style="`transform: translateX(-${current * (100 / itemsPerView)}%);`">
                    <?php if (!empty($featured_courses)) {
                        foreach ($featured_courses as $course) {
                            $course_date = '';
                            if (count($course->course_batches) > 0 && isset($course->course_batches[0])):
                                $course_date = $course->course_batches[0]->start_date->format('d-m-Y');
                                if(count($course->course_batches) > 1){
                                $course_date .= ' <small>+' . (count($course->course_batches) - 1) . ' more</small>';
                                }
                            endif;

                            $modeparts = '';

                            if (!empty($course->modalities) && is_array($course->modalities)) {
                                $names = array_column($course->modalities, 'name');
                                $modeNames = [];

                                // Online types
                                if (in_array('Online Live', $names)) {
                                    $modeNames[] = 'Online - Live';
                                }
                                if (in_array('Online VOD', $names)) {
                                    $modeNames[] = 'Online - VOD';
                                }
                                if (in_array('Hybrid', $names)) {
                                    $modeNames[] = 'Hybrid';
                                }

                                // On-Site
                                $onSite = in_array('On Site', $names);
                                if ($onSite) {
                                    $location = $course->city->name ?? '';
                                    $modeNames[] = !empty($location) ? h($location) : 'On-Site';
                                }

                                $count = count($modeNames);

                                // Determine icon:
                                if ($count > 1) {
                                    $icon = "<img src=".$this->Url->webroot('img/home_work.png') ."></img>";
                                } elseif ($onSite) {
                                    $icon = "<i class='fas fa-map-marker-alt'></i>";
                                } else {
                                    $icon = "<i class='fas fa-laptop-code'></i>";
                                }

                                if (!empty($modeNames)) {
                                    $modeparts = $icon . ' ' . implode(' | ', $modeNames);
                                }
                            }

                            $featured_courses_mobile[] = [
                                'imgSrc' => $course->image_url,
                                'imgAlt' => !empty($course->partner) ? $course->partner->name : '',
                                'info' => !empty($course->partner) ? '@ ' . $course->partner->name : '',
                                'title' => h($course->name),
                                'desc' => h($course->short_description),
                                'date' => $course_date,
                                'mode' => $modeparts ? $modeparts : "<i class='fas fa-laptop-code'></i>",
                                'lang' => $course->language,
                                'slug' => urlencode($course->slug),
                                'country' => $course->country->name,
                                'state'  => $course->state->name,
                                'city' => $course->city->name,
                                'duration' => $course->duration_details
                            ];
                        
                            $parts = [];
                            $base = $this->Url->build('/');
                            $lang = h($this->request->getParam('lang'));
                            $parts[] = "{$base}{$lang}/yoga-courses";

                            if (!empty($course->country)) {
                                $parts[] = h($course->country->name);
                            }

                            if (!empty($course->state->region)) {
                                $parts[] = h($course->state->region->name);
                            }

                            if (!empty($course->state)) {
                            //   $parts[] = 'region';
                                $parts[] = h($course->state->name);
                            }

                            if (!empty($course->city)) {
                                $parts[] = h($course->city->name);
                            }

                            if (!empty($course->slug)) {
                                $parts[] =h($course->slug);
                            }

                            $url = implode('/', array_map(function($part) {
                                return strtolower(str_replace(' ', '-', $part));
                            }, $parts));
                        ?>

                            <a href="<?= $url ?>" class="flex-none w-full sm:w-1/2 lg:w-1/4 slide">
                                <!-- Card 1 -->
                                <div class="bg-white rounded-lg card-container flex flex-col min-h-[450px] h-[600px]"
                                    id="<?= $course->id; ?>">
                                    <img src="<?= $course->image_url; ?>" alt="Yoga teacher training in india"
                                        class="w-full h-56 object-cover yoga-img">
                                    <div class="card-body">
                                        <p class="info line-clamp-2"><?= !empty($course->partner) ? '@ ' . $course->partner->name : '' ?></p>
                                        <h3 class="yoga-name">
                                            <span class="line-clamp-2">
                                            <?= h($course->name) ?></span>
                                            <span class="rating-wrapper">
                                                <span class="rating">4.5</span>
                                                <i class="fas fa-star"></i>
                                            </span>
                                        </h3>

                                        <p class="text-gray-600 line-clamp-4 yoga-description"><?= h($course->short_description) ?></p>

                                        <p class="time"><i class="fas fa-calendar-alt"></i>
                                            <span><?= $course_date ?></span>
                                        </p>

                                        <p class="mode"><?= $modeparts ?  $modeparts : "<i class='fas fa-laptop-code'></i>"; ?></p>
                                        <p class="lang"><i class="fas fa-globe"></i> <?= $course->language ?></p>
                                    </div>
                                </div>
                            </a>
                        <?php }
                    } ?>
                </div>
            </div>
        </div>
        <div x-data="{  
            autoplayIntervalTime: 4000,          
            slides: <?= htmlspecialchars(json_encode($featured_courses_mobile, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE), ENT_QUOTES, 'UTF-8'); ?>,           
            currentSlideIndex: 1,
            isPaused: false,
            autoplayInterval: null,

            startX: 0,
            endX: 0,
            handleSwipeStart(event) {
                this.startX = event.type.includes('mouse') ? event.clientX : event.touches[0].clientX;
            },
            handleSwipeEnd(event) {
                this.endX = event.type.includes('mouse') ? event.clientX : event.changedTouches[0].clientX;
                const diff = this.startX - this.endX;

                if (Math.abs(diff) > 50) {
                    if (diff > 0) {
                        this.next();
                    } else {
                        this.previous();
                    }
                }
            },

            previous() {                
                if (this.currentSlideIndex > 1) {                    
                    this.currentSlideIndex = this.currentSlideIndex - 1                
                } else {           
                    this.currentSlideIndex = this.slides.length                
                }            
            },            
            next() {                
                if (this.currentSlideIndex < this.slides.length) {                    
                    this.currentSlideIndex = this.currentSlideIndex + 1                
                } else {                   
                    this.currentSlideIndex = 1                
                }            
            },  
            autoplay() {
                this.autoplayInterval = setInterval(() => {
                    if (! this.isPaused) {
                        this.next()
                    }
                }, this.autoplayIntervalTime)
            },
            setAutoplayInterval(newIntervalTime) {
                clearInterval(this.autoplayInterval)
                this.autoplayIntervalTime = newIntervalTime
                this.autoplay()
            },
            generateSeoLink(item) {
                const parts = [baseUrl + lang + '/yoga-courses'];

                if (item.country) {
                    parts.push(item.country.replace(/\s+/g, '-').toLowerCase());
                }
            
                if (item.state) {
                    parts.push('region', item.state.replace(/\s+/g, '-').toLowerCase());
                }

                if (item.city) {
                    parts.push(item.city.replace(/\s+/g, '-').toLowerCase());
                }

                if (item.slug) {
                    parts.push(item.slug);
                }

                return parts.join('/');
            }
        }" x-init="" class="relative w-full featured-slider mobile-view">

            <!-- SLIDES -->
            <div class="card-container relative w-full mb-[20px] min-h-[634px] overflow-hidden"
                @touchstart="handleSwipeStart" @touchend="handleSwipeEnd" @mousedown="handleSwipeStart"
                @mouseup="handleSwipeEnd">
                <div class="flex transition-transform duration-500 ease-linear" :style="`transform: translateX(-${(currentSlideIndex - 1) * 100}%);`">
                    <template x-for="(slide, index) in slides" :key="index">
                            <!-- for clickable -->
                            <a :href="generateSeoLink(slide)" class="link w-full flex-shrink-0">
                                <div class="course-card bg-white rounded-lg h-auto">
                                    <img class="w-full h-56 object-cover rounded-md yoga-img" x-bind:src="slide.imgSrc"
                                        x-bind:alt="slide.imgAlt">
                                    <div class="card-body">
                                        <p class="info line-clamp-2 h-[40px]" x-text="slide.info"></p>
                                        <h3 class="text-xl font-semibold yoga-name line-clamp-2 h-[68px]">
                                            <span x-html="slide.title" class="line-clamp-2"></span>
                                            <span class='rating-wrapper'><span class='rating'>4.5</span> <i class='fas fa-star'></i></span>
                                        </h3>
                                        <p class="text-gray-600 mt-2 line-clamp-4 yoga-description line-clamp-4 h-[72px]" x-text="slide.desc"></p>
                                        <p class="time" x-html="`<i class='fas fa-calendar-alt'></i> `+slide.date"></p>
                                        <p class="mode" x-html="slide.mode"></p>
                                        <p class="lang" x-html="`<i class='fas fa-globe'></i> `+slide.lang"></p>
                                    </div>
                                </div>
                            <!-- </div> -->
                        </a>
                    </template>
                </div>
            </div>

            <!-- indicators -->
            <!-- <div class="indicator absolute rounded-radius bottom-3 md:bottom-5 left-1/2 z-20 flex -translate-x-1/2 gap-4 md:gap-3 bg-transparent px-1.5 py-1 md:px-2" role="group" aria-label="slides" >
                <template x-for="(slide, index) in slides">
                    <button class="size-3 rounded-full transition bg-on-surface dark:bg-on-surface-dark" x-on:click="currentSlideIndex = index + 1" x-bind:class="[currentSlideIndex === index + 1 ? 'bg-[#D87A61]' : 'bg-[#D87A61]/40']" x-bind:aria-label="'slide ' + (index + 1)"></button>
                </template>
            </div> -->
            <button type="button" @click="previous()" :disabled="currentSlideIndex === 1" class="absolute top-[36%] start-[10px] end-[0px] z-30 flex items-center justify-center px-2 py-1 cursor-pointer group focus:outline-none disabled:opacity-50 prev">
                <img src="<?= $this->Url->webroot('img/arrow-back-black.png') ?>" alt="Arrow Back">
            </button>
            <button type="button" @click="next()" :disabled="currentSlideIndex === slides.length" class="absolute top-[36%] end-[10px] z-30 flex items-center justify-center px-2 py-1 cursor-pointer group focus:outline-none disabled:opacity-50 next">
                <img src="<?= $this->Url->webroot('img/arrow-next-black.png') ?>" alt="Arrow Next">
            </button>
        </div>
        <div class="courses-view text-center">
            <button class="mt-4 text-[#D87A61] px-4 py-2" x-data
                :data-url="'<?= $this->Url->build([
                        'lang' => $this->request->getParam('lang'),
                        'controller' => 'Courses',
                        'action' => 'index',
                        'country' => $this->request->getParam('country') ?? 'india'
                    ]) ?>'"
                @click="window.location.href = $el.dataset.url">View All</a>
        </div>
    </div>
</section>

<?php if (!empty($imported['destinations'])): ?>
    <section class="yoga-destination py-5 px-6 md:px-10 lg:px-25 xl:px-40 relative">
        <div class="mx-auto relative">
            <h2>Top Yoga Destinations</h2>
            <!-- Slider controls -->
            <button type="button"
                class="absolute top-[50px] start-auto end-[190px] z-30 flex items-center justify-center px-4 cursor-pointer group focus:outline-none prev"
                data-carousel-prev onclick="popularPrev();">
                <img src="<?= $this->Url->webroot('img/arrow-back-black.png') ?>" alt="Arrow Back">
            </button>
            <button type="button"
                class="absolute top-[50px] end-[140px] z-30 flex items-center justify-center px-4 cursor-pointer group focus:outline-none next"
                data-carousel-next onclick="popularNext();">
                <img src="<?= $this->Url->webroot('img/arrow-next-black.png') ?>" alt="Arrow Next">
            </button>
            <div id="default-carousel2" class="relative w-full overflow-hidden lg:h-[468px]" data-carousel="slide">
                <!-- Carousel wrapper -->
                <div id="popular-slider" class="relative rounded-lg flex items-center w-full">
                    <?php
                    $i = 1;
                    foreach ($imported['destinations'] as $destination):
                        if ($i == 1) {
                            $crousel_item = 'data-carousel-item="active"';
                        } else {
                            $crousel_item = 'data-carousel-item';
                        }
                        ?>
                        <!-- Item 1 -->
                        <div class="duration-700 ease-in-out popular-carousel mr-[0px] md:mr-[30px] lg:mr-[30px] xl:mr-[30px]"
                            <?= $crousel_item ?>>
                            <!-- Card 1 -->
                            <div class="bg-transparent">
                                <a href="<?= $destination['url'] ?>" target="_blank">
                                    <div class="card-img relative">
                                        <img src="<?= $destination['image'] ?>" alt="<?= $destination['title'] ?>"
                                            class="w-full h-87 object-cover">
                                        <div class="overlay"><?= $destination['title'] ?></div>
                                    </div>

                                </a>
                                <div class="card-body">
                                    <p class="time">Upgrade your yoga and wellness knowledge by visiting and which cover a wide range of holistic health</p>
                                    <button class="text-[#D87A61] px-4 py-2" x-data :data-url="'<?= $destination['url'] ?>'"
                                        @click="window.location.href = $el.dataset.url" target="_blank">Read More</button>
                                </div>
                            </div>
                        </div>
                        <?php $i++;
                    endforeach; ?>
                </div>
                <!-- Slider indicators -->
                <div class="relative z-30 flex w-100 justify-center rtl:space-x-reverse slide-buttons">
                    <?php
                    $k = 1;
                    foreach ($imported['destinations'] as $key => $destination) { ?>
                        <button type="button" class="w-3 h-3 rounded-full mx-2" aria-current="true" aria-label="Slide <?= $k ?>"
                            data-carousel-slide-to="<?= $key ?>"></button>
                        <button type="button" class="w-3 h-3 rounded-full mx-2" aria-current="false"
                            aria-label="Slide <?= $k ?>" data-carousel-slide-to="<?= $key ?>"></button>
                        <button type="button" class="w-3 h-3 rounded-full mx-2" aria-current="false"
                            aria-label="Slide <?= $k ?>" data-carousel-slide-to="<?= $key ?>"></button>
                        <button type="button" class="w-3 h-3 rounded-full mx-2" aria-current="false"
                            aria-label="Slide <?= $k ?>" data-carousel-slide-to="<?= $key ?>"></button>
                        <button type="button" class="w-3 h-3 rounded-full mx-2" aria-current="false"
                            aria-label="Slide <?= $k ?>" data-carousel-slide-to="<?= $key ?>"></button>
                    <?php } ?>
                </div>
            </div>
            <div class="destination-view text-center">
                <button class="mt-4 text-[#D87A61] px-4 py-2" x-data :data-url="'<?= $configSettings['WP_DESTINATIONS'] ?>'"
                    @click="window.location.href = $el.dataset.url">View All</button>
            </div>
        </div>
    </section>
<?php endif; ?>

<section class="featured-yoga featured-yoga-center py-5 px-6 md:px-10 lg:px-25 xl:px-40 relative">
    <img src="<?= $this->Url->webroot('img/flower.png') ?>" class="footer-flower" alt="flower" />
    <h2>Popular Yoga Centers and Ashrams</h2>
    <div class="mx-auto">
        <!-- Slider Controls -->
        <button type="button" id="featured-prev" class="absolute top-[41%] md:top-[50%] start-[30px] md:start-[105px] z-30 px-2 py-1 cursor-pointer group focus:outline-none prev" onclick="featuredPrev();">
            <img src="<?= $this->Url->webroot('img/arrow-back-black.png') ?>" alt="Arrow Back">
        </button>
        <button type="button" id="featured-next" class="absolute top-[41%] md:top-[50%] end-[30px] md:end-[100px] z-30 px-2 py-1 cursor-pointer group focus:outline-none next" onclick="featuredNext();">
            <img src="<?= $this->Url->webroot('img/arrow-next-black.png') ?>" alt="Arrow Next" class="relative">
        </button>

        <div id="featured-carousel" class="relative w-full overflow-hidden">
            <div id="featured-slider" class="relative rounded-lg flex items-center w-full">
                <?php foreach ($featured_partners as $partner): 
                $base = $this->Url->build('/');
                $lang = h($this->request->getParam('lang'));
                $seoUrlParts = [
                    $base . $lang . '/yoga-courses'
                ];
                if (!empty($partner['country'])) {
                    $seoUrlParts[] = strtolower(str_replace(' ', '-', $partner['country']));
                }
                if (!empty($partner['state'])) {
                    $seoUrlParts[] = 'region/' . strtolower(str_replace(' ', '-', $partner['state']));
                }
                if (!empty($partner['city'])) {
                    $seoUrlParts[] = strtolower(str_replace(' ', '-', $partner['city']));
                }
                if (!empty($partner['slug'])) {
                    $seoUrlParts[] = $partner['slug'];
                }
                $seoUrl = implode('/', $seoUrlParts);
                ?>
                <div class="duration-700 ease-in-out featured-center-carousel mr-[0px] md:mr-[30px] lg:mr-[30px] xl:mr-[30px]">
                    <div class="bg-white rounded-lg course-card card-container flex flex-col min-h-[450px] h-[600px]">
                        <a href="<?= h($seoUrl) ?>">
                        <img src="<?= h($partner['imgSrc']) ?>" alt="<?= h($partner['imgAlt']) ?>" class="w-full h-56 object-cover yoga-img">
                        <div class="card-body">
                        
                            <p class="info line-clamp-2"><i class='fas fa-map-marker-alt'></i> <?= h($partner['info']) ?></p>
                            <h3 class="yoga-name">
                                <span class="line-clamp-2"><?= h($partner['title']) ?></span>
                                <span class="rating-wrapper">
                                    <span class="rating">4.5</span>
                                    <i class="fas fa-star"></i>
                                </span>
                            </h3>
                            <p class="text-gray-600 line-clamp-4 yoga-description"><?= h($partner['desc']) ?></p>
                            <p class="time"><i class='fas fa-calendar-alt'></i> <?= h($partner['is_open']) ?></p>
                            <p class="text-gray-600 mode line-clamp-1">
                                <img src='<?= $this->Url->webroot('img/yoga-class.png') ?>' alt='Yoga class'/>
                                <?= h($partner['styles']) ?>
                            </p>
                            <p class="text-gray-600 lang">
                                <i class='fas fa-globe'></i> <?= h($partner['lang']) ?>
                            </p>
                        </div>
                        </a>
                    </div> 
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <div class="courses-view text-center">
            <!-- <button class="view text-[#D87A61] px-4 py-2 mt-" x-data
                :data-url="'<?= $this->Url->build([
                        'lang' => $this->request->getParam('lang'),
                        'controller' => 'Partners',
                        'action' => 'index',
                        'country' => $this->request->getParam('country') ?? 'india'
                    ]) ?>'"
                @click="window.location.href = $el.dataset.url">View All</button> -->
            <button class="mt-4 text-[#D87A61] px-4 py-2" x-data :data-url="'<?= $this->Url->build(['lang' => $this->request->getParam('lang'), 'controller' => 'Partners', 'action' => 'index']) ?>'" @click="window.location.href = $el.dataset.url">View All</button>
        </div>
    </div>
</section>

<section class="featured-teachers px-6 py-5 md:px-10 lg:px-25 xl:px-40">
    <!-- <div x-data="carouselTeachers()" x-init="startAutoplay()" class="relative w-full overflow-hidden desktop-view">
        <h2>Featured Teachers</h2>
        <!-- Previous Button -->
        <!-- <button @click="prev()" class="btn left">‹</button> -->
        <!-- Carousel -->
        <!-- <div x-data="{
            index: 0,
            itemsToShow: 5,
            totalItems: <?= count($featured_teachers) ?>,
            images:<?= htmlspecialchars(json_encode($featured_teachers, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE), ENT_QUOTES, 'UTF-8'); ?>,
            next() {
                if (this.index < this.totalItems - this.itemsToShow) {
                    this.index++;
                }
            },
            prev() {
                if (this.index > 0) {
                    this.index--;
                }
            },
            goTo(i) {
                this.index = i * this.itemsToShow;
            },
            getTransform() {
                return `translateX(-${this.index * 100 / this.itemsToShow}%)`;
            }
        }" class="relative w-full overflow-hidden desktop-view"> -->


        <!--<div x-data="{
        index: 0,
        itemsToShow: 5,
        totalItems: <?= count($featured_teachers) ?>,
        images:<?= htmlspecialchars(json_encode($featured_teachers, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE), ENT_QUOTES, 'UTF-8'); ?>,
        interval: null,
        next() {
            if (this.index < this.totalItems - this.itemsToShow) {
                this.index++;
            } else {
                this.index = 0; // loop back to start
            }
        },
        prev() {
            if (this.index > 0) {
                this.index--;
            }
        },
        goTo(i) {
            this.index = i * this.itemsToShow;
        },
        getTransform() {
            return `translateX(-${this.index * 100 / this.itemsToShow}%)`;
        }
    }" x-init="interval = setInterval(() => next(), 3000)" @mouseenter="clearInterval(interval)"
            @mouseleave="interval = setInterval(() => next(), 3000)"
            class="relative w-full overflow-hidden desktop-view">
            <div class="flex transition-transform duration-500 ease-in-out feature-carousel-container"
                :style="{'transform': getTransform()}">
                <template x-for="(item, i) in images" :key="i">
                    <div class=" text-center">
                        <a :href="generateSeoLink(item)">
                            <img :src="item.src" class="w-[200px] h-[200px] rounded-lg round-image" />
                        </a>
                        <p class="mt-2 text-[#283148] teacher-name" x-text="item.name"></p>
                        <p class="mt-2 text-[#666666] profession" x-text="item.caption"></p>
                        <a class="mt-2 btn-view text-[#D87A61]">Read More</a>
                    </div>
                </template>
            </div>
            <!-- Indicators -->
            <!--<div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 flex space-x-2 btn-indicate">
                <template x-for="i in Math.ceil(totalItems / itemsToShow)" :key="i">
                    <span @click="goTo(i - 1)" class="cursor-pointer w-3 h-3 rounded-full"
                        :class="{'bg-[#A3C4A9]': Math.floor(index / itemsToShow) === i - 1, 'bg-[#97bb9d9e]': Math.floor(index / itemsToShow) !== i - 1}"></span>
                </template>
            </div>
        </div>
    </div> -->
    <!-- New Featured teacher slider starts  -->
    <div x-data="carouselTeachers()" x-init="init()" class="relative w-full">
        <h2>Featured Teachers</h2>
        <!-- Navigation Arrows -->
        <button @click="prev" :disabled="currentSlide === 0" class="absolute left-[-17px] lg:left-[-50px] top-[55%] lg:top-[40%] -translate-y-1/2 z-10 bg-white px-2 py-1 disabled:opacity-50 disabled:cursor-not-allowed prev">
            <img src="<?= $this->Url->webroot('img/arrow-back-black.png') ?>" alt="Arrow Back">
        </button>
        
        <button @click="next" :disabled="currentSlide >= slides.length - visibleItems" class="absolute right-[-5px] lg:right-[-35px] top-[55%] lg:top-[40%] -translate-y-1/2 z-10 bg-white px-2 py-1 disabled:opacity-50 disabled:cursor-not-allowed next">
            <img src="<?= $this->Url->webroot('img/arrow-next-black.png') ?>" alt="Arrow next">
        </button>
        <div class="relative w-full overflow-hidden" @touchstart="handleSwipeStart" @touchend="handleSwipeEnd" @mousedown="handleSwipeStart" @mouseup="handleSwipeEnd">
            <div class="flex gap-x-4 transition-transform duration-500 ease-in-out feature-carousel-container mb-[30px]" :style="`transform: translateX(-${currentSlide * (100 / visibleItems)}%)`">
                <template x-for="(item, i) in slides" :key="i">
                    <div class="flex-[0_0_calc(50%-1rem)] lg:flex-[0_0_calc(20%-1rem)] flex-shrink-0 text-center box-border">
                        <a :href="generateSeoLink(item)" class="w-full aspect-square overflow-hidden rounded-lg">
                            <img :src="item.src" class="w-full h-full object-cover rounded-lg round-image" :alt="item.name" />
                        </a>
                        <p class="mt-2 text-[#283148] teacher-name info line-clamp-1" x-text="item.name" :title="item.name"></p>
                        <p class="mt-2 text-[#666666] profession info line-clamp-2 h-[46px]" x-text="item.caption"></p>
                        <a class="mt-2 btn-view text-[#D87A61] px-4 py-2">Read More</a>
                    </div>
                </template>
            </div>
            <!-- Indicators -->
            <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 flex space-x-2 btn-indicate">
                <template x-for="(item, i) in Math.ceil(slides.length - visibleItems + 1)">
                    <span @click="currentSlide = i" class="cursor-pointer w-3 h-3 rounded-full" :class="{'bg-[#A3C4A9]': i === currentSlide, 'bg-[#97bb9d9e]': i !== currentSlide}"></span>
                </template>
            </div>
        </div>
    </div>
    <!-- Slider Controls -->
    <!-- <button type="button" class="absolute top-[50%] start-[50px] z-30 px-3 cursor-pointer group focus:outline-none prev" onclick="featuredTeacherPrev();">
        <img src="<?= $this->Url->webroot('img/arrow-back-black.png') ?>" alt="Arrow Back">
    </button>
    <button type="button" class="absolute top-[50%] end-[100px] z-30 px-3 cursor-pointer group focus:outline-none next" onclick="featuredTeacherNext();">
        <img src="<?= $this->Url->webroot('img/arrow-next-black.png') ?>" alt="Arrow Next" class="relative start-[3px]">
    </button>
    <div id="featured-carousel-teacher" class="relative w-full overflow-hidden">
        <div id="featured-slider-teacher" class="relative rounded-lg flex items-center w-full">
            <?php foreach ($featured_teachers as $partner): ?>
                <?php
                    $base = $this->Url->build('/');
                    // $lang = h($this->request->getParam('lang'));
                    $seoUrlParts = [
                        $base . $lang . '/yoga-courses'
                    ];
                    // if (!empty($partner['country'])) {
                    //     $seoUrlParts[] = strtolower(str_replace(' ', '-', $partner['country']));
                    // }
                    // if (!empty($partner['state'])) {
                    //     $seoUrlParts[] = 'region/' . strtolower(str_replace(' ', '-', $partner['state']));
                    // }
                    // if (!empty($partner['city'])) {
                    //     $seoUrlParts[] = strtolower(str_replace(' ', '-', $partner['city']));
                    // }
                    // if (!empty($partner['slug'])) {
                    //     $seoUrlParts[] = $partner['slug'];
                    // }
                    $seoUrl = implode('/', $seoUrlParts);
                ?>
                <div class="duration-700 ease-in-out featured-center-teacher-carousel mr-[0px] md:mr-[30px] lg:mr-[30px] xl:mr-[30px] w-1/4 desktop-view">
                    <div class="bg-white rounded-lg course-card card-container flex flex-col min-h-[450px] h-[600px]">
                        <a href="<?= h($seoUrl) ?>">
                            <img src="<?= h($partner['src']) ?>" alt="<?= h($partner['name']) ?>" class="w-full h-56 object-cover yoga-img">
                            <div class="card-body">
                                <h3 class="yoga-name">
                                    <span class="line-clamp-1"><?= h($partner['name']) ?></span>
                                </h3>
                                <p class="text-gray-600 line-clamp-2 yoga-description"><?= h($partner['caption']) ?></p>
                                <a class="mt-2 btn-view text-[#D87A61]">Read More</a>
                            </div>
                        </a>    
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div> -->
    
    <!-- <h3 class="mobile-view">Featured Teachers</h3> -->
    <!-- <div x-data="{
        index: 0,
        itemsToShow: 3,
        totalItems: <?= count($featured_teachers) ?>,
        images:<?= htmlspecialchars(json_encode($featured_teachers, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE), ENT_QUOTES, 'UTF-8'); ?>,
        next() {
            if (this.index < this.totalItems - this.itemsToShow) {
                this.index++;
            }
        },
        prev() {
            if (this.index > 0) {
                this.index--;
            }
        },
        goTo(i) {
            this.index = i * this.itemsToShow;
        },
        getTransform() {
            return `translateX(-${this.index * 100 / this.itemsToShow}%)`;
        }
    }" class="relative w-full max-w-3xl overflow-hidden mobile-view">
     -->
    <!-- <div x-data="{
        index: 0,
        itemsToShow: 3,
        totalItems: <?= count($featured_teachers) ?>,
        images:<?= htmlspecialchars(json_encode($featured_teachers, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE), ENT_QUOTES, 'UTF-8'); ?>,
        interval: null,
        next() {
            if (this.index < this.totalItems - this.itemsToShow) {
                this.index++;
            } else {
                this.index = 0; // loop back to start
            }
        },
        prev() {
            if (this.index > 0) {
                this.index--;
            }
        },
        goTo(i) {
            this.index = i * this.itemsToShow;
        },
        getTransform() {
            return `translateX(-${this.index * 100 / this.itemsToShow}%)`;
        },
         generateSeoLink(item) {
            const parts = [baseUrl + lang + '/yoga-centers'];

            if (item.country) {
                parts.push(item.country.replace(/\s+/g, '-').toLowerCase());
            }

            if (item.state) {
                parts.push('region', item.state.replace(/\s+/g, '-').toLowerCase());
            }

            if (item.city) {
                parts.push(item.city.replace(/\s+/g, '-').toLowerCase());
            }

            if (item.slug) {
                parts.push(item.slug);
            }

            return parts.join('/');
        }
    }" x-init="interval = setInterval(() => next(), 3000)" @mouseenter="clearInterval(interval)"
        @mouseleave="interval = setInterval(() => next(), 3000)"
        class="relative w-full max-w-3xl overflow-hidden mobile-view">
        <div class="flex transition-transform pb-3 duration-500 ease-in-out space-x-4"
            :style="{'transform': getTransform()}">
            <template x-for="(item, i) in images" :key="i">
                <div class="w-1/4 flex-shrink-0 text-center feature-teacher-container">
                     <a :href="generateSeoLink(item)">
                        <img :src="item.src" class="w-full rounded-lg round-image" />
                    </a>
                    <p class="mt-2 text-gray-700 font-semibold" x-text="item.name"></p>
                    <p class="mt-2 text-gray-700" x-text="item.caption"></p>
                    <a class="mt-2 btn-view text-[#D87A61]">Read More</a>
                </div>
            </template>
        </div>
        <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 flex space-x-2">
            <template x-for="i in Math.ceil(totalItems / itemsToShow)" :key="i">
                <span @click="goTo(i - 1)" class="cursor-pointer w-3 h-3 rounded-full"
                    :class="{'bg-[#A3C4A9]': Math.floor(index / itemsToShow) === i - 1, 'bg-[#a3c4a966]': Math.floor(index / itemsToShow) !== i - 1}"></span>
            </template>
        </div>
    </div> -->
    <div class="destination-view text-center">
        <button class="text-[#D87A61] px-4 py-2">View All</button>
    </div>
</section>
<section class="discover-yoga px-6 py-5 md:px-10 lg:px-25 xl:px-40">
    <div class="mx-auto">
        <h2 class="text-center">Discover Yoga</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-0 ">
            <!-- Card 1 -->
            <div class="bg-transparent rounded-lg">
                <p class="heading">A space to discover traditional Indian Yoga</p>
                <div x-data="{  
                    autoplayIntervalTime: 4000,          
                    slides: [                
                        {
                            imgSrc: '<?= $this->Url->webroot('img/discover-yoga.png') ?>',
                            imgAlt: 'Discover Yoga',                
                        },                
                        {                    
                            imgSrc: '<?= $this->Url->webroot('img/feature-hour-yoga.png') ?>',                    
                            imgAlt: 'Hour Yoga',                
                        },                
                        {                    
                            imgSrc: '<?= $this->Url->webroot('img/feature-yoga-teacher.png') ?>',                    
                            imgAlt: 'Yoga Teacher',                
                        }, 
                        {                    
                            imgSrc: '<?= $this->Url->webroot('img/ashram-master.png') ?>',                    
                            imgAlt: 'Ashram Master',                
                        },                
                        {                    
                            imgSrc: '<?= $this->Url->webroot('img/feature-mom-baby-yoga.png') ?>',                    
                            imgAlt: 'Mom Baby Yoga',                
                        },           
                    ],            
                    currentSlideIndex: 1,
                    isPaused: false,
                    autoplayInterval: null,
                    previous() {                
                        if (this.currentSlideIndex > 1) {                    
                            this.currentSlideIndex = this.currentSlideIndex - 1                
                        } else {   
                            // If it's the first slide, go to the last slide           
                            this.currentSlideIndex = this.slides.length                
                        }            
                    },            
                    next() {                
                        if (this.currentSlideIndex < this.slides.length) {                    
                            this.currentSlideIndex = this.currentSlideIndex + 1                
                        } else {                 
                            // If it's the last slide, go to the first slide    
                            this.currentSlideIndex = 1                
                        }            
                    },  
                    autoplay() {
                        this.autoplayInterval = setInterval(() => {
                            if (! this.isPaused) {
                                this.next()
                            }
                        }, this.autoplayIntervalTime)
                    },
                    // Updates interval time   
                    setAutoplayInterval(newIntervalTime) {
                        clearInterval(this.autoplayInterval)
                        this.autoplayIntervalTime = newIntervalTime
                        this.autoplay()
                    },       
                }" class="relative w-full discover-carousel mobile-view">

                    <!-- previous button -->
                    <button type="button" :disabled="currentSlideIndex === 1" class="prev absolute left-5 top-1/2 z-20 flex rounded-full -translate-y-1/2 items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed" aria-label="previous slide" x-on:click="previous()">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" stroke="#818181" fill="none"
                            stroke-width="2" class="size-4 md:size-6 pr-1.5 w-[95%] h-[95%]" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5 8.25 12l7.5-7.5" />
                        </svg>
                    </button>

                    <!-- next button -->
                    <button type="button" :disabled="currentSlideIndex === slides.length" class="next absolute right-5 top-1/2 z-20 flex rounded-full -translate-y-1/2 items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed" aria-label="next slide" x-on:click="next()">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" stroke="#818181" fill="none"
                            stroke-width="2" class="size-4 md:size-6 pl-1.5 w-[95%] h-[95%]" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                        </svg>
                    </button>

                    <!-- slides -->
                    <!-- Change min-h-[50svh] to your preferred height size -->
                    <div class="relative w-full min-h-[357px]">
                        <template x-for="(slide, index) in slides">
                            <div x-show="currentSlideIndex == index + 1" class="absolute inset-0"
                                x-transition.opacity.duration.1000ms>
                                <img class="absolute w-full h-full inset-0 object-cover text-on-surface dark:text-on-surface-dark"
                                    x-bind:src="slide.imgSrc" x-bind:alt="slide.imgAlt" />
                            </div>
                        </template>
                    </div>

                    <!-- indicators -->
                    <div class="indicator absolute rounded-radius bottom-3 md:bottom-5 left-1/2 z-20 flex -translate-x-1/2 gap-4 md:gap-3 bg-transparent px-1.5 py-1 md:px-2"
                        role="group" aria-label="slides">
                        <template x-for="(slide, index) in slides">
                            <button class="size-3 rounded-full transition bg-on-surface dark:bg-on-surface-dark"
                                x-on:click="currentSlideIndex = index + 1"
                                x-bind:class="[currentSlideIndex === index + 1 ? 'bg-[#D87A61]' : 'bg-[#D87A61]/40']"
                                x-bind:aria-label="'slide ' + (index + 1)"></button>
                        </template>
                    </div>
                </div>
                <p>Believed to have its deepest roots in Yoga about 5000 years ago, India has its pristine presence and
                    glory as the land of Yoga. Yoga has not only helped its aspirants in the communion of their Body and
                    Soul but has also enriched human life with a purposeful existence, thanks to the yogic knowledge
                    attributed to none other than the Adiyogi Lord Shiva himself.</p>

                <p class="mb-0">The last few decades have witnessed more and more people turning to yoga as a
                    supernatural remedy for health and wellness. The subject of yoga deals more with ‘MIND over MATTER’
                    adding more significance to enhance the emotional wellbeing to attain physical wellbeing.</p>
                <div class="btn-discover">
                    <a href="https://yoga.in/discover-yoga/" class="inline-block px-2 py-2 uppercase explore">Explore
                        More</a>
                </div>
            </div>

            <!-- Card 2 -->
            <div class="bg-transparent rounded-lg">
                <!-- <img src="<?= $this->Url->webroot('img/feature-mom-baby-yoga.png') ?>/discover-yoga.png" alt="Discover Yoga" class="w-full object-cover rounded-md"> -->
                <!-- <div id="default-carousel" class="relative w-full overflow-hidden" data-carousel="slide">
                    <!-- Carousel wrapper -->
                <!-- <div id="discover-slider" class="relative rounded-lg flex items-center w-full">
                        <!-- Item 1 -->
                <!-- <div class="duration-700 ease-in-out discover-carousel" data-carousel-item="active">
                            <img src="<?= $this->Url->webroot('img/feature-mom-baby-yoga.png') ?>/discover-yoga.png" class="block w-full top-1/2 left-1/2" alt="Discover yoga">
                        </div>
                        <!-- Item 2 -->
                <!-- <div class="duration-700 ease-in-out discover-carousel" data-carousel-item>
                            <img src="<?= $this->Url->webroot('img/feature-mom-baby-yoga.png') ?>/feature-hour-yoga.png" class="block w-full top-1/2 left-1/2" alt="Discover Yoga 1">
                        </div>
                        <!-- Item 3 -->
                <!-- <div class="duration-700 ease-in-out discover-carousel" data-carousel-item>
                            <img src="<?= $this->Url->webroot('img/feature-mom-baby-yoga.png') ?>/feature-yoga-teacher.png" class="block w-full top-1/2 left-1/2" alt="Discover Yoga 2">
                        </div>
                        <!-- Item 4 -->
                <!-- <div class="duration-700 ease-in-out discover-carousel" data-carousel-item>
                            <img src="<?= $this->Url->webroot('img/feature-mom-baby-yoga.png') ?>/ashram-master.png" class="block w-full top-1/2 left-1/2" alt="Discover Yoga 3">
                        </div>
                        <!-- Item 5 -->
                <!-- <div class="duration-700 ease-in-out discover-carousel" data-carousel-item>
                            <img src="<?= $this->Url->webroot('img/feature-mom-baby-yoga.png') ?>/feature-mom-baby-yoga.png" class="block w-full top-1/2 left-1/2" alt="Discover Yoga 4">
                        </div>
                    </div>
                    <!-- Slider indicators -->
                <!-- <div class="relative z-30 flex w-50 space-x-3 rtl:space-x-reverse slide-buttons">
                        <button type="button" class="w-3 h-3 rounded-full mx-2" aria-current="true" aria-label="Slide 1" data-carousel-slide-to="0"></button>
                        <button type="button" class="w-3 h-3 rounded-full mx-2" aria-current="false" aria-label="Slide 2" data-carousel-slide-to="1"></button>
                        <button type="button" class="w-3 h-3 rounded-full mx-2" aria-current="false" aria-label="Slide 3" data-carousel-slide-to="2"></button>
                        <button type="button" class="w-3 h-3 rounded-full mx-2" aria-current="false" aria-label="Slide 4" data-carousel-slide-to="3"></button>
                        <button type="button" class="w-3 h-3 rounded-full mx-2" aria-current="false" aria-label="Slide 5" data-carousel-slide-to="4"></button>
                    </div>
                    <!-- Slider controls -->
                <!-- <button type="button" class="absolute top-0 start-0 z-30 flex items-center justify-center h-full px-4 cursor-pointer group focus:outline-none" data-carousel-prev onclick="discoverPrev();">
                        <span class="inline-flex items-center justify-center w-10 h-10 rounded-full bg-black dark:bg-gray-800/30 group-hover:bg-white/50 dark:group-hover:bg-gray-800/60 group-focus:ring-4 group-focus:ring-white dark:group-focus:ring-gray-800/70 group-focus:outline-none">
                            <svg class="w-4 h-4 text-white dark:text-gray-800 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 1 1 5l4 4"/>
                            </svg>
                            <span class="sr-only">Previous</span>
                        </span>
                    </button>
                    <button type="button" class="absolute top-0 end-0 z-30 flex items-center justify-center h-full px-4 cursor-pointer group focus:outline-none" data-carousel-next onclick="discoverNext();">
                        <span class="inline-flex items-center justify-center w-10 h-10 rounded-full bg-black dark:bg-gray-800/30 group-hover:bg-white/50 dark:group-hover:bg-gray-800/60 group-focus:ring-4 group-focus:ring-white dark:group-focus:ring-gray-800/70 group-focus:outline-none">
                            <svg class="w-4 h-4 text-white dark:text-gray-800 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                            </svg>
                            <span class="sr-only">Next</span>
                        </span>
                    </button>
                </div> -->
                <div x-data="{  
                    autoplayIntervalTime: 4000,          
                    slides: [                
                        {
                            imgSrc: '<?= $this->Url->webroot('img/discover-yoga.png') ?>',
                            imgAlt: 'Discover Yoga',                
                        },                
                        {                    
                            imgSrc: '<?= $this->Url->webroot('img/feature-hour-yoga.png') ?>',                    
                            imgAlt: 'Hour Yoga',                
                        },                
                        {                    
                            imgSrc: '<?= $this->Url->webroot('img/feature-yoga-teacher.png') ?>',                    
                            imgAlt: 'Yoga Teacher',                
                        }, 
                        {                    
                            imgSrc: '<?= $this->Url->webroot('img/ashram-master') ?>',                    
                            imgAlt: 'Ashram Master',                
                        },                
                        {                    
                            imgSrc: '<?= $this->Url->webroot('img/feature-mom-baby-yoga.png') ?>',                    
                            imgAlt: 'Mom Baby Yoga',                
                        },           
                    ],            
                    currentSlideIndex: 1,
                    isPaused: false,
                    autoplayInterval: null,
                    previous() {                
                        if (this.currentSlideIndex > 1) {                    
                            this.currentSlideIndex = this.currentSlideIndex - 1                
                        } else {   
                            // If it's the first slide, go to the last slide           
                            this.currentSlideIndex = this.slides.length                
                        }            
                    },            
                    next() {                
                        if (this.currentSlideIndex < this.slides.length) {                    
                            this.currentSlideIndex = this.currentSlideIndex + 1                
                        } else {                 
                            // If it's the last slide, go to the first slide    
                            this.currentSlideIndex = 1                
                        }            
                    },  
                    autoplay() {
                        this.autoplayInterval = setInterval(() => {
                            if (! this.isPaused) {
                                this.next()
                            }
                        }, this.autoplayIntervalTime)
                    },
                    // Updates interval time   
                    setAutoplayInterval(newIntervalTime) {
                        clearInterval(this.autoplayInterval)
                        this.autoplayIntervalTime = newIntervalTime
                        this.autoplay()
                    },       
                }" x-init="autoplay" class="relative w-full discover-carousel desktop-view">

                    <!-- previous button -->
                    <button type="button"
                        class="prev absolute left-5 top-1/2 z-20 flex rounded-full -translate-y-1/2 items-center justify-center bg-surface/40 p-2 text-on-surface transition hover:bg-surface/60 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary active:outline-offset-0 dark:bg-surface-dark/40 dark:text-on-surface-dark dark:hover:bg-surface-dark/60 dark:focus-visible:outline-primary-dark"
                        aria-label="previous slide" x-on:click="previous()">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" stroke="currentColor" fill="none"
                            stroke-width="3" class="size-5 md:size-6 pr-0.5" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5 8.25 12l7.5-7.5" />
                        </svg>
                    </button>

                    <!-- next button -->
                    <button type="button"
                        class="next absolute right-5 top-1/2 z-20 flex rounded-full -translate-y-1/2 items-center justify-center bg-surface/40 p-2 text-on-surface transition hover:bg-surface/60 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary active:outline-offset-0 dark:bg-surface-dark/40 dark:text-on-surface-dark dark:hover:bg-surface-dark/60 dark:focus-visible:outline-primary-dark"
                        aria-label="next slide" x-on:click="next()">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" stroke="currentColor" fill="none"
                            stroke-width="3" class="size-5 md:size-6 pl-0.5" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                        </svg>
                    </button>

                    <!-- slides -->
                    <!-- Change min-h-[50svh] to your preferred height size -->
                    <div class="relative w-full min-h-[357px]">
                        <template x-for="(slide, index) in slides">
                            <div x-show="currentSlideIndex == index + 1" class="absolute inset-0"
                                x-transition.opacity.duration.1000ms>
                                <img class="absolute w-full h-full inset-0 object-cover text-on-surface dark:text-on-surface-dark"
                                    x-bind:src="slide.imgSrc" x-bind:alt="slide.imgAlt" />
                            </div>
                        </template>
                    </div>

                    <!-- indicators -->
                    <div class="indicator absolute rounded-radius bottom-3 md:bottom-5 left-1/2 z-20 flex -translate-x-1/2 gap-4 md:gap-3 bg-transparent px-1.5 py-1 md:px-2"
                        role="group" aria-label="slides">
                        <template x-for="(slide, index) in slides">
                            <button class="size-3 mx-2 rounded-full transition bg-on-surface dark:bg-on-surface-dark"
                                x-on:click="currentSlideIndex = index + 1"
                                x-bind:class="[currentSlideIndex === index + 1 ? 'bg-[#D87A61]' : 'bg-[#D87A61]/40']"
                                x-bind:aria-label="'slide ' + (index + 1)"></button>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php if (!empty($imported['masters'])) { ?>
    <section class="yoga-master px-6 py-5 md:px-10 lg:px-25 xl:px-40">
        <div class="mx-auto">
            <h2 class="text-center">Yoga Masters of India</h2>
            <!-- Vertical tab with alpineJS -->
            <div class="yoga-master-container  lg:h-130 flex flex-col overflow-y-scroll overflow-x-none">
                <section class="space-y-8 pr-5 desktop-view">
                    <div class="flex items-start justify-between" x-data="{tab: 1}">
                        <div class="flex flex-col justify-start yoga-master-wrapper">
                            <?php
                            $tab = 1;
                            foreach ($imported['masters'] as $key => $master) { ?>
                                <div class="text-sm border-b-2 border-slate-200"
                                    :class="{'z-20 transform font-bold': tab === <?= $tab ?>}"
                                    @click.prevent="tab = <?= $tab ?>">
                                    <div class="tab-content w-100">
                                        <p class="trainer-name text-[#000]"><?= $master['title'] ?></p>
                                        <p class="content"><?= $master['description'] ?></p>
                                        <a href="<?= $master['url'] ?>" target="_blank" class="text-[#D87A61] uppercase px-3 py-2"
                                            @click.stop>Read More</a>
                                    </div>
                                    <div class="tab-active-img flex justify-end w-18">
                                        <img src="<?= $this->Url->webroot('img/arrow-right.png') ?>" alt="Arrow Right">
                                    </div>
                                </div>
                                <?php $tab++;
                            } ?>
                        </div>
                        <div class="sticky top-0 end-0 yoga-image-wrapper">
                            <?php
                            $tab = 1;
                            foreach ($imported['masters'] as $key => $master) { ?>
                                <div class="space-y-6 h-113" x-show="tab === <?= $tab ?>">
                                    <img src="<?= $master['image'] ?>" alt="<?= $master['title'] ?>"
                                        class="w-full object-cover rounded-lg h-100">
                                </div>
                                <?php $tab++;
                            } ?>
                        </div>
                    </div>
                </section>
                <?php if (!empty($imported['masters'])): ?>
                    <div x-data="carouselMasterData()" class="relative w-full discover-carousel mobile-view">
                        <!-- slides -->
                        <!-- Change min-h-[50svh] to your preferred height size -->
                        <div class="relative w-full">
                            <button type="button" :disabled="currentSlideIndex === 1" class="prev relative left-5  z-20 flex rounded-full -translate-y-1/2 items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed" aria-label="previous slide" x-on:click="previous()">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" stroke="#818181" fill="none"
                                    stroke-width="2" class="size-4 md:size-6 pr-1.5 w-[95%] h-[95%] relative start-[3px] aria-hidden="true">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5 8.25 12l7.5-7.5" />
                                </svg>
                            </button>
                                <!-- next button -->
                            <button type="button" :disabled="currentSlideIndex === slides.length" class="next relative right-5  z-20 flex rounded-full -translate-y-1/2 items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed" aria-label="next slide" x-on:click="next()">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" stroke="#818181" fill="none"
                                    stroke-width="2" class="size-4 md:size-6 pl-1.5 w-[95%] h-[95%]" aria-hidden="true">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                                </svg>
                            </button>
                            <template x-for="(slide, index) in slides">
                                <div x-show="currentSlideIndex == index + 1" class="inset-0"
                                    >
                                    <p class="trainer-name" x-text="slide.title"></p>
                                    <img class="w-full h-[371px] inset-0 object-cover object-top text-on-surface dark:text-on-surface-dark rounded-lg"
                                        x-bind:src="slide.image" x-bind:alt="slide.title" />
                                    <p class="content" x-text="slide.description"></p>
                                    <div class="read-master text-center">
                                        <a :href="slide.url" target="_blank" class="bg-[#D87A61] uppercase px-3 py-2">Read More</a>
                                    </div>
                                </div>
                            </template>
                        </div>
                        <!-- previous button -->

                        <!-- indicators -->
                        <!-- <div class="indicator absolute rounded-radius bottom-3 md:bottom-5 left-1/2 z-20 flex -translate-x-1/2 gap-4 md:gap-3 bg-transparent px-1.5 py-1 md:px-2" role="group" aria-label="slides" >
                    <template x-for="(slide, index) in slides">
                        <button class="size-3 rounded-full transition bg-on-surface dark:bg-on-surface-dark" x-on:click="currentSlideIndex = index + 1" x-bind:class="[currentSlideIndex === index + 1 ? 'bg-[#D87A61]' : 'bg-[#D87A61]/40']" x-bind:aria-label="'slide ' + (index + 1)"></button>
                    </template>
                </div> -->
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>
<?php } ?>
<?= $this->element('frontend/newsletter_section') ?>
<script>

function centerSliderData() {
    return {
        partners: <?= json_encode($featured_partners) ?>,
        currentIndex: 0,
        itemsPerSlide: 4,
        get visiblePartners() {
            return this.partners.slice(this.currentIndex, this.currentIndex + this.itemsPerSlide);
        },
        next() {
            if (this.currentIndex + this.itemsPerSlide < this.partners.length) {
                this.currentIndex += this.itemsPerSlide;
            }
        },
        prev() {
            if (this.currentIndex - this.itemsPerSlide >= 0) {
                this.currentIndex -= this.itemsPerSlide;
            }
        },
        generateSeoLink(item) {
            const parts = [baseUrl + lang + '/yoga-centers'];

            if (item.country) {
                parts.push(item.country.replace(/\s+/g, '-').toLowerCase());
            }

            if (item.state) {
                parts.push('region', item.state.replace(/\s+/g, '-').toLowerCase());
            }

            if (item.city) {
                parts.push(item.city.replace(/\s+/g, '-').toLowerCase());
            }

            if (item.slug) {
                parts.push(item.slug);
            }

            return parts.join('/');
        }
    }
}

function testimonialCarousel() {
    return {
        current: 0,
        interval: null,
        itemsPerView: 1, // default to 1
        startX: 0,
        endX: 0,
        testimonials: [{
                quote: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore",
                name: "Esther Howard",
                role: "CEO, Acme Inc.",
                image: "/img/esther.png"
            },
            {
                quote: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore",
                name: "John Smith",
                role: "Freelancer",
                image: "/img/smith.png"
            },
            {
                quote: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore",
                name: "Joseph Cristopher",
                role: "Marketing Manager",
                image: "/img/cristopher.png"
            },
            {
                quote: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore",
                name: "Test Name",
                role: "Test Role",
                image: "/img/phool-chatti.png"
            },
        ],
        get totalSlides() {
            return Math.ceil(this.testimonials.length / this.itemsPerView);
        },
        updateItemsPerView() {
            this.itemsPerView = window.innerWidth < 640 ? 1 : 2;
        },
        prev() {
            this.current = (this.current === 0) ? this.testimonials.length - 1 : this.current - 1;
        },
        next() {
            if (this.testimonials.length === 0) {
                this.current = 0;
                return;
            }
            // if (this.current < this.totalSlides - 1) {
            //     this.current++;
            // }
            // if (this.current >= this.testimonials.length - 1) {
            //     this.current = 0; // Go back to the first
            // } else {
            //     this.current++;
            // }
            if (this.current < this.totalSlides - 0) {
                this.current++;
            }

            // this.current = (this.current + 1) % this.totalSlides;
        },
        handleTouchStart(e) {
            this.startX = e.touches[0].clientX;
        },
        handleTouchEnd(e) {
            this.endX = e.changedTouches[0].clientX;
            const deltaX = this.endX - this.startX;
            if (Math.abs(deltaX) > 50) { // minimum swipe threshold
                if (deltaX < 0) {
                    this.next(); // swiped left
                } else {
                    this.prev(); // swiped right
                }
            }
            if (window.innerWidth < 640) {
                this.current = Math.min(Math.max(this.current, 0), this.totalSlides - 1);
            } else {
                this.current = Math.min(Math.max(this.current, 0), this.totalSlides - 0);
            }
        },
        autoplay() {
            this.interval = setInterval(() => {
                this.next();
            }, 5000);
        },
        stopAutoplay() {
            clearInterval(this.interval);
        },
        init() {
            this.updateItemsPerView();
            window.addEventListener('resize', () => {
                this.updateItemsPerView();
                this.current = 0;
            });
            // this.autoplay();
        }
        // init() {
        //     this.updateItemsPerView();
        //     window.addEventListener('resize', () => {
        //         this.updateItemsPerView();
        //         this.current = 0; // reset slider on resize
        //     });
        //     // this.autoplay();
        // }
    };
}


    //for carousel
    const featured_teachers = <?= json_encode($featured_teachers, JSON_UNESCAPED_UNICODE) ?>;
    const masters = <?= (!empty($imported['masters'])) ? json_encode($imported['masters'], JSON_UNESCAPED_UNICODE) : "[]" ?>

    function carouselTeachers() {
        return {
            slides: typeof featured_teachers !== 'undefined' ? featured_teachers : [],
            currentSlide: 0,
            visibleItems: 5,
            autoplayInterval: null,
            autoplaySpeed: 3000, // Number of slides visible at a time
            startX: 0,
            endX: 0,
            handleSwipeStart(event) {
                this.startX = event.type.includes('mouse') ? event.clientX : event.touches[0].clientX;
            },
            handleSwipeEnd(event) {
                this.endX = event.type.includes('mouse') ? event.clientX : event.changedTouches[0].clientX;
                const diff = this.startX - this.endX;

                if (Math.abs(diff) > 50) {
                    if (diff > 0) {
                        this.next();
                    } else {
                        this.prev();
                    }
                }
            },
            init() {
                this.setResponsiveItems();
                window.addEventListener('resize', this.setResponsiveItems);
            },

            setResponsiveItems() {
                const width = window.innerWidth;
                if (width < 992) {         // Tailwind's sm breakpoint (mobile)
                    this.visibleItems = 2;
                } else {
                    this.visibleItems = 5; // desktop
                }
            },
            
            next() {
                // Max slides before looping back
                // let maxSlides = this.slides.length - this.visibleItems;
                // if (this.currentSlide < maxSlides) {
                //     this.currentSlide++;
                // } else {
                //     this.currentSlide = 0; // Loop to first slide
                // }
                if (this.currentSlide < this.slides.length - this.visibleItems) {
                    this.currentSlide++;
                }
            },
            prev() {
                // let maxSlides = this.slides.length - this.visibleItems;
                // if (this.currentSlide > 0) {
                //     this.currentSlide--;
                // } else {
                //     this.currentSlide = maxSlides; // Loop to last visible set
                // }
                if (this.currentSlide > 0) {
                    this.currentSlide--;
                }
            },
            startAutoplay() {
                this.autoplayInterval = setInterval(() => this.next(), this.autoplaySpeed);
            },
            resetAutoplay() {
                clearInterval(this.autoplayInterval);
                this.startAutoplay();
            },
            generateSeoLink(item) {
                const parts = [baseUrl + lang + '/yoga-centers'];

                if (item.country) {
                    parts.push(item.country.replace(/\s+/g, '-').toLowerCase());
                }

                if (item.state) {
                    parts.push('region', item.state.replace(/\s+/g, '-').toLowerCase());
                }

                if (item.city) {
                    parts.push(item.city.replace(/\s+/g, '-').toLowerCase());
                }

                if (item.slug) {
                    parts.push(item.slug);
                }

                return parts.join('/');
            }
        }
    }

    function carouselMasterData() {
        return {
            autoplayIntervalTime: 4000,
            slides: typeof masters !== 'undefined' ? masters : [],
            currentSlideIndex: 1,
            isPaused: false,
            autoplayInterval: null,

            previous() {
                this.currentSlideIndex = this.currentSlideIndex > 1 ? this.currentSlideIndex - 1 : this.slides.length;
            },
            next() {
                this.currentSlideIndex = this.currentSlideIndex < this.slides.length ? this.currentSlideIndex + 1 : 1;
            },
            autoplay() {
                this.autoplayInterval = setInterval(() => {
                    if (!this.isPaused) {
                        this.next();
                    }
                }, this.autoplayIntervalTime);
            },
            setAutoplayInterval(newIntervalTime) {
                clearInterval(this.autoplayInterval);
                this.autoplayIntervalTime = newIntervalTime;
                this.autoplay();
            }
        };
    }

    function showError() {
        Swal.fire({
            icon: 'info',
            // title: 'Oops...',
            text: 'Please fill at least one field to search.',
            confirmButtonText: 'OK',
        });
    }

    function courseSlider() {
        return {
        current: 0,
        itemsPerView: 4,
        total: 0,

        get maxIndex() {
            return Math.max(this.total - this.itemsPerView, 0);
        },

        init() {
            this.updateItemsPerView();
            this.total = document.querySelectorAll('.slide').length;

            window.addEventListener('resize', () => {
            this.updateItemsPerView();
            if (this.current > this.maxIndex) {
                this.current = this.maxIndex;
            }
            });
        },

        updateItemsPerView() {
            const w = window.innerWidth;
            if (w < 640) this.itemsPerView = 1;
            else if (w < 1024) this.itemsPerView = 2;
            else this.itemsPerView = 4;
        },

        prev() {
            if (this.current > 0) this.current--;
        },

        next() {
            if (this.current < this.maxIndex) this.current++;
        }
        }
    }
</script>