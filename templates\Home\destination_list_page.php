<?php use Cake\Core\Configure; ?>
<?php 
$this->assign('title', 'Yoga Destinations in India - Find Premium Yoga Cities & States'); 
$this->assign('meta_desc', 'Discover the best yoga destinations across India. Find top yoga cities and states for retreats, courses, and spiritual journeys.');
?>

<link rel="stylesheet" href="<?= $this->Url->webroot('css/card.css') ?>">

<section class="course-list-container overflow-y-auto max-h-screen snap-y snap-mandatory md:overflow-visible md:max-h-none md:snap-none">
    <div class="px-6 md:px-10 lg:px-25 xl:pl-40 xl:pr-21 2xl:px-60">
        <!-- Breadcrumbs -->
        <nav class="flex breadcrumb-nav desktop-view" aria-label="Breadcrumb">
            <ol class="pt-4 flex items-center space-x-1 md:space-x-0 rtl:space-x-reverse">
                <li>
                    <a href="<?= $this->Url->build(['controller' => 'Home', 'action' => 'index']) ?>" 
                    class="flex items-center text-sm text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                        Home
                    </a>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-[5px] h-3 text-[#1C1B1F] mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="flex items-center text-sm text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                            Destinations
                        </span>
                    </div>
                </li>
            </ol>
        </nav>
        
        <h1 class="heading desktop-view">Yoga Destinations in India</h1>
                    <p class="desktop-view">
                    India, the birthplace of yoga, offers a diverse array of desnaons that cater to both novice and
experienced praconers seeking to deepen their pracce and connect with the ancient roots of this
holisc discipline. From the spiritual heart of Rishikesh to the serene beaches of Goa, each locaon
provides a unique blend of natural beauty, cultural richness, and authenc yoga experiences.
Whether you're looking for a rigorous teacher training course, a serene retreat, or a blend of cultural
immersion and yoga, India's diverse yoga desnaons promise an enriching experience that
resonates long aer the journey ends.
Please click any of the links below to get more informaon about these yoga desnaons.
                    </p>
        
        <!-- Mobile Header -->
        <div class="mobile-filter mobile-view snap-start">
            <div class="filter-container">
                <div class="mt-[15px] flex items-center justify-between">
                    <h1 class="heading">Yoga Destinations in India</h1>
                    <p>
                    India, the birthplace of yoga, offers a diverse array of desnaons that cater to both novice and
experienced praconers seeking to deepen their pracce and connect with the ancient roots of this
holisc discipline. From the spiritual heart of Rishikesh to the serene beaches of Goa, each locaon
provides a unique blend of natural beauty, cultural richness, and authenc yoga experiences.
Whether you're looking for a rigorous teacher training course, a serene retreat, or a blend of cultural
immersion and yoga, India's diverse yoga desnaons promise an enriching experience that
resonates long aer the journey ends.
Please click any of the links below to get more informaon about these yoga desnaons.
                    </p>
                </div>
            </div>
        </div>
        
<?php if (!empty($imported['destinations'])): ?>
    <section class="yoga-destination py-5 px-6 md:px-10 lg:px-25 xl:px-40 relative">
        <div class="mx-auto relative">
            <h2>Top Yoga Destinations</h2>
            <!-- Slider controls -->
            <button type="button"
                class="absolute top-[50px] start-auto end-[190px] z-30 flex items-center justify-center px-4 cursor-pointer group focus:outline-none prev"
                data-carousel-prev onclick="popularPrev();">
                <img src="<?= $this->Url->webroot('img/arrow-back-black.png') ?>" alt="Arrow Back">
            </button>
            <button type="button"
                class="absolute top-[50px] end-[140px] z-30 flex items-center justify-center px-4 cursor-pointer group focus:outline-none next"
                data-carousel-next onclick="popularNext();">
                <img src="<?= $this->Url->webroot('img/arrow-next-black.png') ?>" alt="Arrow Next">
            </button>
            <div id="default-carousel2" class="relative w-full overflow-hidden lg:h-[468px]" data-carousel="slide">
                <!-- Carousel wrapper -->
                <div id="popular-slider" class="relative rounded-lg flex items-center w-full">
                    <?php
                    $i = 1;
                    foreach ($imported['destinations'] as $destination):
                        if ($i == 1) {
                            $crousel_item = 'data-carousel-item="active"';
                        } else {
                            $crousel_item = 'data-carousel-item';
                        }
                        ?>
                        <!-- Item 1 -->
                        <div class="duration-700 ease-in-out popular-carousel mr-[0px] md:mr-[30px] lg:mr-[30px] xl:mr-[30px]"
                            <?= $crousel_item ?>>
                            <!-- Card 1 -->
                            <div class="bg-transparent">
                                <a href="<?= $destination['url'] ?>" target="_blank">
                                    <div class="card-img relative">
                                        <img src="<?= $destination['image'] ?>" alt="<?= $destination['title'] ?>"
                                            class="w-full h-87 object-cover">
                                        <div class="overlay"><?= $destination['title'] ?></div>
                                    </div>

                                </a>
                                <div class="card-body">
                                    <p class="time">Upgrade your yoga and wellness knowledge by visiting and which cover a wide range of holistic health</p>
                                    <button class="text-[#D87A61] px-4 py-2" x-data :data-url="'<?= $destination['url'] ?>'"
                                        @click="window.location.href = $el.dataset.url" target="_blank">Read More</button>
                                </div>
                            </div>
                        </div>
                        <?php $i++;
                    endforeach; ?>
                </div>
                <!-- Slider indicators -->
                <div class="relative z-30 flex w-100 justify-center rtl:space-x-reverse slide-buttons">
                    <?php
                    $k = 1;
                    foreach ($imported['destinations'] as $key => $destination) { ?>
                        <button type="button" class="w-3 h-3 rounded-full mx-2" aria-current="true" aria-label="Slide <?= $k ?>"
                            data-carousel-slide-to="<?= $key ?>"></button>
                        <button type="button" class="w-3 h-3 rounded-full mx-2" aria-current="false"
                            aria-label="Slide <?= $k ?>" data-carousel-slide-to="<?= $key ?>"></button>
                        <button type="button" class="w-3 h-3 rounded-full mx-2" aria-current="false"
                            aria-label="Slide <?= $k ?>" data-carousel-slide-to="<?= $key ?>"></button>
                        <button type="button" class="w-3 h-3 rounded-full mx-2" aria-current="false"
                            aria-label="Slide <?= $k ?>" data-carousel-slide-to="<?= $key ?>"></button>
                        <button type="button" class="w-3 h-3 rounded-full mx-2" aria-current="false"
                            aria-label="Slide <?= $k ?>" data-carousel-slide-to="<?= $key ?>"></button>
                    <?php } ?>
                </div>
            </div>
            <div class="destination-view text-center">
                <button class="mt-4 text-[#D87A61] px-4 py-2" x-data :data-url="'<?= $configSettings['WP_DESTINATIONS'] ?>'"
                    @click="window.location.href = $el.dataset.url">View All</button>
            </div>
        </div>
    </section>
<?php endif; ?>


        <!-- Main Content -->
        <div class="search-content-wrapper">
        
        

        
        <?php if (empty($data)): ?>
                <div class="text-center py-16">
                    <p class="text-lg text-gray-600">No destinations found.</p>
                </div>
            <?php else: ?>
                <?php foreach ($data as $regionName => $regionData): ?>
                    <?php 
                    $regionSlug = $regionData['region_slug'] ?? null;
                    $countrySlug = $regionData['country_slug'] ?? 'india';
                    $states = $regionData['states'] ?? [];
                    ?>
                    <div class="mb-16">
                        <!-- Region Header -->
                        <div class="text-left mb-12">
                            <h2 class="text-3xl md:text-4xl font-bold mb-4 text-[#000000]">
                                Yoga in <?= h($regionName); ?>
                            </h2>
                            <p class="text-left text-md max-w-4xl text-[#000000]">
                                Explore all major yoga cities in <?= h($regionName); ?>
                            </p>
                        </div>
                        
                        <!-- States and Cities -->
                        <div class="bg-white rounded-2xl shadow-lg border border-[#fae7c9] p-8 md:p-12">
                            <?php foreach ($states as $state): ?>
                                <div class="mb-8 last:mb-0">
                                    <!-- State Header -->
                                    <h3 class="text-xl md:text-2xl font-bold mb-4 text-[#b8860b]">
                                        <?php if (!empty($state['state_slug']) && !empty($regionSlug) && isset($state['type']) && $state['type'] === 'state'): ?>
                                            <a href="/en/yoga/<?= h($countrySlug); ?>/<?= h($regionSlug); ?>/<?= h($state['state_slug']); ?>"
                                               class="hover:text-[#9a7209] transition-colors duration-200">
                                                <?= h($state['state_name']); ?>
                                            </a>
                                        <?php else: ?>
                                            <?= h($state['state_name']); ?>
                                        <?php endif; ?>
                                    </h3>
                                    
                                    <!-- Cities -->
                                    <?php if (!empty($state['cities'])): ?>
                                        <div class="flex flex-wrap gap-3">
                                            <?php foreach ($state['cities'] as $city): ?>
                                                <span class="inline-block px-4 py-2 bg-[#fae7c9] text-[#b8860b] rounded-full text-base font-semibold hover:bg-[#f5d99b] transition-colors duration-200">
                                                    <?php if (!empty($regionSlug) && !empty($state['state_slug'])): ?>
                                                        <a href="/en/yoga/<?= h($countrySlug); ?>/<?= h($regionSlug); ?>/<?= h($state['state_slug']); ?>/<?= h($city->slug) ?>"
                                                           class="hover:text-[#9a7209] transition-colors duration-200">
                                                            <?= h($city->name); ?>
                                                        </a>
                                                    <?php else: ?>
                                                        <span class="text-[#b8860b]"><?= h($city->name); ?></span>
                                                    <?php endif; ?>
                                                </span>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</section>
