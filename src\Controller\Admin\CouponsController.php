<?php
declare(strict_types=1);

namespace App\Controller\Admin;

use App\Controller\Admin\AppController;
use Cake\Routing\Router;
use Cake\Core\Configure;
use Cake\Utility\Security;
use Cake\I18n\FrozenTime;
use Authentication\PasswordHasher\DefaultPasswordHasher;
use Cake\Http\Cookie\Cookie;
use DateTime;
use Cake\Http\Response;
use Cake\Cache\Cache;
use Cake\Log\Log;

/**
 * Coupons Controller
 *
 * @property \App\Model\Table\CouponsTable $Coupons
 */
class CouponsController extends AppController
{

    public function initialize(): void
    {
        parent::initialize();
        $this->loadComponent('Flash');
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->loadComponent('CustomPaginator');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
    }

    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function index()
    {
        // Get sorting parameters
        $sort = $this->request->getQuery('sort', 'created_at');
        $direction = $this->request->getQuery('direction', 'desc');

        // Validate sort field to prevent SQL injection
        $allowedSortFields = ['id', 'code', 'title', 'discount_type', 'discount_value', 'status', 'created_at', 'end_date'];
        if (!in_array($sort, $allowedSortFields)) {
            $sort = 'created_at';
        }

        // Validate direction
        $direction = strtolower($direction) === 'asc' ? 'asc' : 'desc';

        $this->paginate = [
            'limit' => 10,
            'order' => ['Coupons.' . $sort => $direction]
        ];

        // Build query
        $query = $this->Coupons->find();

        // Apply filters
        $status = $this->request->getQuery('status');
        $discountType = $this->request->getQuery('discount_type');
        $search = $this->request->getQuery('search');
        $page = $this->request->getQuery('page', 1);

        if (!empty($status)) {
            $query->where(['Coupons.status' => $status]);
        }

        if (!empty($discountType)) {
            $query->where(['Coupons.discount_type' => $discountType]);
        }

        if (!empty($search)) {
            $query->where([
                'OR' => [
                    'Coupons.code LIKE' => '%' . $search . '%',
                    'Coupons.title LIKE' => '%' . $search . '%',
                    'Coupons.description LIKE' => '%' . $search . '%'
                ]
            ]);
        }

        $coupons = $this->paginate($query);

        // Store current pagination state in session
        $paginationState = [
            'page' => $page,
            'status' => $status,
            'discount_type' => $discountType,
            'search' => $search,
            'sort' => $sort,
            'direction' => $direction
        ];
        $paginationState = array_filter($paginationState); // Remove empty values
        $this->request->getSession()->write('Coupons.pagination', $paginationState);

        // Pass current filters to the view for URL building
        $currentFilters = [
            'status' => $status,
            'discount_type' => $discountType,
            'search' => $search
        ];
        $currentFilters = array_filter($currentFilters); // Remove empty values

        $this->set(compact('coupons', 'currentFilters', 'sort', 'direction'));
    }

    /**
     * View method
     *
     * @param string|null $id Coupon id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $coupon = $this->Coupons->get($id);

        // Preserve pagination parameters for back navigation
        $paginationParams = $this->_getPaginationParams();

        $this->set(compact('coupon', 'paginationParams'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $coupon = $this->Coupons->newEmptyEntity();
        if ($this->request->is('post')) {
            $coupon = $this->Coupons->patchEntity($coupon, $this->request->getData());
            if ($this->Coupons->save($coupon)) {
                $this->Flash->success(__('The coupon has been saved.'));

                // Redirect to first page to show the new coupon (usually sorted by created_at desc)
                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The coupon could not be saved. Please, try again.'));
        }
        $this->set(compact('coupon'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Coupon id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $coupon = $this->Coupons->get($id);

        if ($this->request->is(['patch', 'post', 'put'])) {
            $coupon = $this->Coupons->patchEntity($coupon, $this->request->getData());
            if ($this->Coupons->save($coupon)) {
                $this->Flash->success(__('The coupon has been saved.'));

                // Get saved pagination state from session and redirect
                $sessionParams = $this->request->getSession()->read('Coupons.pagination');
                if (!empty($sessionParams)) {
                    $queryString = http_build_query($sessionParams);
                    return $this->redirect('/admin/coupons?' . $queryString);
                }

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The coupon could not be saved. Please, try again.'));
        }

        $this->set(compact('coupon'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Coupon id.
     * @return \Cake\Http\Response|null|void Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $coupon = $this->Coupons->get($id);

        if ($this->Coupons->delete($coupon)) {
            $this->Flash->success(__('The coupon has been deleted.'));
        } else {
            $this->Flash->error(__('The coupon could not be deleted. Please, try again.'));
        }

        // Get saved pagination state from session and redirect
        $sessionParams = $this->request->getSession()->read('Coupons.pagination');
        if (!empty($sessionParams)) {
            $queryString = http_build_query($sessionParams);
            return $this->redirect('/admin/coupons?' . $queryString);
        }

        return $this->redirect(['action' => 'index']);
    }

    /**
     * Toggle status method
     *
     * @param string|null $id Coupon id.
     * @return \Cake\Http\Response|null|void Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function toggleStatus($id = null)
    {
        $this->request->allowMethod(['post', 'patch']);
        $coupon = $this->Coupons->get($id);

        $coupon->status = ($coupon->status === 'Active') ? 'Inactive' : 'Active';

        if ($this->Coupons->save($coupon)) {
            $this->Flash->success(__('The coupon status has been updated.'));
        } else {
            $this->Flash->error(__('The coupon status could not be updated. Please, try again.'));
        }

        // Get saved pagination state from session and redirect
        $sessionParams = $this->request->getSession()->read('Coupons.pagination');
        if (!empty($sessionParams)) {
            $queryString = http_build_query($sessionParams);
            return $this->redirect('/admin/coupons?' . $queryString);
        }

        return $this->redirect(['action' => 'index']);
    }

    /**
     * Bulk actions method
     *
     * @return \Cake\Http\Response|null|void Redirects to index.
     */
    public function bulkActions()
    {
        $this->request->allowMethod(['post']);

        $action = $this->request->getData('bulk_action');
        $selectedIds = $this->request->getData('selected_ids');

        if (empty($selectedIds)) {
            $this->Flash->error(__('Please select at least one coupon.'));

            // Get saved pagination state from session and redirect
            $sessionParams = $this->request->getSession()->read('Coupons.pagination');
            if (!empty($sessionParams)) {
                $queryString = http_build_query($sessionParams);
                return $this->redirect('/admin/coupons?' . $queryString);
            }

            return $this->redirect(['action' => 'index']);
        }

        $count = 0;
        switch ($action) {
            case 'activate':
                $count = $this->Coupons->updateAll(
                    ['status' => 'Active'],
                    ['id IN' => $selectedIds]
                );
                $this->Flash->success(__('Activated {0} coupons.', $count));
                break;

            case 'deactivate':
                $count = $this->Coupons->updateAll(
                    ['status' => 'Inactive'],
                    ['id IN' => $selectedIds]
                );
                $this->Flash->success(__('Deactivated {0} coupons.', $count));
                break;

            case 'delete':
                $coupons = $this->Coupons->find()->where(['id IN' => $selectedIds]);
                $count = $this->Coupons->deleteMany($coupons);
                $this->Flash->success(__('Deleted {0} coupons.', count($count)));
                break;

            default:
                $this->Flash->error(__('Invalid bulk action.'));
        }

        // Get saved pagination state from session and redirect
        $sessionParams = $this->request->getSession()->read('Coupons.pagination');
        if (!empty($sessionParams)) {
            $queryString = http_build_query($sessionParams);
            return $this->redirect('/admin/coupons?' . $queryString);
        }

        return $this->redirect(['action' => 'index']);
    }

    /**
     * Export method
     *
     * @return \Cake\Http\Response
     */
    public function export()
    {
        // Build query
        $query = $this->Coupons->find();

        // Apply same filters as index
        $status = $this->request->getQuery('status');
        $discountType = $this->request->getQuery('discount_type');
        $search = $this->request->getQuery('search');

        if (!empty($status)) {
            $query->where(['Coupons.status' => $status]);
        }

        if (!empty($discountType)) {
            $query->where(['Coupons.discount_type' => $discountType]);
        }

        if (!empty($search)) {
            $query->where([
                'OR' => [
                    'Coupons.code LIKE' => '%' . $search . '%',
                    'Coupons.title LIKE' => '%' . $search . '%',
                    'Coupons.description LIKE' => '%' . $search . '%'
                ]
            ]);
        }

        $coupons = $query->all();

        $this->response = $this->response->withType('csv');
        $this->response = $this->response->withDownload('coupons_' . date('Y-m-d') . '.csv');

        $this->set(compact('coupons'));
        $this->viewBuilder()->setTemplate('export_csv');
        $this->viewBuilder()->setLayout('csv');
    }

    /**
     * Get pagination parameters from request or session
     *
     * @return array
     */
    private function _getPaginationParams()
    {
        $params = [];

        // Try to get from query parameters first
        if ($this->request->getQuery('page')) {
            $params['page'] = $this->request->getQuery('page');
        }
        if ($this->request->getQuery('status')) {
            $params['status'] = $this->request->getQuery('status');
        }
        if ($this->request->getQuery('discount_type')) {
            $params['discount_type'] = $this->request->getQuery('discount_type');
        }
        if ($this->request->getQuery('search')) {
            $params['search'] = $this->request->getQuery('search');
        }

        // If no query params, try to get from session
        if (empty($params)) {
            $sessionParams = $this->request->getSession()->read('Coupons.pagination');
            if (!empty($sessionParams)) {
                $params = $sessionParams;
            }
        }

        return $params;
    }

    /**
     * Build index URL with pagination parameters
     *
     * @param array $params
     * @return array
     */
    private function _buildIndexUrl($params = [])
    {
        $url = ['action' => 'index'];

        // Add query parameters
        if (!empty($params)) {
            $url['?'] = $params;
        }

        return $url;
    }
}
