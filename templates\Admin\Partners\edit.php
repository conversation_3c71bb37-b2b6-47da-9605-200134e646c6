<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Offer $offer
 */
?>
<?php $this->append('style'); ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <?= $this->Html->meta('csrfToken', $this->request->getAttribute('csrfToken')) ?>

    <link rel="stylesheet" href="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.css') ?>">
    <link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
    <link rel="stylesheet" href="<?= $this->Url->webroot('bundles/intlTelInput/css/intlTelInput.min.css') ?>">
    <script src="<?= $this->Url->webroot('bundles/intlTelInput/js/intlTelInput.min.js') ?>"></script>
    <script src="<?= $this->Url->webroot('bundles/intlTelInput/js/utils.js') ?>"></script>

    <style>
        .iti {
            width: 100%;
            display: block;
        }

        /* Style the phone input to match other fields */
        .iti .form-control {
            width: 100%;
            height: calc(2.25rem + 2px);
            padding: .375rem .75rem;
            font-size: 1rem;
            line-height: 1.5;
            border: 1px solid #ced4da;
            border-radius: .25rem;
        }

        /* Fix the flag dropdown alignment */
        .iti__flag-container {
            height: calc(2.25rem + 2px);
            display: flex;
            align-items: center;
        }

        #map {
            height: 90vh;
            width: 100%;
        }

        .op .select {
            display: flex;
            align-items: center;
        }

        .op .select .checkbox {
            margin-right: 10px;
        }

        .op .select .checkbox label {
            display: flex;
            align-items: flex-start;
        }

        .op .select .checkbox label > .form-check {
            margin-right: 5px;
            position: relative;
            top: 2px;
        }
    </style>
</head>
<?php $this->end(); ?>
<div class="main-content bg-container">
    <div class="row">
        <div class="col-12 col-xl-12">
            <div class="card card-primary">
                <div class="d-flex justify-content-between align-items-center">
                    <ul class="breadcrumb breadcrumb-style m-0">
                        <li class="breadcrumb-item">Partners</li>
                        <li class="breadcrumb-item">
                            <a href="<?= $this->Url->build(['controller' => 'Partners', 'action' => 'index']) ?>">Partners</a>
                        </li>
                        <li class="breadcrumb-item">Edit Partner</li>
                    </ul>
                    <a href="javascript:void(0);" class="d-flex align-items-center partner-back-button  breadcrumb m-0" id="back-button-mo" onclick="history.back();">
                        <span class="rotate me-2">⤣</span>
                        <small class="fw-bold" data-translate="back"><?= __("BACK") ?></small>
                    </a>
                </div>
                <div class="section-body1">
                    <div class="container-fluid">
                        <?php
                        $successMessage = $this->Flash->render('partner_success');
                        $errorMessage = $this->Flash->render('partner_error');

                        if (!empty($successMessage)) {
                            echo $successMessage;
                        } elseif (!empty($errorMessage)) {
                            echo $errorMessage;
                        }
                        ?>
                    </div>
                </div>
                <div class="card-header">
                    <h4>Edit Partner</h4>
                </div>
                <div class="card-body">

                    <ul class="nav nav-tabs" id="partnerTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="basic-tab" data-bs-toggle="tab" href="#basic" role="tab">Basic Information</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="details-tab" data-bs-toggle="tab" href="#details" role="tab">Details </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="details-tab" data-bs-toggle="tab" href="#addresses_and_more" role="tab">Addresses & More </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="media-tab" data-bs-toggle="tab" href="#media" role="tab">Media</a>
                        </li>
                    
                        <li class="nav-item">
                            <a class="nav-link" id="gst-tab" data-bs-toggle="tab" href="#gst" role="tab">GST</a>
                        </li>
                            <li class="nav-item">
                            <a class="nav-link" id="seo-tab" data-bs-toggle="tab" href="#seo" role="tab">SEO</a>
                        </li>
                    </ul>

                    <?= $this->Form->create($partner, ['id' => 'edit', 'novalidate' => true, 'type' => 'file']); ?>
                    <input type="hidden" id="record_id" value="<?= $partner->id; ?>">
                    
                     <div class="tab-content p-3 border border-top-0" id="partnerTabContent">
                      <!-- Basic Tab -->
                        <div class="tab-pane fade show active" id="basic" role="tabpanel">
                        
                            <!-- Center/Teacher business name -->
                            <div class="form-group row">
                                <label for="name" class="col-sm-2 col-form-label"><?= __("Center/Teacher business name") ?> <sup class="text-danger font-11">*</sup></label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('name', [
                                        'type' => 'text',
                                        'class' => 'form-control',
                                        'label' => false,
                                        'required' => true,
                                        'placeholder' => 'Enter Center/Teacher business name',
                                        'id' => 'name'
                                    ]) ?>
                                </div>
                            </div>

                            <!-- Slug -->
                            <div class="form-group row">
                                <label for="slug" class="col-sm-2 col-form-label">Slug <span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <div class="input-group">
                                        <span class="input-group-text bg-secondary text-white fw-bold" id="partner-id-prefix" style="border-right: 0;">
                                            <?= $partner->id ?>-
                                        </span>
                                        <?php
                                        echo $this->Form->control('slug', [
                                            'type' => 'text',
                                            'class' => 'form-control',
                                            'id' => 'slug',
                                            'placeholder' => __('slug'),
                                            'label' => false,
                                            'required' => false,
                                            'value' => preg_replace('/^\d+-/', '', $partner->slug ?? ''),
                                            'style' => 'border-left: 0;'
                                        ]);
                                        ?>
                                    </div>
                                    <?php
                                    // Hidden field to store full slug
                                    echo $this->Form->control('slug', [
                                        'type' => 'hidden',
                                        'id' => 'partner-slug-hidden'
                                    ]);
                                    ?>
                                </div>
                            </div>

                            <!-- Type -->
                             <div class="form-group row">
                                <label for="type" class="col-sm-2 col-form-label">Type <span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('type', [
                                        'type' => 'select',
                                        'options' => [
                                            'Individual' => 'Individual',
                                            'Organization' => 'Organization'
                                        ],
                                        'empty' => 'Select Type',
                                        'class' => 'form-control form-select',
                                        'label' => false,
                                        'id' => 'type'
                                    ]); ?>
                                </div>
                            </div>

                            <!-- Partner Type -->
                            <div class="form-group row">
                                <label for="partner_type_id" class="col-sm-2 col-form-label "><?= __("Partner Type") ?> <sup
                                        class="text-danger font-11">*</sup> </label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('partner_type_id', [
                                        'type' => 'select',
                                        'options' => $partnetypes,
                                        'empty' => __('Select Partner Type'),
                                        'class' => 'form-control form-select',
                                        'label' => false,
                                        'id' => 'partner_type_id'
                                    ]); ?>
                                </div>
                            </div>

                            <!-- Short Description -->
                            <div class="form-group row">
                                <label for="short_description" class="col-sm-2 col-form-label"><?= __("Short Description") ?> <sup class="text-danger font-11">*</sup></label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('short_description', [
                                        'type' => 'textarea',
                                        'rows' => '2',
                                        'class' => 'form-control',
                                        'label' => false,
                                        'placeholder' => __('Enter Short Description'),
                                        'id' => 'short_description',
                                        'maxlength' => 140,
                                    ]) ?>
                                </div>
                            </div>

                             <!-- First Name -->
                            <!-- <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label "><span><?= __("First Name") ?> </span> <sup
                                        class="text-danger font-11">*</sup></label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('first_name', [
                                        'type' => 'text',
                                        'class' => 'form-control',
                                        'id' => 'first_name',
                                        'placeholder' => __('Enter First Name'),
                                        'label' => false,
                                    ]); ?>
                                </div>
                            </div> -->

                            <!-- Last Name -->
                            <!-- <div class="form-group row">
                                <label for="last_name" class="col-sm-2 col-form-label "><span><?= __("Last Name") ?> </span> <sup
                                        class="text-danger font-11">*</sup></label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('last_name', [
                                        'type' => 'text',
                                        'class' => 'form-control',
                                        'id' => 'last_name',
                                        'placeholder' => __('Enter Last Name'),
                                        'label' => false,
                                    ]); ?>
                                </div>
                            </div> -->

                             <div class="form-group row">
                                <label for="is_featured" class="col-sm-2 col-form-label">Is Featured</label>
                                <div class="col-sm-1 main-field">
                                    <?= $this->Form->checkbox('is_featured', [
                                        'class' => 'form-check-input',
                                        'id' => 'is_featured',
                                    ]); ?>
                                </div>

                                <label for="is_verified" class="col-sm-1 col-form-label">Is Verified</label>
                                <div class="col-sm-1 main-field">
                                    <?= $this->Form->checkbox('is_verified', [
                                        'class' => 'form-check-input',
                                        'id' => 'is_verified',
                                    ]); ?>
                                </div>

                                <label for="is_certified" class="col-sm-1 col-form-label">Is Certified</label>
                                <div class="col-sm-1 main-field">
                                    <?= $this->Form->checkbox('is_certified', [
                                        'class' => 'form-check-input',
                                        'id' => 'is_certified',
                                    ]); ?>
                                </div>
                            </div>
                            
                            <!-- <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Flags</label>
                                <div class="col-sm-10">
                                    <div class="form-check form-check-inline">
                                        <?= $this->Form->checkbox('is_featured', [
                                            'class' => 'form-check-input',
                                            'id' => 'is_featured',
                                        ]); ?>
                                        <label class="form-check-label ms-1" for="is_featured">Is Featured</label>
                                    </div>

                                    <div class="form-check form-check-inline">
                                        <?= $this->Form->checkbox('is_verified', [
                                            'class' => 'form-check-input',
                                            'id' => 'is_verified',
                                        ]); ?>
                                        <label class="form-check-label ms-1" for="is_verified">Is Verified</label>
                                    </div>

                                    <div class="form-check form-check-inline">
                                        <?= $this->Form->checkbox('is_certified', [
                                            'class' => 'form-check-input',
                                            'id' => 'is_certified',
                                        ]); ?>
                                        <label class="form-check-label ms-1" for="is_certified">Is Certified</label>
                                    </div>
                                </div>
                            </div> -->

                           <!-- Operating Months -->
                            <div class="form-group row mt-3 op">
                                <label class="col-sm-2 col-form-label"><?= __('Operating Months') ?></label>
                                <div class="col-sm-10">
                                    <?php
                                    $months = [
                                        'Jan' => 'Jan', 'Feb' => 'Feb', 'Mar' => 'Mar', 'Apr' => 'Apr',
                                        'May' => 'May', 'Jun' => 'Jun', 'Jul' => 'Jul', 'Aug' => 'Aug',
                                        'Sep' => 'Sep', 'Oct' => 'Oct', 'Nov' => 'Nov', 'Dec' => 'Dec'
                                    ];
                                    ?>

                                    <!-- Select All Checkbox -->
                                    <div class="form-check">
                                        <input type="checkbox" id="select-all-months" class="form-check-input">
                                        <label for="select-all-months" class="form-check-label">Select All</label>
                                    </div>

                                    <?= $this->Form->control('operating_months', [
                                        'type' => 'select',
                                        'multiple' => 'checkbox',
                                        'options' => $months,
                                        'label' => false,
                                        'class' => 'form-check',
                                        'required' => false
                                    ]); ?>
                                </div>
                            </div>
                            
                            <!-- Course Types -->
                            <div class="form-group row">
                                <label for="course_type_id" class="col-sm-2 col-form-label">Course Types<span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('course_type_id', [
                                        'type' => 'select',
                                        'options' => $courseTypes,
                                        // 'empty' => __('Select Course Type'),
                                        'class' => 'form-control select2-multi',
                                        'label' => false,
                                        'id' => 'course_type_id',
                                        'multiple' => true
                                    ]); ?>
                                </div>
                            </div>
  
                            <!-- Yoga Styles -->
                            <div class="form-group row">
                                <label for="yoga_style_id" class="col-sm-2 col-form-label">Yoga Styles<span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('yoga_style_id', [
                                        'style'=>'height: 100px',
                                        'type' => 'select',
                                        'id' => 'yoga_style_id',
                                        'options' => $yogaStyles,
                                        'class' => 'form-control select2-multi',
                                        // 'empty' => __('Select Yoga Style'),
                                        'label' => false,
                                        'multiple' => true
                                    ]) ?>
                                </div>
                            </div>

                            <!-- Special Needs -->
                           <div class="form-group row">
                                <label for="special_need_id" class="col-sm-2 col-form-label">Special Needs <span class="required-sign">*</span> </label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('special_need_id', [
                                        'style'=>'height: 100px',
                                        'type' => 'select',
                                        'id' => 'special_need_id',
                                        'options' => $specialNeeds,
                                        'class' => 'form-control select2-multi',
                                        // 'empty' => __('Select Special Need'),
                                        'label' => false,
                                        'multiple' => true
                                    ]) ?>
                                </div>
                            </div>

                            <!-- Technique -->
                            <div class="form-group row">
                                <label for="techniques" class="col-sm-2 col-form-label">Techniques <span class="required-sign">*</span> </label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('techniques', [
                                        // 'style'=>'height: 100px',
                                        'type' => 'select',
                                        'id' => 'techniques',
                                        'options' => $techniques,
                                        'class' => 'form-control select2-multi',
                                        // 'empty' => __('Select Techniques'),
                                        'label' => false,
                                        'multiple' => true
                                    ]) ?>
                                </div>
                            </div>

                            <!-- Level -->
                            <div class="form-group row">
                                <label for="level" class="col-sm-2 col-form-label">Level <span class="required-sign">*</span> </label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('level', [
                                        // 'style'=>'height: 100px',
                                        'type' => 'select',
                                        'id' => 'level',
                                        'options' => $level,
                                        'class' => 'form-control select2-multi',
                                        // 'empty' => __('Select Level'),
                                        'label' => false,
                                        'multiple' => true
                                    ]) ?>
                                </div>
                            </div>

                            <!-- Language -->
                            <div class="form-group row">
                                <label for="language" class="col-sm-2 col-form-label">Language<span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('language', [
                                        'type' => 'select',
                                        'options' => $languages,
                                        // 'empty' => __('Select Language'),
                                        'class' => 'form-control select2-multi',
                                        'label' => false,
                                        'id' => 'language',
                                        'multiple' => true
                                    ]); ?>
                                </div>
                            </div>

                            <!-- Modality -->
                             <div class="form-group row">
                                <label for="modality" class="col-sm-2 col-form-label">Modality</label>
                                 <div class="col-sm-6 main-field">
                                        <?= $this->Form->control('modality', [
                                        'type' => 'select',
                                        'options'=> $modalities,
                                        'value' => $selectedModalities,
                                        'empty' => __('Select Modality'),
                                        'class' => 'select2-multi',
                                        'label' => false,
                                        'multiple' => true
                                    ]); ?>
                                </div>
                            </div>

                            <!-- Description -->
                            <!-- <div class="form-group row">
                                <label for="description" class="col-sm-2 col-form-label"><?= __("Description") ?></label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('description', [
                                        'type' => 'textarea',
                                        'rows' => '2',
                                        'class' => 'form-control',
                                        'label' => false,
                                        'placeholder' => __('Enter Description'),
                                        'id' => 'description'
                                    ]) ?>
                                </div>
                            </div> -->

                             <!-- Logo -->
                            <div class="form-group row">
                                <label for="logo" class="col-sm-2 col-form-label"><?= __("Logo") ?></label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('logo', [
                                        'type' => 'file',
                                        'id' => 'logo',
                                        'name' => 'logo',
                                        'accept' => '.jpg,.jpeg,.png,.webp',
                                        'class' => 'form-control',
                                        'label' => false
                                    ]) ?>
                                    <?php if (!empty($partner->logo)) : ?>
                                        <div class="mb-2">
                                            <img src="<?= $this->Url->build('/uploads/partners/logo/' . $partner->logo) ?>" alt="Logo" width="100">
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Status -->
                            <div class="form-group row">
                                <label for="status" class="col-sm-2 col-form-label"><?= __("Status") ?></label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('status', [
                                        'type' => 'select',
                                        'options' => ['A' => 'Active', 'I' => 'Inactive'],
                                        'empty' => __('Select Status'),
                                        'class' => 'form-control form-select',
                                        'label' => false
                                    ]) ?>
                                </div>
                            </div>
                        </div>

                        <!-- Details Tab -->
                        <div class="tab-pane fade" id="details" role="tabpanel">
                            <div class="form-group row">
                                <label for="desc1_label" class="col-sm-2 col-form-label"><?= __("Description 1 Label") ?></label>
                                <div class="col-sm-8 main-field">
                                    <?php echo $this->Form->control('desc1_label', [
                                        'type' => 'text',
                                        'rows' => '2',
                                        'class' => 'form-control',
                                        'id' => 'desc1_label',
                                        'placeholder' => __('Description 1 Label'),
                                        'label' => false,
                                    ]); ?>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="desc1_text" class="col-sm-2 col-form-label"><?= __("Description 1 Text") ?></label>
                                <div class="col-sm-8 main-field">
                                    <?php echo $this->Form->control('desc1_text', [
                                        'type' => 'textarea',
                                        'rows' => '2',
                                        'class' => 'form-control',
                                        'id' => 'desc1_text',
                                        'placeholder' => __('Description 1 Text'),
                                        'label' => false,
                                    ]); ?>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="desc2label" class="col-sm-2 col-form-label"><?= __("Description 2 Label") ?></label>
                                <div class="col-sm-8 main-field">
                                    <?php echo $this->Form->control('desc2_label', [
                                        'type' => 'text',
                                        'rows' => '2',
                                        'class' => 'form-control',
                                        'id' => 'desc2_label',
                                        'placeholder' => __('Description 2 Label'),
                                        'label' => false,
                                    ]); ?>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="description" class="col-sm-2 col-form-label"><?= __("Description 2 Text") ?></label>
                                <div class="col-sm-8 main-field">
                                    <?php echo $this->Form->control('desc2_text', [
                                        'type' => 'textarea',
                                        'rows' => '2',
                                        'class' => 'form-control',
                                        'id' => 'desc2_text',
                                        'placeholder' => __('Description 2 Text'),
                                        'label' => false,
                                    ]); ?>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="desc3_label" class="col-sm-2 col-form-label"><?= __("Description 3 Label") ?></label>
                                <div class="col-sm-8 main-field">
                                    <?php echo $this->Form->control('desc3_label', [
                                        'type' => 'text',
                                        'rows' => '2',
                                        'class' => 'form-control',
                                        'id' => 'desc3_label',
                                        'placeholder' => __('Description 3 Label'),
                                        'label' => false,
                                    ]); ?>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="desc3_text" class="col-sm-2 col-form-label"><?= __("Description 3 Text") ?></label>
                                <div class="col-sm-8 main-field">
                                    <?php echo $this->Form->control('desc3_text', [
                                        'type' => 'textarea',
                                        'rows' => '2',
                                        'class' => 'form-control',
                                        'id' => 'desc3_text',
                                        'placeholder' => __('Description 3 Text'),
                                        'label' => false,
                                    ]); ?>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="faq" class="col-sm-2 col-form-label"> FAQ</label>
                                <div class="col-sm-8 main-field">
                                    <?php echo $this->Form->control('faq', [
                                        'type' => 'textarea',
                                        'rows' => '2',
                                        'class' => 'form-control',
                                        'id' => 'faq',
                                        'placeholder' => __('FAQ'),
                                        'label' => false,
                                    ]); ?>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="refund_policy" class="col-sm-2 col-form-label"> Refund Policy</label>
                                <div class="col-sm-8 main-field">
                                    <?php echo $this->Form->control('refund_policy', [
                                        'type' => 'textarea',
                                        'rows' => '2',
                                        'class' => 'form-control',
                                        'id' => 'refund_policy',
                                        'placeholder' => __('Refund Policy'),
                                        'label' => false,
                                    ]); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Tab -->
                        <div class="tab-pane fade" id="addresses_and_more" role="tabpanel">
                            <!-- Country -->
                            <div class="form-group row">
                                <label for="country_id" class="col-sm-2 col-form-label"><?= __("Country") ?> <sup class="text-danger font-11">*</sup></label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('country_id', [
                                        'type' => 'select',
                                        'options' => $countries,
                                        'empty' => __('Select Country'),
                                        'class' => 'form-control form-select',
                                        'label' => false,
                                        'id' => 'country_id'
                                    ]) ?>
                                </div>
                            </div>

                            <!-- State -->
                            <div class="form-group row">
                                <label for="state_id" class="col-sm-2 col-form-label"><?= __("State") ?> <sup class="text-danger font-11">*</sup></label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('state_id', [
                                        'type' => 'select',
                                        'options' => $states,
                                        'empty' => __('Select State'),
                                        'class' => 'form-control form-select',
                                        'label' => false,
                                        'id' => 'state_id'
                                    ]) ?>
                                </div>
                            </div>

                            <!-- City -->
                            <div class="form-group row">
                                <label for="city_id" class="col-sm-2 col-form-label"><?= __("City") ?> <sup class="text-danger font-11">*</sup></label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('city_id', [
                                        'type' => 'select',
                                        'options' => $cities,
                                        'empty' => __('Select City'),
                                        'class' => 'form-control form-select',
                                        'label' => false,
                                        'id' => 'city_id'
                                    ]) ?>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Locality</label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('locality_id', [
                                        'type' => 'select',
                                        'options' => $localities,
                                        'empty' => 'Select Locality',
                                        'class' => 'form-control form-select',
                                        'label' => false,
                                        'id' => 'locality_id'
                                    ]) ?>
                                </div>
                            </div>
                            
                            <!-- <div class="form-group row">
                                <label for="is_main_address" class="col-sm-2 col-form-label">Is Main Address</label>
                                <div class="col-sm-6 main-field d-flex align-items-center">
                                    <?php
                                    echo $this->Form->radio('is_main_address', [
                                        ['value' => 1, 'text' => 'Yes'],
                                        ['value' => 0, 'text' => 'No']
                                    ], [
                                        'between' => '',
                                        'separator' => '',
                                        'label' => ['class' => 'form-check-label mb-0 ms-1 me-4 align-middle'],
                                        'class' => 'form-check-input mt-0 align-middle me-1',
                                        'legend' => false,
                                        'div' => ['class' => 'form-check form-check-inline align-items-center']
                                    ]);
                                    ?>
                                </div>
                            </div> -->

                            <!-- Address 1 -->
                            <!-- <div class="form-group row">
                                <label for="address1" class="col-sm-2 col-form-label"><?= __("Address 1") ?> </label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('address1', [
                                        'type' => 'textarea',
                                        'rows' => '2',
                                        'class' => 'form-control',
                                        'label' => false,
                                        'id' => 'address1',
                                        'placeholder' => 'Enter Address 1'
                                    ])
                                    ?>
                                </div>
                            </div> -->

                            <!-- Address 2 -->
                            <!-- <div class="form-group row">
                                <label for="address2" class="col-sm-2 col-form-label"><?= __("Address 2") ?></label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('address2', [
                                        'type' => 'textarea',
                                        'rows' => '2',
                                        'class' => 'form-control',
                                        'label' => false,
                                        'id' => 'address2',
                                        'placeholder' => 'Enter Address 2'
                                    ])
                                    ?>
                                </div>
                            </div> -->

                            <!-- Address -->
                            <div class="form-group row">
                                <label for="address" class="col-sm-2 col-form-label"><?= __("Address") ?> <sup
                                        class="text-danger font-11">*</sup> </label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('address', [
                                        'type' => 'textarea',
                                        'rows' => '2',
                                        'class' => 'form-control',
                                        'label' => false,
                                        'id' => 'address',
                                        'placeholder' => 'Enter Address'
                                    ])
                                    ?>
                                </div>
                            </div>

                            <!-- Zipcode -->
                            <div class="form-group row">
                                <label for="zipcode" class="col-sm-2 col-form-label"><?= __("Zipcode") ?> <sup
                                        class="text-danger font-11">*</sup> </label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('zipcode', ['type' => 'text', 'class' => 'form-control', 'label' => false, 'id' => 'zipcode', 'placeholder' => 'Enter Zipcode']) ?>
                                </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-md-4">
                                    <label for="area" class="col-sm-4 col-form-label"><?= __("Search Your Area") ?> </label>
                                    <input type="text" class="form-control" name='area' id="area" placeholder="Enter Your Area" value="<?= h($partner->area) ?>">
                                </div>
                                <div class="col-md-4">
                                    <label for="latitude" class="col-sm-3 col-form-label"><?= __("Latitude") ?> <sup class="text-danger font-11">*</sup></label>
                                    <input type="text" name="latitude" class="form-control" id="latitude" placeholder="Enter Latitude"
                                        value="<?= h($partner->latitude) ?>" step="any" min="-90" max="90">
                                </div>
                                <div class="col-md-4">
                                    <label for="longitude" class="col-sm-3 col-form-label"><?= __("Longitude") ?> <sup class="text-danger font-11">*</sup></label>
                                    <input type="text" name="longitude" class="form-control" id="longitude" placeholder="Enter Longitude"
                                        value="<?= h($partner->longitude) ?>" step="any" min="-180" max="180">
                                </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-md-12">
                                    <div id="map"></div>
                                </div>
                            </div>


                            <!-- Phone -->
                            <div class="form-group row">
                                <label for="phone" class="col-sm-2 col-form-label"><?= __('Phone') ?> <sup class="text-danger font-11">*</sup></label>
                                <div class="col-sm-6 main-field">
                                    <input id="country_code" type="hidden" name="country_code" value="<?= h($partner->country_code ?? '') ?>">
                                    <?= $this->Form->control('phone', [
                                        'type' => 'tel',
                                        'class' => 'form-control tel',
                                        'id' => 'phone',
                                        'placeholder' => __('Enter Phone Number'),
                                        'label' => false
                                    ]) ?>
                                </div>
                            </div>

                            <!-- Email -->
                            <div class="form-group row">
                                <label for="email" class="col-sm-2 col-form-label"><?= __("Email") ?> <sup class="text-danger font-11">*</sup></label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('email', [
                                        'type' => 'email',
                                        'class' => 'form-control',
                                        'label' => false,
                                        'placeholder' => __('Enter Email'),
                                        'id' => 'email'
                                    ]) ?>
                                    <small id="email-error" class="invalid-feedback" style="display: none;"></small>
                                </div>
                            </div>

                            <!-- Website -->
                            <div class="form-group row">
                                <label for="website" class="col-sm-2 col-form-label"><?= __("Website") ?></label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('website', ['type' => 'text', 'class' => 'form-control', 'label' => false, 'placeholder' => __('Enter Website')]) ?>
                                </div>
                            </div>

                            <!-- Social Links -->
                            <?php
                            $social = [
                                'whatsapp' => 'WhatsApp',
                                'facebook_url' => 'Facebook URL',
                                'instagram_url' => 'Instagram URL',
                                'youtube_url' => 'YouTube URL',
                                'x_url' => 'X URL',
                                'telegram_url' => 'Telegram URL'
                            ];
                            foreach ($social as $field => $label) :
                            ?>
                                <div class="form-group row">
                                    <label for="<?= $field ?>" class="col-sm-2 col-form-label"><?= __($label) ?></label>
                                    <div class="col-sm-6 main-field">
                                        <?= $this->Form->control($field, [
                                            'type' => 'text',
                                            'class' => 'form-control',
                                            'label' => false,
                                            'placeholder' => __('Enter ') . $label,
                                            'id' => $field
                                        ]) ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <!-- Media Tab -->
                        <div class="tab-pane fade" id="media" role="tabpanel" aria-labelledby="media-tab">
                            <!-- Multiple Images -->
                            <div class="form-group row">
                                <label for="media" class="col-sm-2 col-form-label "><?= __("Upload Media") ?></label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('image[]', [
                                        'type' => 'file',
                                        'class' => 'form-control',
                                        'label' => false,
                                        'multiple' => 'multiple',
                                        'id' => 'imageInput',
                                        'accept' => 'image/*,video/*',
                                        'placeholder' => __("Upload images and videos")
                                    ]); ?>
                                    <small class="text-muted"><?= __("Only .jpg, .jpeg, .png, .gif, .webp, .mp4, .mov, .mkv, .avi files are allowed. Max size: 2MB") ?></small>
                                    <div id="previewContainer">
                                        <ul id="imagePreviewContainer" class="list-unstyled d-flex flex-wrap gap-2 mt-2"></ul>
                                        <?= $this->Form->hidden('deletedImages', ['id' => 'deletedImagesInput', 'value' => '']); ?>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="video_link_1" class="col-sm-2 col-form-label"><?= __("Video Link 1") ?></label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('video_link_1', [
                                        'type' => 'text',
                                        'class' => 'form-control',
                                        'id' => 'video_link_1',
                                        'placeholder' => __('Enter Video Link 1'),
                                        'label' => false,
                                    ]); ?>
                                </div>
                            </div>

                             <div class="form-group row">
                                <label for="video_link_2" class="col-sm-2 col-form-label"><?= __("Video Link 2") ?></label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('video_link_2', [
                                        'type' => 'text',
                                        'class' => 'form-control',
                                        'id' => 'video_link_2',
                                        'placeholder' => __('Enter Video Link 2'),
                                        'label' => false,
                                    ]); ?>
                                </div>
                            </div>

                             <div class="form-group row">
                                <label for="video_link_3" class="col-sm-2 col-form-label"><?= __("Video Link 3") ?></label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('video_link_3', [
                                        'type' => 'text',
                                        'class' => 'form-control',
                                        'id' => 'video_link_3',
                                        'placeholder' => __('Enter Video Link 3'),
                                        'label' => false,
                                    ]); ?>
                                </div>
                            </div>
                            
                            <!-- <div class="form-group row align-items-start">
                                <label for="video_link" class="col-sm-2 col-form-label">Video Link</label>
                                <div class="col-sm-6">
                                    <div id="video-links-wrapper">
                                        <?php if (!empty($videoUrls)) : ?>
                                            <?php foreach ($videoUrls as $i => $url): ?>
                                                <div class="video-link-group mb-2 d-flex gap-2">
                                                    <input type="text" name="video_url[]" class="form-control video-link-input" placeholder="Video Link <?= $i + 1 ?>" value="<?= h($url) ?>">
                                                </div>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <div class="video-link-group mb-2 d-flex gap-2">
                                                <input type="text" name="video_url[]" class="form-control video-link-input" placeholder="Video Link 1">
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-sm-2">
                                    <button type="button" id="add-video-link" class="btn btn-secondary btn-sm w-100">Add More</button>
                                </div>
                            </div> -->

                        </div>

                        <!-- GST Tab -->
                        <div class="tab-pane fade" id="gst" role="tabpanel" aria-labelledby="gst-tab">
                            <!-- Tax Section: Domestic -->
                            <div class="form-group row align-items-center mb-3">
                                <div class="col-sm-4">
                                    <div class="form-check">
                                        <?= $this->Form->control('is_tax_domestic', [
                                            'type' => 'checkbox',
                                            'class' => 'form-check-input tax-toggle',
                                            'id' => 'is_tax_domestic',
                                            'label' => false,
                                            'data-target' => 'domestic_tax_percentage',
                                            'checked' => !empty($partner->is_tax_domestic),
                                        ]); ?>

                                        <label class="form-check-label" for="is_tax_domestic">
                                            Is Tax Applicable for Domestic Bookings
                                        </label>
                                    </div>
                                </div>
                                
                                <label class="col-sm-2 col-form-label">Tax Percentage</label>
                                <div class="col-sm-2">
                                    <?= $this->Form->control('domestic_tax_percentage', [
                                        'type' => 'select',
                                        'options' => $domesticTaxPercent,
                                        'empty' => __('Select'),
                                        'class' => 'form-control form-select',
                                        'label' => false,
                                        'id' => 'domestic_tax_percentage',
                                        'disabled' => true,
                                    ]); ?>
                                </div>
                            </div>

                            <!-- Tax Section: International -->
                            <div class="form-group row align-items-center mb-3">
                                <div class="col-sm-4">
                                    <div class="form-check">
                                         <?= $this->Form->control('is_tax_international', [
                                            'type' => 'checkbox',
                                            'class' => 'form-check-input tax-toggle',
                                            'id' => 'is_tax_international',
                                            'label' => false,
                                            'data-target' => 'international_tax_percentage',
                                            'checked' => !empty($partner->is_tax_international),
                                        ]); ?>
                                        
                                        <label class="form-check-label" for="is_tax_international">
                                            Is Tax Applicable for International Customers
                                        </label>
                                    </div>
                                </div>

                                <label class="col-sm-2 col-form-label">Tax Percentage</label>
                                <div class="col-sm-2">
                                     <?= $this->Form->control('international_tax_percentage', [
                                        'type' => 'select',
                                        'options' => $internationalTaxPercent,
                                        'empty' => __('Select'),
                                        'class' => 'form-control form-select',
                                        'label' => false,
                                        'id' => 'international_tax_percentage',
                                        'disabled' => true
                                    ]); ?>
                                </div>
                            </div>

                            <!-- Tax Number row -->
                            <div class="form-group row mb-3">
                                <label class="col-sm-2 offset-sm-4 col-form-label">Tax Number</label>
                                <div class="col-sm-4">
                                    <?= $this->Form->control('tax_number', [
                                        'id' => 'tax_number',
                                        'class' => 'form-control',
                                        'label' => false,
                                        'placeholder' => 'Tax Number',
                                        'pattern' => '[a-zA-Z0-9]+',
                                        'title' => 'Only alphanumeric characters allowed'
                                    ]); ?>

                                </div>
                            </div>
                        </div>

                         <!-- SEO Tab -->
                        <div class="tab-pane fade" id="seo" role="tabpanel">
                           <?= $this->element('Admin/Courses/add_seo_data') ?>
                        </div>
                    </div>

                    <div class="text-end mt-3">
                        <button name="action" value="save" class="btn btn-secondary submitBtn">Save</button>
                        <button name="action" value="validate" class="btn btn-primary submitBtn">Validate & Save</button>
                    </div>
                    <?= $this->Form->end(); ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/ckeditor/ckeditor.js') ?>"></script>
<script>
    // $(function() {
    //     CKEDITOR.replace("description");
    //     CKEDITOR.config.height = 300;
    //     for (instance in CKEDITOR.instances) {
    //         CKEDITOR.instances[instance].on('blur', function() {
    //             CKEDITOR.instances[this.name].updateElement();
    //         });
    //     }
    // });

     $(function() {
            CKEDITOR.replace("desc1_text");
            CKEDITOR.config.height = 300;
            for (instance in CKEDITOR.instances) {
                CKEDITOR.instances[instance].on('blur', function() {
                    CKEDITOR.instances[this.name].updateElement();
                });
            }
        });

        $(function() {
            CKEDITOR.replace("desc2_text");
            CKEDITOR.config.height = 300;
            for (instance in CKEDITOR.instances) {
                CKEDITOR.instances[instance].on('blur', function() {
                    CKEDITOR.instances[this.name].updateElement();
                });
            }
        });
        
        $(function() {
            CKEDITOR.replace("desc3_text");
            CKEDITOR.config.height = 300;
            for (instance in CKEDITOR.instances) {
                CKEDITOR.instances[instance].on('blur', function() {
                    CKEDITOR.instances[this.name].updateElement();
                });
            }
        });

</script>
<script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/jquery.validate.min.js"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script src="<?= $this->Url->webroot('bundles/ckeditor/ckeditor.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script>

    function updateTaxUI() {
        document.querySelectorAll('.tax-toggle').forEach(function (checkbox) {
            const targetId = checkbox.dataset.target;
            const dropdown = document.getElementById(targetId);

            if (checkbox.checked) {
                dropdown.disabled = false;
            } else {
                dropdown.disabled = true;
                dropdown.value = ""; // Reset to default if unchecked
            }
        });
    }

    // Run on load and on checkbox change
    document.addEventListener('DOMContentLoaded', function () {
        updateTaxUI(); // Initial check on page load
        document.querySelectorAll('.tax-toggle').forEach(function (checkbox) {
            checkbox.addEventListener('change', updateTaxUI);
        });
    });


    $(document).ready(function() {

        var inputFax = document.querySelector("#phone");
        var itiFax = window.intlTelInput(inputFax, {
            initialCountry: "IN", // 🇮🇳 Default to India
            separateDialCode: true,
            preferredCountries: ["IN", "QA", "US", "GB"],
            utilsScript: "<?= $this->Url->webroot('bundles/intlTelInput/js/utils.js') ?>"
        });

        function updateFaxCountryCode() {
            $('#country_code').val(itiFax.getSelectedCountryData().dialCode);
        }

        $('#country_code').val(itiFax.getSelectedCountryData().dialCode);
        inputFax.addEventListener("countrychange", updateFaxCountryCode);

        $.validator.addMethod('checkDuplicatePhone', function(value, element) {
            let isValid = false;
            var country_code = $('#country_code').val();
            var recordId = $('#record_id').val();
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'Partners', 'action' => 'checkDuplicatePhone']) ?>",
                method: 'POST',
                dataType: 'json',
                data: {
                    phone_number: value,
                    country_code: country_code,
                    id: recordId,
                },
                async: false,
                headers: {
                    'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                success: function(response) {
                    isValid = !response.isDuplicate;
                },
                error: function() {
                    isValid = false;
                }
            });

            return isValid;
        }, 'This phone number is already in use. Please enter another one.');

        $.validator.addMethod('checkDuplicateEmail', function(value, element) {
            const recordId = $('#record_id').val();
            let isValid = false;
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'Partners', 'action' => 'checkDuplicateEmail']) ?>",
                method: 'POST',
                dataType: 'json',
                data: {
                    email: value,
                    id: recordId,
                },
                async: false,
                headers: {
                    'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                success: function(response) {
                    isValid = !response.isDuplicate;
                },
                error: function() {
                    isValid = false;
                }
            });

            return isValid;
        }, ' This email is already in use. Please enter another one.');

        // Validate latitude: decimal(10,8)
        $.validator.addMethod("validLatitude", function(value, element) {
            return this.optional(element) || /^-?\d{1,2}\.\d{1,8}$/.test(value);
        }, "Please enter a valid latitude (max 2 digits before decimal and 8 digits after).");

        // Validate longitude: decimal(11,8)
        $.validator.addMethod("validLongitude", function(value, element) {
            return this.optional(element) || /^-?\d{1,3}\.\d{1,8}$/.test(value);
        }, "Please enter a valid longitude (max 3 digits before decimal and 8 digits after).");

         // GSTIN Format: 22ABCDE1234F1Z5
        $.validator.addMethod("validGstNumber", function (value, element) {
            return this.optional(element) || /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/.test(value);
        }, "Invalid GSTIN format. Example: 22ABCDE1234F1Z5");

        // International Tax Number: Alphanumeric with dashes, 5–20 characters
        $.validator.addMethod("validInternationalTax", function (value, element) {
            return this.optional(element) || /^[A-Z0-9\-]{5,20}$/i.test(value);
        }, "Invalid International Tax Number. Use 5–20 alphanumeric characters or hyphens.");

        $("#edit").validate({
            ignore: "",
            rules: {
                'name': {
                    required: true
                },
                'slug': {
                    required: true
                },
                'type': {
                    required: true
                },
                // 'first_name': {
                //     required: true
                // },
                // 'last_name': {
                //     required: true
                // },
                'course_type_id[]': {
                    required: true
                },
                'yoga_style_id[]': {
                    required: true
                },
                'special_need_id[]': {
                    required: true
                },
                'techniques[]': {
                    required: true
                },
                'level[]': {
                    required: true
                },
                'language[]': {
                    required: true
                },
                'short_description': {
                    required: true
                },
                'email': {
                    required: true,
                    email: true,
                    checkDuplicateEmail: true
                },
                'phone': {
                    required: true,
                    validPhoneFax: true,
                    checkDuplicatePhone: true
                },
                'country_id': {
                    required: true
                },
                'state_id': {
                    required: true
                },
                'city_id': {
                    required: true
                },
                'address': {
                    required: true
                },
                'zipcode': {
                    required: true
                },
                'partner_type_id': {
                    required: true
                },
                'latitude': {
                    required: true,
                    validLatitude: true
                },
                'longitude': {
                    required: true,
                    validLongitude: true
                },
                'tax_number': {
                    required: function () {
                        return $('#is_tax_domestic').is(':checked') || $('#is_tax_international').is(':checked');
                    },
                    validGstNumber: {
                        depends: function () {
                            return $('#is_tax_domestic').is(':checked');
                        }
                    },
                    validInternationalTax: {
                        depends: function () {
                            return $('#is_tax_international').is(':checked');
                        }
                    }
                }
            },
            messages: {
                'name': {
                    required: "<?= __('Please enter center/teacher business name') ?>"
                },
                'slug': {
                    required: "<?= __('Please enter slug') ?>"
                },
                'type': {
                    required: "<?= __('Please select type') ?>"
                },
                // 'first_name': {
                //     required: "<?= __("Please enter first name") ?>",
                // },
                // 'last_name': {
                //     required: "<?= __("Please enter last name") ?>",
                // },
                'course_type_id[]': {
                    required: "<?= __("Please select course type") ?>",
                },
                'yoga_style_id[]': {
                    required: "<?= __("Please select yoga styles") ?>",
                },
                'special_need_id[]': {
                    required: "<?= __("Please select special needs") ?>",
                },
                'techniques[]': {
                    required: "<?= __("Please select techniques") ?>",
                },
                'level[]': {
                    required: "<?= __("Please select level") ?>",
                },
                'language[]': {
                    required: "<?= __("Please select language") ?>",
                },
                'short_description': {
                    required: "<?= __("Please enter short description") ?>",
                },
                'email': {
                    required: "<?= __('Please enter email') ?>",
                    email: "<?= __('Please enter a valid email address') ?>",
                    checkDuplicateEmail: "<?= __('This email is already taken.') ?>"
                },
                'phone': {
                    required: "<?= __('Please enter phone number') ?>",
                    checkDuplicatePhone: "<?= __('This phone number is already in use. Please enter another one.') ?>"
                },
                'country_id': {
                    required: "<?= __('Please select country') ?>"
                },
                'state_id': {
                    required: "<?= __('Please select state') ?>"
                },
                'city_id': {
                    required: "<?= __('Please select city') ?>"
                },
                'address': {
                    required: "<?= __('Please enter address') ?>"
                },
                'zipcode': {
                    required: "<?= __('Please enter zipcode') ?>"
                },
                'partner_type_id': {
                    required: "<?= __('Please select partner type') ?>"
                },
                'latitude': {
                    required: "<?= __('Please enter latitude') ?>",
                    validLatitude: "<?= __('Latitude must be in correct decimal format like 28.6139') ?>"
                },
                'longitude': {
                    required: "<?= __('Please enter longitude') ?>",
                    validLongitude: "<?= __('Longitude must be in correct decimal format like 77.20902') ?>"
                },
                'tax_number': {
                    required: "Please enter Tax Number",
                    validGstNumber: "Please enter a valid GST Number (e.g., 22AAAAA0000A1Z5)",
                    validInternationalTax: "Please enter a valid International Tax Number"
                }
            },
            submitHandler: function(form) {
                $('button[type="submit"]').attr('disabled', 'disabled');

                for (instance in CKEDITOR.instances) {
                    CKEDITOR.instances[instance].updateElement();
                }

                form.submit();
            },

            invalidHandler: function (event, validator) {
                if (validator.errorList.length > 0) {
                    const firstInvalidEl = validator.errorList[0].element;
                    const tabPane = firstInvalidEl.closest('.tab-pane');

                    if (tabPane && tabPane.id) {
                        const tabTriggerEl = document.querySelector(`[data-bs-toggle="tab"][href="#${tabPane.id}"]`);
                        if (tabTriggerEl) {
                            const tab = new bootstrap.Tab(tabTriggerEl);
                            tab.show();
                        }
                    }
                }
            },
            errorPlacement: function(error, element) {
                if (element.hasClass('select2-hidden-accessible')) {
                    // For Select2 fields, insert after the Select2 container
                    error.insertAfter(element.next('.select2'));
                } else if (element.closest(".main-field").length) {
                    // For all other inputs inside .main-field
                    error.appendTo(element.closest(".main-field"));
                } else {
                    // Fallback (e.g., for tax_number outside form group)
                    error.insertAfter(element);
                }
            }

        });

        $('#country_id').change(function() {
            var countryId = $(this).val();
            $('#state_id').html('<option value="">Loading...</option>');
            $('#city_id').html('<option value="">Select City</option>');

            $.ajax({
                url: "<?= $this->Url->build(['action' => 'getStates']) ?>",
                data: {
                    country_id: countryId
                },
                success: function(response) {
                    var options = '<option value="">Select State</option>';
                    $.each(response.states, function(key, value) {
                        options += `<option value="${value.id}">${value.name}</option>`;
                    });
                    $('#state_id').html(options);
                }
            });
        });

        $('#state_id').change(function() {
            var stateId = $(this).val();
            $('#city_id').html('<option value="">Loading...</option>');

            $.ajax({
                url: "<?= $this->Url->build(['action' => 'getCities']) ?>",
                data: {
                    state_id: stateId
                },
                success: function(response) {
                    var options = '<option value="">Select City</option>';
                    $.each(response.cities, function(key, value) {
                        options += `<option value="${value.id}">${value.name}</option>`;
                    });
                    $('#city_id').html(options);
                }
            });
        });

        $('#city_id').change(function() {
            var cityId = $(this).val();
            $('#locality_id').html('<option value="">Loading...</option>');

            $.ajax({
                url: "<?= $this->Url->build(['action' => 'getLocalities']) ?>",
                data: { city_id: cityId },
                success: function(response) {
                    var options = '<option value="">Select Locality</option>';
                    $.each(response.localities, function(key, value) {
                        options += `<option value="${value.id}">${value.name}</option>`;
                    });
                    $('#locality_id').html(options);
                }
            });
        });

        $('#phone').on('input', function(e) {
            this.value = this.value.replace(/[^0-9]/g, '');
        });

        // var inputFax = document.querySelector("#phone");
        // var itiFax = window.intlTelInput(inputFax, {
        //     separateDialCode: true,
        //     preferredCountries: ["IN", "QA", "US", "GB"],
        //     utilsScript: "<?= $this->Url->webroot('bundles/intlTelInput/js/utils.js') ?>"
        // });
        // var dialCode = $('#country_code').val();
        // itiFax.setNumber('+' + dialCode + inputFax.value);

        // function updateFaxCountryCode() {
        //     var countryData = itiFax.getSelectedCountryData();
        //     var countryCode = countryData.dialCode;
        //     var countryCodeText = `+${countryCode} `;
        //     $('#country_code').val(itiFax.getSelectedCountryData().dialCode);
        // }
        // inputFax.addEventListener("countrychange", updateFaxCountryCode);

        $.validator.addMethod("validPhoneFax", function(value, element) {
            return itiFax.isValidNumber();
        }, "Please enter a valid phone");

        // Add this method for zipcode validation
        $.validator.addMethod("zipcodeFormat", function(value, element) {
            // This regex allows 5-6 digits only
            return this.optional(element) || /^[0-9]{5,6}$/.test(value);
        }, "Please enter a valid zipcode (5-6 digits only)");

        // Add this to prevent non-numeric input
        $('#zipcode').on('input', function(e) {
            this.value = this.value.replace(/[^0-9]/g, '');
        });

        // Add this to prevent non-numeric input for latitude and longitude
        $('#latitude, #longitude').on('input', function(e) {
            // Allow only numbers, decimal point, and minus sign
            this.value = this.value.replace(/[^0-9.-]/g, '');

            // Ensure only one decimal point
            var parts = this.value.split('.');
            if (parts.length > 2) {
                this.value = parts[0] + '.' + parts.slice(1).join('');
            }

            // Ensure minus sign is only at the beginning
            if (this.value.indexOf('-') > 0) {
                this.value = this.value.replace(/-/g, '');
                this.value = '-' + this.value;
            }

            // Remove multiple minus signs
            if ((this.value.match(/-/g) || []).length > 1) {
                this.value = this.value.replace(/-/g, '');
                this.value = '-' + this.value;
            }
        });

        // If 'N/A' is selected along with other options, it keeps only 'N/A' selected.
        function handleNAExclusive(selectId) {
            const select = document.getElementById(selectId);
            if (!select) return;
            
            // For Select2, listen to its change event
            const eventName = $(select).hasClass('select2-multi') ? 'select2:select select2:unselect' : 'change';

            $(select).on(eventName, function () {
                const options = Array.from(select.options);
                const selected = options.filter(opt => opt.selected).map(opt => opt.value);
                const naOption = options.find(opt => opt.text.trim().toLowerCase() === 'n/a');
                const naValue = naOption ? naOption.value : null;

                if (!naValue) return;

                // If N/A is selected with others, keep only N/A
                if (selected.includes(naValue) && selected.length > 1) {
                    options.forEach(opt => opt.selected = false);
                    naOption.selected = true;
                    if ($(select).hasClass('select2-multi')) {
                        $(select).val([naValue]).trigger('change.select2');
                    }
                }
            });
        }

        handleNAExclusive('yoga_style_id');
        handleNAExclusive('special_need_id');
        handleNAExclusive('techniques');
        handleNAExclusive('level');

    });

    let allFiles = [];

    document.getElementById('imageInput').addEventListener('change', function(event) {
        let newFiles = Array.from(event.target.files);

        const allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'mp4', 'mov', 'mkv', 'avi'];
        const maxSizeMB = 2;
        const maxSizeBytes = maxSizeMB * 1024 * 1024;
        let invalidFiles = [];

        newFiles.forEach(file => {
            let extension = file.name.split('.').pop().toLowerCase();
            let fileSize = file.size;

            if (!allowedExtensions.includes(extension)) {
                invalidFiles.push({
                    file: file.name,
                    reason: '<?= __('Invalid file type. Only .jpg, .jpeg, .png, .gif,.webp ,.mp4, .mov, .mkv, .avi files are allowed.') ?>'
                });
            } else if (fileSize > maxSizeBytes) {
                invalidFiles.push({
                    file: file.name,
                    reason: `<?= __('File size exceeds the maximum limit of 2MB.') ?>`
                });
            }
        });

        if (invalidFiles.length > 0) {
            let html = "<ul>";
            invalidFiles.forEach(function(invalidFile) {
                html += `<li>${invalidFile.file} - ${invalidFile.reason}</li>`;
            });
            html += '</ul>';

            const wrapper = document.createElement('div');
            wrapper.innerHTML = html;

            swal({
                title: "<?= __("Invalid Files") ?>",
                content: wrapper,
                confirmButtonText: "<?= __("OK") ?>",
                allowOutsideClick: true
            });
            document.getElementById('imageInput').value = "";
            return;
        }

        allFiles = [...allFiles, ...newFiles];
        renderPreviews();
        updateFileInput();
    });

    
    // document.getElementById('add-video-link').addEventListener('click', function () {
    //     const wrapper = document.getElementById('video-links-wrapper');

    //     const newGroup = document.createElement('div');
    //     newGroup.className = 'video-link-group mb-2 d-flex gap-2';

    //     newGroup.innerHTML = `
    //         <input type="text" name="video_url[]" class="form-control video-link-input" placeholder="Video Link ${videoIndex}">
    //         <button type="button" class="btn btn-danger btn-sm remove-video-link">Remove</button>
    //     `;

    //     wrapper.appendChild(newGroup);
    //     videoIndex++;
    // });

    function renderPreviews() {
        let previewContainer = document.getElementById('imagePreviewContainer');
        previewContainer.innerHTML = '';
        allFiles.forEach((file, index) => {
            let li = document.createElement('li');
            li.classList.add('media-thumbnail', 'position-relative');
            let fileName = file.name;
            let extension = fileName.split('.').pop().toLowerCase();
            let isImage = ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension);
            let isVideo = ['mp4', 'mov', 'mkv', 'avi'].includes(extension);

            let shortName = fileName.length > 20 ? fileName.slice(0, 17) + '...' : fileName;

            let reader = new FileReader();
            reader.onload = function(e) {
                if (isImage) {
                    li.innerHTML = `
                        <img src="${e.target.result}" alt="<?= __("Image Preview") ?>" class="preview-img" style="width: 120px; height: 100px; object-fit: cover;" />
                        <span class="image-name d-block text-truncate" title="${fileName}">${shortName}</span>
                        <button type="button" class="delete-img-btn btn btn-sm btn-danger position-absolute top-0 end-0" data-index="${index}">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                } else if (isVideo) {
                    li.innerHTML = `
                        <video src="${e.target.result}" class="preview-img" controls style="width: 120px; height: 100px; object-fit: cover;"></video>
                        <span class="image-name d-block text-truncate" title="${fileName}">${shortName}</span>
                        <button type="button" class="delete-img-btn btn btn-sm btn-danger position-absolute top-0 end-0" data-index="${index}">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                }
            };
            reader.readAsDataURL(file);
            previewContainer.appendChild(li);
        });
    }

    document.getElementById('imagePreviewContainer').addEventListener('click', function(e) {
        if (e.target.closest('.delete-img-btn')) {
            let index = e.target.closest('.delete-img-btn').getAttribute('data-index');
            allFiles.splice(index, 1);
            renderPreviews();
            updateFileInput();
        }
    });

    function updateFileInput() {
        let dataTransfer = new DataTransfer();
        allFiles.forEach(file => dataTransfer.items.add(file));
        document.getElementById('imageInput').files = dataTransfer.files;
    }

    // document.addEventListener('DOMContentLoaded', function () {
    //     let videoIndex = 2;

    //     document.getElementById('add-video-link').addEventListener('click', function () {
    //         const wrapper = document.getElementById('video-links-wrapper');

    //         const newGroup = document.createElement('div');
    //         newGroup.className = 'video-link-group mb-2 d-flex gap-2';

    //         newGroup.innerHTML = `
    //             <input type="text" name="video_url[]" class="form-control video-link-input" placeholder="Video Link ${videoIndex}">
    //             <button type="button" class="btn btn-danger btn-sm remove-video-link">Remove</button>
    //         `;

    //         wrapper.appendChild(newGroup);
    //         videoIndex++;
    //     });

    //     document.getElementById('video-links-wrapper').addEventListener('click', function (e) {
    //         if (e.target && e.target.classList.contains('remove-video-link')) {
    //             e.target.closest('.video-link-group').remove();
    //         }
    //     });
    // });

    document.addEventListener('DOMContentLoaded', function() {
        // Map initialization and logic
        setTimeout(function() {
            // Ensure the map container is visible before initializing
            var map = L.map('map').setView([20.5937, 78.9629], 5); // Center of India

            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                maxZoom: 19,
                attribution: '&copy; OpenStreetMap contributors'
            }).addTo(map);

            let marker = null;

            function setMarker(lat, lon, city = '') {
                map.setView([lat, lon], 14);
                if (marker) {
                    marker.setLatLng([lat, lon]);
                } else {
                    marker = L.marker([lat, lon]).addTo(map);
                }
                marker.bindPopup('<b>' + (city ? city : 'Selected Location') + '</b><br>Lat: ' + lat + '<br>Lon: ' + lon).openPopup();
                document.getElementById('latitude').value = lat;
                document.getElementById('longitude').value = lon;
            }

            function searchCity(city) {
                if (!city || city.length < 2) return;
                fetch('https://nominatim.openstreetmap.org/search?city=' + encodeURIComponent(city) + '&format=json')
                    .then(res => res.json())
                    .then(function(data) {
                        if (data.length > 0) {
                            const lat = parseFloat(data[0].lat);
                            const lon = parseFloat(data[0].lon);
                            setMarker(lat, lon, city);
                        }
                    })
                    .catch(err => console.error(err));
            }

            document.getElementById('city').addEventListener('input', function() {
                const city = this.value;
                clearTimeout(debounceTimeout);
                debounceTimeout = setTimeout(function() {
                    searchCity(city);
                }, 700); // 700ms debounce
            });

            document.getElementById('city').addEventListener('click', function() {
                const city = this.value;
                if (city && city.length > 1) {
                    searchCity(city);
                }
            });

            // Update map when lat/lng fields are changed manually
            document.getElementById('latitude').addEventListener('change', function() {
                const lat = parseFloat(this.value);
                const lng = parseFloat(document.getElementById('longitude').value);
                if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {
                    setMarker(lat, lng);
                }
            });
            document.getElementById('longitude').addEventListener('change', function() {
                const lng = parseFloat(this.value);
                const lat = parseFloat(document.getElementById('latitude').value);
                if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {
                    setMarker(lat, lng);
                }
            });

            // Optionally, update lat/lng fields when marker is dragged (if you want draggable marker)
            // marker.on('dragend', function(e) { ... });

        }, 0); // Ensure this runs after DOM is ready and map container is visible
    });

    // check all months
    document.addEventListener('DOMContentLoaded', function () {
        const selectAll = document.getElementById('select-all-months');
        const monthCheckboxes = document.querySelectorAll('input[name="operating_months[]"]');

        selectAll.addEventListener('change', function () {
            monthCheckboxes.forEach(cb => cb.checked = this.checked);
        });

        monthCheckboxes.forEach(cb => {
            cb.addEventListener('change', function () {
                if (!this.checked) {
                    selectAll.checked = false;
                } else if ([...monthCheckboxes].every(c => c.checked)) {
                    selectAll.checked = true;
                }
            });
        });

        // Auto-check "Select All" if all checkboxes are already checked (on edit load)
        const allChecked = [...monthCheckboxes].length > 0 && [...monthCheckboxes].every(c => c.checked);
        if (allChecked) {
            selectAll.checked = true;
        }
    });

    // Slug
   document.addEventListener('DOMContentLoaded', function () {

        const partnerNameField = document.getElementById('name'); // Partner name input
        const partnerSlugNameField = document.getElementById('slug'); // Visible slug input
        const partnerSlugHiddenField = document.getElementById('partner-slug-hidden'); // Hidden slug input
        const partnerId = <?= (int)($partner->id ?? 0) ?>;

        function generateSlug(text) {
            return text
                .toString()
                .trim()
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-') // Replace non-alphanumeric with -
                .replace(/^-+|-+$/g, '');    // Remove leading/trailing dashes
        }

        function updateSlugFields() {
            if (partnerSlugNameField && partnerSlugHiddenField && partnerId) {
                const slugName = partnerSlugNameField.value || generateSlug(partnerNameField?.value ?? '');
                const fullSlug = partnerId + '-' + slugName;
                partnerSlugHiddenField.value = fullSlug;
            }
        }

        if (partnerNameField && partnerSlugNameField && partnerId) {

            // Auto-update slug when partner name is typed
            partnerNameField.addEventListener('input', function () {
                const newSlugName = generateSlug(this.value);
                partnerSlugNameField.value = newSlugName;
                updateSlugFields();
            });

            // Update hidden field when visible slug is typed manually
            partnerSlugNameField.addEventListener('input', function () {
                updateSlugFields();
            });

            // Initialize on load
            updateSlugFields();
        }
    });

</script>
<script>
    const existingmedia = <?php echo !empty($mediaImages) ? json_encode($mediaImages, JSON_HEX_TAG) : '[]'; ?>;
    let allFiles = [];
    let deletedImages = [];

    function initializeExistingImages(existingmedia) {
        existingmedia.forEach((media) => {
            const ext = media.url.split('.').pop().toLowerCase();
            const isVideo = ['mp4', 'mov', 'mkv', 'avi'].includes(ext);
            let file = {
                id: media.id,
                name: media.displayname,
                type: isVideo ? 'video' : 'image',
                value: media.path,
                url: media.url
            };
            allFiles.push(file);
        });
        renderPreviews();
    }

    initializeExistingImages(existingmedia);
    document.getElementById('imageInput').addEventListener('change', function(event) {
        let newFiles = Array.from(event.target.files);

        // Validation for file types and size
        const allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'mp4', 'mov', 'mkv', 'avi'];
        const maxSizeMB = 2;
        const maxSizeBytes = maxSizeMB * 1024 * 1024;
        let invalidFiles = [];

        newFiles.forEach(file => {
            let fileExtension = file.name.split('.').pop().toLowerCase();
            let fileSize = file.size;

            if (!allowedExtensions.includes(fileExtension)) {
                invalidFiles.push({
                    file: file.name,
                    reason: '<?= __('Invalid file type. Only .jpg, .jpeg, .png, .gif,.webp ,.mp4, .mov, .mkv, .avi files are allowed.') ?>'
                });
            } else if (fileSize > maxSizeBytes) {
                invalidFiles.push({
                    file: file.name,
                    reason: `<?= __('File size exceeds the maximum limit of 2MB.') ?>`
                });
            }
        });
        if (invalidFiles.length > 0) {
            var html = "<ul>";
            invalidFiles.forEach(function(invalidFile) {
                html += `<li>${invalidFile.file} - ${invalidFile.reason}</li>`;
            });
            html += '</ul>';

            const wrapper = document.createElement('div');
            wrapper.innerHTML = html;

            swal({
                title: "<?= __("Invalid Files") ?>",
                content: wrapper,
                confirmButtonText: "<?= __("OK") ?>",
                allowOutsideClick: "true"
            });
            document.getElementById('imageInput').value = "";
            return; // Exit early if there are invalid files
        }

        // Proceed with processing valid files
        newFiles.forEach(file => {
            let fileType = file.type.startsWith('image') ? 'image' : 'video';
            let fileObj = {
                id: Date.now() + Math.random(), // to avoid duplicate IDs
                name: file.name,
                type: fileType,
                value: file.name,
                url: URL.createObjectURL(file),
                file: file
            };
            allFiles.push(fileObj);
        });

        renderPreviews();
        updateFileInput();
    });


    function renderPreviews() {
        let previewContainer = document.getElementById('imagePreviewContainer');
        previewContainer.innerHTML = '';
        allFiles.forEach((file) => {
            let li = document.createElement('li');
            li.classList.add('media-thumbnail', 'position-relative');

            let fileName = file.name;
            let shortName = fileName.length > 20 ? fileName.slice(0, 17) + '...' : fileName;

            let mediaPreview = '';
            if (file.type.startsWith('image')) {
                mediaPreview = `<img src="${file.url}" alt="Image Preview" class="preview-img" style="width: 120px; height: 100px; object-fit: cover;"/>`;
            } else if (file.type.startsWith('video') || ['mp4', 'mov', 'mkv', 'avi'].includes(file.name.split('.').pop().toLowerCase())) {
                mediaPreview = `<video src="${file.url}" class="preview-img" controls style="width: 120px; height: 100px; object-fit: cover;"></video>`;
            }

            li.innerHTML = `
            ${mediaPreview}
            <span class="image-name d-block text-truncate" title="${file.name}">${shortName}</span>
            <button type="button"class="delete-img-btn btn btn-sm btn-danger position-absolute top-0 end-0" data-id="${file.id}">
                <i class="fas fa-times"></i>
            </button>`;
            previewContainer.appendChild(li);
        });
    }


    document.getElementById('imagePreviewContainer').addEventListener('click', function(e) {
        if (e.target.closest('.delete-img-btn')) {
            let id = e.target.closest('.delete-img-btn').getAttribute('data-id');
            let index = allFiles.findIndex(file => file.id == id);
            if (index !== -1) {
                let removedFile = allFiles.splice(index, 1)[0];
                if (removedFile.id) {
                    deletedImages.push(removedFile.id);
                }
                renderPreviews();
                updateFileInput();
                updateDeletedImagesInput();
            }
        }
    });

    function updateDeletedImagesInput() {
        document.getElementById('deletedImagesInput').value = JSON.stringify(deletedImages);
    }

    function updateFileInput() {
        let dataTransfer = new DataTransfer();
        allFiles.forEach(file => {
            if (file.file) {
                dataTransfer.items.add(file.file);
            }
        });
        document.getElementById('imageInput').files = dataTransfer.files;
    }
</script>
<?php $this->end(); ?>