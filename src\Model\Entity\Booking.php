<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * Booking Entity
 *
 * @property int $id
 * @property int $customer_id
 * @property int|null $course_id
 * @property int|null $class_id
 * @property int|null $class_time_slot_id
 * @property int $partner_id
 * @property \Cake\I18n\Date $booking_date
 * @property string $booking_status
 * @property string|null $billing_currency
 * @property string|null $tax_amount
 * @property string|null $tax_rate
 * @property string $total_price
 * @property string|null $grand_total
 * @property string|null $payment_status
 * @property \Cake\I18n\DateTime $created_at
 * @property \Cake\I18n\DateTime $modified_at
 *
 * @property \App\Model\Entity\Customer $customer
 * @property \App\Model\Entity\Course $course
 * @property \App\Model\Entity\Class $class
 * @property \App\Model\Entity\ClassTimeSlot $class_time_slot
 * @property \App\Model\Entity\Partner $partner
 * @property \App\Model\Entity\Discount $discount
 * @property \App\Model\Entity\BookingAddon[] $booking_addons
 * @property \App\Model\Entity\BookingDetail[] $booking_details
 * @property \App\Model\Entity\Cancellation[] $cancellations
 * @property \App\Model\Entity\PartnerSettlement[] $partner_settlements
 * @property \App\Model\Entity\Payment[] $payments
 */
class Booking extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'customer_id' => true,
        'course_id' => true,
        'partner_id'=> true,
        'batch_id'  => true,
        'booking_date' => true,
        'booking_status' => true,
        'billing_currency' => true,
        'tax_amount' => true,
        'tax_rate' => true,
        'total_price' => true,
        'grand_total' => true,
        'payment_status' => true,
        'created_at' => true,
        'modified_at' => true,
        'customer' => true,
        'course' => true,
        'class' => true,
        'class_time_slot' => true,
        'partner' => true,
        'booking_addons' => true,
        'booking_details' => true,
        'cancellations' => true,
        'partner_settlements' => true,
        'payments' => true,
    ];

    /**
     * Get full name by combining first_name and last_name
     *
     * @return string
     */
    protected function _getFullName(): string
    {
        $name = '';
        if (!empty($this->title)) {
            $name .= $this->title . ' ';
        }
        if (!empty($this->first_name)) {
            $name .= $this->first_name . ' ';
        }
        if (!empty($this->last_name)) {
            $name .= $this->last_name;
        }
        return trim($name);
    }

    /**
     * Calculate grand total if not set
     *
     * @return string
     */
    // protected function _getGrandTotal(): string
    // {
    //     if (isset($this->_fields['grand_total']) && !empty($this->_fields['grand_total'])) {
    //         return $this->_fields['grand_total'];
    //     }

    //     $total = (float)($this->total_price ?? 0);
    //     $tax = (float)($this->tax_amount ?? 0);

    //     return (string)($total + $tax);
    // }

    /**
     * Calculate tax amount based on tax rate and total price
     *
     * @return string
     */
    // protected function _getTaxAmount(): string
    // {
    //     if (isset($this->_fields['tax_amount']) && !empty($this->_fields['tax_amount'])) {
    //         return $this->_fields['tax_amount'];
    //     }

    //     if (isset($this->tax_rate) && isset($this->total_price)) {
    //         $rate = (float)$this->tax_rate / 100;
    //         $total = (float)$this->total_price;
    //         return (string)($total * $rate);
    //     }

    //     return '0.00';
    // }

    /**
     * Check if booking is confirmed
     *
     * @return bool
     */
    public function isConfirmed(): bool
    {
        return $this->booking_status === 'confirmed';
    }

    /**
     * Check if booking is cancelled
     *
     * @return bool
     */
    public function isCancelled(): bool
    {
        return $this->booking_status === 'cancelled';
    }

    /**
     * Check if payment is completed
     *
     * @return bool
     */
    public function isPaid(): bool
    {
        return $this->payment_status === 'paid';
    }
}