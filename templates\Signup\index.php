<head>
    <link rel="stylesheet" href="<?= $this->Url->webroot('css/signup.css'); ?>">
</head>

<section class="login-container px-6 md:px-10 lg:px-25 xl:px-40">
    <div class="mx-auto">
        <div class="block md:flex md:items-normal justify-center lg:h-[96vh]" id="loginWrapper">
            <!-- Card 1 -->
            <div class="bg-white rounded-lg left-img-content desktop-view lg:strciky lg:top:0">
                <!-- <img src="<?= $this->Url->webroot('img/yoga-register.png') ?>" alt="Login Yoga image" /> -->
                 <a href="/"><img src="<?= $this->Url->webroot('img/yoga-big.png') ?>" alt="Yoga logo" class="yoga-logo"></a>
                <div class="img-container">
                    <img src="/img/login-img.png" alt="Login image" class="login-img">
                </div>
            </div>

            <div class="bg-white rounded-lg login-wrapper rounded shadow lg:overflow-y-auto">
                <a href="/" class="mobile-view"><img src="<?= $this->Url->webroot('img/yoga-big.png') ?>" alt="Yoga logo" class="yoga-logo"></a>
                <div class="hr-container">
                    <div class="hr"></div>
                    <h2 class="text-center heading">Create an account</h2>
                </div>
                <div class="social-media px-4 py-1">
                    <button id="google-signin-btn" class="google">
                        <img src="<?= $this->Url->webroot('img/google-icon.png') ?>" alt="Google Icon">
                    </button>
                    <button class="fb"><img src="<?= $this->Url->webroot('img/facebook-icon.png') ?>" alt="Facebook Icon"></button>
                </div>
                <div x-data="{ tab: 'first' }" class="max-w-2xl mx-auto bg-white px-4 py-2">

                    <!-- Tabs -->
                    <div class="flex mb-2 bg-[#FFEFE9] rounded-[8px] px-[6px] py-[4px]">
                        <button @click="tab = 'first'" :class="tab === 'first' ? 'border-b-2 border-[#d87a61] active' : 'text-[#293148]'" class="px-2 py-1 focus:outline-none btn-noactive">With Password</button>
                        <button @click="tab = 'second'" :class="tab === 'second' ? 'border-b-2 border-[#d87a61] active' : 'text-[#293148]'" class="px-2 py-1 focus:outline-none btn-noactive">With OTP</button>
                    </div>

                    <!-- Tab Panels -->
                    <div x-show="tab === 'first'" class="h-93 first-tab">
                        <form id="password-signup-form" class="login-form" method="post">
                            <?= $this->Html->meta('csrfToken', $this->request->getAttribute('csrfToken')) ?>
                            <div class="form-container">
                                <div class="relative form-field">
                                    <label>First Name</label>
                                    <input type="text" name="first_name" minlength="3" maxlength="50" class="form-control" id="input" required />
                                </div>
                                <div class="relative form-field">
                                    <label>Last Name</label>
                                    <input type="text" name="last_name" class="form-control" id="input" required placeholder="" />
                                </div>
                                <div class="relative form-field">
                                    <label>Email ID</label>
                                    <input type="text" name="email_id" id="email_id" class="form-control" required placeholder="" />
                                </div>
                                <div class="relative form-field">
                                    <label>Mobile No</label>
                                    <span class="mobile">
                                        <select name="country_code" class="code">
                                            <option value="1">+1</option>
                                            <option value="7">+7</option>
                                            <option value="43">+43</option>
                                            <option value="44">+44</option>
                                            <option value="60">+60</option>
                                            <option value="65">+65</option>
                                            <option value="91">+91</option>
                                            <option value="92">+92</option>
                                            <option value="1 648">******</option>
                                        </select>
                                        <input type="text" name="email_or_mobile" maxlength="10" class="form-control" required onkeypress="validNumber(event);" />
                                    </span>
                                </div>
                                
                                <div class="relative form-field">
                                    <label>Create Password</label>
                                    <input type="password" name="password" class="form-control password" id="input" required />
                                    <div class="eye-icon">
                                        <i class="fas fa-eye-slash"></i>
                                    </div>
                                    <div class="password-requirements text-xs text-gray-600 mt-1">
                                        Password must be 8-20 characters and include uppercase, lowercase, number, and special character.
                                    </div>
                                </div>
                                <div class="relative form-field">
                                    <label>Re-enter Password</label>
                                    <input type="password" name="re_password" class="form-control password" id="input" required />
                                    <div class="eye-icon">
                                        <i class="fas fa-eye-slash"></i>
                                    </div>
                                    <!-- <div class="password-requirements text-xs text-gray-600 mt-1">
                                        Password is not matched.
                                    </div> -->
                                </div>
                                <button type="submit" class="btn-login">Create Account</button>
                                <!-- <p class="new-account text-center">Already have an account? <a href="<?= $this->Url->build('/login') ?>?redirect=<?= $qryRedirect ?>">Login</a></p>
                                <p class="small-text">
                                    <label class="flex items-start">
                                        <input type="checkbox" name="terms_accepted" id="input" required /> <span>I agree to <a href="<?= h($configSettings['WP_TERMS_CONDITIONS']) ?>">Terms & Conditions </a> and <a href="<?= h($configSettings['WP_PRIVACY_POLICY']) ?>"> Privacy Policy</a> of yoga.in</span>
                                    </label>
                                </p> -->
                                <p class="small-text terms">
                                    <span>By continuing, you agree to <a href="<?= h($configSettings['WP_TERMS_CONDITIONS']) ?>">Terms of Use </a> and <a href="<?= h($configSettings['WP_PRIVACY_POLICY']) ?>"> Privacy Policy</a> of yoga.in</span>
                                </p>
                            </div>
                        </form>
                    </div>
                    <div x-show="tab === 'second'" class="h-93 second-tab">
                        <form class="login-form" @submit.prevent="console.log('Form submitted');">
                            <div class="form-container">
                                <!-- <div class="relative form-field">
                                    <label>First Name</label>   
                                    <input type="text" name="first_name" minlength="3" maxlength="50" class="form-control" id="input" required />
                                </div>
                                <div class="relative form-field">
                                    <label>Email ID / Mobile Number</label>
                                    <div class="flex items-center gap-2">
                                        <input type="text" name="email" class="form-control email" id="input" required />
                                        <button id="send-otp-btn" type="button" class="btn-send-otp">Send OTP</button>
                                    </div>
                                </div> -->
                                <div class="relative form-field otp-wrapper">
                                    <form class="otp-form" @submit.prevent="console.log('Form submitted');">
                                        <div class="mobile-otp-verify">
                                            <p class="otp-sent-msg text-success"></p>
                                            <div class="flex items-center justify-between">
                                                <label>Mobile Verification Code</label>
                                                <span class="text-[#BE1F2E]" id="otp-timer">05:00</span>
                                            </div>
                                            <div class="otp-msg">
                                                <input type="text" class="form-control form-otp" id="input" value="" require />
                                                <input type="text" class="form-control form-otp" id="input" value="" require />
                                                <input type="text" class="form-control form-otp" id="input" value="" require />
                                                <input type="text" class="form-control form-otp" id="input" value="" require />
                                            </div>
                                            <span class="resend-span">Didn't receive a code? <button type="button" class="btn-link" id="resend-otp-btn" disabled>Resend OTP</button></span>
                                            <span class="verify-msg">Verification code sent to <span class="verify-mail">+91 9876543210</span></span>
                                            <!-- <button type="submit" class="btn-resend mobile-view disabled:cursor-not-allowed" disabled>Resend OTP</button> -->
                                        </div>
                                        <div class="email-otp-verify">
                                            <p class="otp-sent-msg text-success"></p>
                                            <div class="flex items-center justify-between">
                                                <label>Email Verification Code</label>
                                                <span class="text-[#BE1F2E]" id="otp-timer">05:00</span>
                                            </div>
                                            <div class="otp-msg">
                                                <input type="text" class="form-control form-otp" id="input" value="" require />
                                                <input type="text" class="form-control form-otp" id="input" value="" require />
                                                <input type="text" class="form-control form-otp" id="input" value="" require />
                                                <input type="text" class="form-control form-otp" id="input" value="" require />
                                            </div>
                                            <span class="resend-span">Didn't receive a code? <button type="button" class="btn-link" id="resend-otp-btn" disabled>Resend OTP</button></span>
                                            <span class="verify-msg">Verification code sent to <span class="verify-mail"><EMAIL></span></span>
                                            <!-- <button type="submit" class="btn-resend mobile-view disabled:cursor-not-allowed" disabled>Resend OTP</button> -->
                                        </div>
                                    </form>
                                </div>
                                <button class="btn-otp">Create Account</button>
                                <!-- <p class="new-account text-center">Already have an account? <a href="<?= $this->Url->build('/login') ?>?redirect=<?= $qryRedirect ?>">Login</a></p>
                                    <p class="small-text">
                                        <label class="flex items-start">
                                        <input type="checkbox" name="terms_accepted" id="input" required /> <span>I agree to <a href="<?= h($configSettings['WP_TERMS_CONDITIONS']) ?>">Terms & Conditions </a> and <a href="<?= h($configSettings['WP_PRIVACY_POLICY']) ?>"> Privacy Policy</a> of yoga.in</span>
                                    </label>
                                </p> -->
                                <p class="small-text terms">
                                    <span>By continuing, you agree to <a href="<?= h($configSettings['WP_TERMS_CONDITIONS']) ?>">Terms of Use </a> and <a href="<?= h($configSettings['WP_PRIVACY_POLICY']) ?>"> Privacy Policy</a> of yoga.in</span>
                                </p>
                            </div>
                        </form>
                    </div>
                </div>
                <hr />
                <div class="text-center">
                    <span class="new-account text-center inline-block">Already have an account? 
                        <a href="<?= $this->Url->build('/login') ?>?redirect=<?= $qryRedirect ?>">Login</a>
                    </span>
                </div>
                
                <!--<div class="divider-container">
                    <span class="divider">Or</span>
                </div> -->
                
            </div>
        </div>
    </div>
</section>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        let createBtnOtp = document.querySelector('.second-tab .btn-otp');
        if (createBtnOtp) {
            createBtnOtp.disabled = true;
            createBtnOtp.classList.add('disabled:cursor-not-allowed');
        }
        const passwordForm = document.getElementById('password-signup-form');
        const emailOrMobileInput = passwordForm.querySelector("#email_or_mobile");
        const firstNameInputs = passwordForm.querySelector('input[name="first_name"]');
        const passwordInput = passwordForm.querySelector('input[name="password"]');
        const termsAccepted = passwordForm.querySelector('input[name="terms_accepted"]');
        const queryRedirect = '<?php echo  $qryRedirect ?>'

        // === Password visibility toggle ===
        passwordForm.querySelectorAll('.eye-icon').forEach(icon => {
            icon.addEventListener('click', function() {
                const passwordField = this.parentElement.querySelector('input');
                const eyeIcon = this.querySelector('i');

                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    eyeIcon.classList.replace('fa-eye-slash', 'fa-eye');
                } else {
                    passwordField.type = 'password';
                    eyeIcon.classList.replace('fa-eye', 'fa-eye-slash');
                }
            });
        });

        // === Form submit ===
        passwordForm.addEventListener('submit', function(e) {
            e.preventDefault();

            let isValid = true;

            // Get values
            const emailOrMobile = emailOrMobileInput.value.trim();
            const firstName = firstNameInputs.value.trim();
            const password = passwordInput.value;

            // Clear previous errors
            clearInlineError(firstNameInputs);
            clearInlineError(emailOrMobileInput);
            clearInlineError(passwordInput);

            // === Validations ===
            const firstNameError = validateFirstName(firstName);
            if (firstNameError) {
                showInlineError(firstNameInputs, firstNameError);
                isValid = false;
            }

            const emailOrMobileError = validateEmailOrMobile(emailOrMobile);
            if (emailOrMobileError) {
                showInlineError(emailOrMobileInput, emailOrMobileError);
                isValid = false;
            }

            const passwordError = validatePassword(password);
            if (passwordError) {
                showInlineError(passwordInput, passwordError);
                isValid = false;
            }

            if (!termsAccepted.checked) {
                alert("Please accept the Terms & Conditions and Privacy Policy");
                isValid = false;
            }

            if (!isValid) return;

            // === Submit with fetch ===
            const csrfToken = document.querySelector('meta[name="csrfToken"]').getAttribute('content');
            const formData = new FormData(passwordForm);
            // Show loading state
            let createBtn = document.querySelector('#password-signup-form .btn-login');
            if (createBtn) {
                createBtn.textContent = 'Processing...';
                createBtn.disabled = true;
            }
            formData.append('reqdirect_url', queryRedirect);

            fetch('<?= $this->Url->build(['controller' => 'Signup', 'action' => 'register']) ?>', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-Token': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json'
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                  
                    if (createBtn) {
                        // Reset button state
                        createBtn.textContent = 'Create Account';
                        createBtn.disabled = false;
                    }

                    if (data.success && data.redirect) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Success!',
                            text: data.message,
                            confirmButtonText: 'OK'
                        }).then(() => {
                            if (data.redirect) {
                                window.location.href = data.redirect;
                            }
                        });

                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: data.message || 'An error occurred. Please try again.',
                            confirmButtonText: 'OK'
                        });

                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'An unexpected error occurred. Please try again.',
                        confirmButtonText: 'OK'
                    });

                });
        });

        // === Helper Functions ===
        function showInlineError(input, message, withOtp=false) {
            var errorEl = input.parentElement.querySelector('.input-error');
            if(withOtp){
                var errorEl = input.parentElement.parentElement.querySelector('.input-error');
            }
           
            if (!errorEl) {
                errorEl = document.createElement('span');
                errorEl.className = 'input-error text-red-500 text-xs mt-1 block';
                if(withOtp){
                    input.parentElement.parentElement.appendChild(errorEl);
                } else {
                    input.parentElement.appendChild(errorEl);
                }
               
            }
            errorEl.textContent = message;
            input.classList.add('is-invalid');
        }

        function clearInlineError(input, withOtp=false) {
            if(withOtp){
                var errorEl = input.parentElement.parentElement.querySelector('.input-error');
            } else {
                var errorEl = input.parentElement.querySelector('.input-error');
            }
        
            if (errorEl) errorEl.textContent = '';
            input.classList.remove('is-invalid');
        }

        function validateFirstName(value) {
            const trimmed = value.trim();

            if (!trimmed) return 'This field is required';
            if (!/^[a-zA-Z\s]+$/.test(trimmed)) return 'Only letters and spaces allowed';
            if (trimmed.length < 3) return 'At least 3 characters required';
            if (trimmed.length > 50) return 'Max 50 characters allowed';
            return '';
        }

        function validateEmailOrMobile(value) {
            const trimmed = value.trim();

            if (!trimmed) {
                return 'This field is required';
            }

            if (trimmed.includes('@')) {
                const emailRegex = /^[a-zA-Z0-9](?!.*\.\.)[a-zA-Z0-9._%+-]{0,63}@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                return emailRegex.test(trimmed) ? '' : 'Invalid email format';
            } else {
                return /^\d{10}$/.test(trimmed) ? '' : 'Enter a valid 10-digit mobile number';
            }
        }


        function validatePassword(value) {
            if (value.length < 8) return 'Min 8 characters required';
            if (value.length > 20) return 'Max 20 characters allowed';
            if (!/[A-Z]/.test(value)) return 'Include uppercase letter';
            if (!/[a-z]/.test(value)) return 'Include lowercase letter';
            if (!/[0-9]/.test(value)) return 'Include a number';
            if (!/[^A-Za-z0-9]/.test(value)) return 'Include special character';
            return '';
        }


        const otpEmailInput = document.querySelector('.second-tab input[name="email"]');
        const sendOtpBtn = document.getElementById('send-otp-btn');
        const otpWrapper = document.querySelector('.otp-wrapper');
        const otpSentMsg = document.querySelector('.otp-sent-msg');
        const otpInputs = document.querySelectorAll('.form-otp');
        const resendOtpBtn = document.querySelector('.btn-resend');
        const createAccountBtn = document.querySelector('.btn-otp');
        const otpemailOrMobileInput = document.querySelector('input[name="email"]');
        const firstNameInput = document.querySelector('.second-tab input[name="first_name"]');
        const termsAccepetdInput = document.querySelector('.second-tab input[name="terms_accepted"]');

        // Email/Mobile validation for second tab (OTP)
        if (otpEmailInput) {
            let debounceTimer;
            let withOtp = true;

            const showValidationFeedback = (input, message) => {
             const parent = input.closest('.form-field');
                removeValidationFeedback(input);
                const feedback = document.createElement('div');
                feedback.className = 'validation-feedback text-red-500 text-xs mt-1';
                feedback.textContent = message;
                parent.appendChild(feedback);
            };

            const removeValidationFeedback = (input) => {
                const parent = input.closest('.form-field');
                const existingFeedback = parent.querySelector('.validation-feedback');
                if (existingFeedback) existingFeedback.remove();
               // input.classList.remove('is-invalid', 'is-valid');
            };

            otpEmailInput.addEventListener('input', function() {
                clearTimeout(debounceTimer);
                removeValidationFeedback(this);
                // Remove validation messages while typing
                this.classList.remove('is-invalid', 'is-valid');
                
                clearInlineError(otpEmailInput);
                // Debounce the validation
                debounceTimer = setTimeout(() => {
                    const value = this.value.trim();

                    // Check if the trimmed value is empty
                    if (!value) {
                        this.classList.add('is-invalid');
                        clearInlineError(this, withOtp);
                        showValidationFeedback(this, 'Please enter a valid email address or mobile number');
                        return;
                    } else {
                        removeValidationFeedback(this);
                    }

                    // Check if it looks like an email attempt
                    const looksLikeEmail = /[a-zA-Z@]/.test(value);
                    
                    if (looksLikeEmail) {
                        // Validate email format
                        // const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                        const emailRegex = /^[a-zA-Z0-9](?!.*\.\.)[a-zA-Z0-9._%+-]{0,63}@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                        const isValidEmail = emailRegex.test(value);
                 
                        // Show validation feedback
                        this.classList.add(isValidEmail ? 'is-valid' : 'is-invalid');

                        if (!isValidEmail) {
                            clearInlineError(this, withOtp);
                            showValidationFeedback(this, 'Please enter a valid email address');
                        }

                    } else {
                        // Validate mobile number (exactly 10 digits)
                        const isValidMobile = /^\d{10}$/.test(value);
                        // Show validation feedback
                        this.classList.add(isValidMobile ? 'is-valid' : 'is-invalid');
                        clearInlineError(this, withOtp);
                        removeValidationFeedback(this);
                    }
                }, 500);
            });
        }
        // Timer elements
        const timerText = document.getElementById('otp-timer');
        let countdown;
        let remainingTime = 60; // 60 seconds countdown

        // Function to start countdown timer
        function startTimer() {
            remainingTime = 60;
            updateTimerText();

            if (countdown) {
                clearInterval(countdown);
            }

            countdown = setInterval(function() {
                remainingTime--;
                updateTimerText();

                if (remainingTime <= 0) {
                    clearInterval(countdown);
                    resendOtpBtn.disabled = false;
                    resendOtpBtn.classList.remove('disabled:cursor-not-allowed');
                }
            }, 1000);
        }

        // Update timer text
        function updateTimerText() {
            if (timerText) {
                if (remainingTime > 0) {
                    timerText.textContent = `Resend OTP in ${remainingTime} seconds`;
                } else {
                    timerText.textContent = `You can now resend the OTP`;
                }
            }
        }


        // Initialize OTP inputs to handle one digit per input and auto-focus next input
        if (otpInputs.length) {
            otpInputs.forEach((input, index) => {
                input.addEventListener('input', function(e) {
                    // Allow only one digit
                    this.value = this.value.replace(/\D/g, '').substring(0, 1);

                    // Auto focus next input
                    if (this.value && index < otpInputs.length - 1) {
                        otpInputs[index + 1].focus();
                    }
                });

                // Handle backspace to go to previous input
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Backspace' && !this.value && index > 0) {
                        otpInputs[index - 1].focus();
                    }
                });
            });
        }

        // Function to get complete OTP from inputs
        function getCompleteOtp() {
            let otp = '';
            otpInputs.forEach(input => {
                otp += input.value;
            });
            return otp;
        }

        // Handle Send OTP button click
        if (sendOtpBtn) {
            sendOtpBtn.addEventListener('click', function() {
                const firstName = firstNameInput.value.trim();
                const emailOrMobile = otpemailOrMobileInput.value.trim();
                const withOtp = true;

                clearInlineError(firstNameInput);
                clearInlineError(otpemailOrMobileInput, withOtp);

                if (!emailOrMobile) {
                    showInlineError(otpemailOrMobileInput, 'This field is required', withOtp);
                    return;
                }
                
                const firstNameError = validateFirstName(firstName);
          
                if (firstNameError) {
                    showInlineError(firstNameInput, firstNameError);
                    return;
                }

               
                // Validate email or mobile format
                const isEmail = /^[a-zA-Z0-9](?!.*\.\.)[a-zA-Z0-9._%+-]{0,63}@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(emailOrMobile);
                const isMobile = /^\d{10}$/.test(emailOrMobile);

                if (!isEmail && !isMobile) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: emailOrMobile.includes('@') ? 'Please enter a valid email address' : 'Please enter a valid 10-digit mobile number',
                        confirmButtonText: 'OK'
                    });
                    return;
                }


                // Show loading state
                sendOtpBtn.textContent = 'Sending...';
                sendOtpBtn.disabled = true;

                // Send OTP request
                const formData = new FormData();
                formData.append('email', emailOrMobile);
                const csrfToken = document.querySelector('meta[name="csrfToken"]').getAttribute('content');

                fetch('<?= $this->Url->build(['controller' => 'Signup', 'action' => 'sendOtp']) ?>', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-Token': csrfToken,
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        // Reset button state
                        sendOtpBtn.textContent = 'Send OTP';
                        sendOtpBtn.disabled = false;

                        if (data.success) {
                            // Show OTP input fields
                            otpWrapper.style.display = 'block';

                            // Update message based on input type
                            if (data.input_type === 'mobile') {
                                otpSentMsg.textContent = 'The OTP is sent to your mobile number';
                            } else {
                                otpSentMsg.textContent = 'The OTP is sent to your email address';
                            }

                            console.log('Starting timer, timer element:', timerText);
                            // Start countdown timer
                            startTimer();

                            // Disable resend button initially
                            resendOtpBtn.disabled = true;
                            resendOtpBtn.classList.add('disabled:cursor-not-allowed');

                            // Focus first OTP input
                            if (otpInputs.length) {
                                otpInputs[0].focus();
                            }

                            // Show success message
                            Swal.fire({
                                icon: 'success',
                                title: 'OTP Sent',
                                text: data.message,
                                confirmButtonText: 'OK'
                            });

                            // enable create button //
                            if (createBtnOtp) {
                                createBtnOtp.disabled = false;
                                createBtnOtp.classList.remove('disabled:cursor-not-allowed');
                            }

                        } else {
                            // Show error message
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: data.message || 'Failed to send OTP. Please try again.',
                                confirmButtonText: 'OK'
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        sendOtpBtn.textContent = 'Send OTP';
                        sendOtpBtn.disabled = false;

                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'An unexpected error occurred. Please try again.',
                            confirmButtonText: 'OK'
                        });
                    });
            });
        }

        // Handle Resend OTP button click
        if (resendOtpBtn) {
            resendOtpBtn.addEventListener('click', function() {
                if (this.disabled) return;

                // Trigger the send OTP function
                sendOtpBtn.click();
            });
        }

        // Handle Create Account button click
        if (createAccountBtn) {
            createAccountBtn.addEventListener('click', function() {
                const firstName = firstNameInput.value.trim();
                const emailOrMobile = otpEmailInput.value.trim();
                const otp = getCompleteOtp();
                // const termsAccepetd = termsAccepetdInput.value.trim();
               
                clearInlineError(firstNameInput);
                clearInlineError(otpEmailInput);
                clearInlineError(termsAccepetdInput);
                otpInputs.forEach(clearInlineError);

                let hasError = false;
                const firstNameError = validateFirstName(firstName);
            
                if (firstNameError) {
                    showInlineError(firstNameInput, firstNameError);
                    hasError = true;
                }

                if (!emailOrMobile) {
                    showInlineError(otpEmailInput, 'This field is required');
                    hasError = true;
                }

                if (!termsAccepetdInput.checked) {
                    //showInlineError(termsAccepetdInput, 'This field is required');
                    hasError = true;
                }
                
                const otpValue = getCompleteOtp();
                var showOTPError = false;
                if (otpValue.length < otpInputs.length) {
                    otpInputs.forEach(input => {
                        if (!input.value) showOTPError = true; 
                    });

                    if(showOTPError == true){
                        showInlineError(input, 'Required');
                    }
                    hasError = true;
                }

                if (hasError) return;


                // Show loading state
                this.textContent = 'Processing...';
                this.disabled = true;

                // Send verification request
                const formData = new FormData();
                formData.append('first_name', firstName);
                formData.append('email', emailOrMobile);
                formData.append('otp', otp);
                const csrfToken = document.querySelector('meta[name="csrfToken"]').getAttribute('content');

                formData.append('reqdirect_url', queryRedirect);
                
                fetch('<?= $this->Url->build(['controller' => 'Signup', 'action' => 'verifyOtp']) ?>', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-Token': csrfToken,
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        // Reset button state
                        this.textContent = 'Create Account';
                        this.disabled = false;

                        if (data.success) {
                            // Show success message
                            Swal.fire({
                                icon: 'success',
                                title: 'Success!',
                                text: data.message,
                                confirmButtonText: 'OK'
                            }).then(() => {
                                if (data.redirect) {
                                    window.location.href = data.redirect;
                                }
                            });
                        } else {
                            // Show error message
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: data.message || 'OTP verification failed. Please check your OTP.',
                                confirmButtonText: 'OK'
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        this.textContent = 'Create Account';
                        this.disabled = false;

                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'An unexpected error occurred. Please try again.',
                            confirmButtonText: 'OK'
                        });
                    });
            });
        }


        // Add CSS for validation styling
        document.head.insertAdjacentHTML('beforeend', `
        <style>
        .is-invalid {
            border-color: #dc3545 !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
        .otp-msg .is-invalid{
            border-color: #dc3545 !important;
            background-image: none;
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
            padding: 1px 7px;
        }
        .password.is-invalid{
            border-color: #dc3545 !important;
            background-image: none;
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
        .is-valid {
            border-color: #28a745 !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
        </style>
        `);
    });

    document.addEventListener('DOMContentLoaded', function () {
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
        const input = document.getElementById('input');
        const container = document.getElementById('loginWrapper');

        if (isIOS && input && container) {
            input.addEventListener('focus', function () {
            container.classList.add('ios-focused');
            });

            input.addEventListener('blur', function () {
            container.classList.remove('ios-focused');
            });
        }
        });
</script>

<!-- Add Google Sign-In API -->
<script src="https://accounts.google.com/gsi/client" async defer></script>