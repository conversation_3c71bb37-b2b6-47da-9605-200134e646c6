<?php
/**
 * @var \App\View\AppView $this
 * @var string $title
 */

// $this->extend('/layout/profile_layout');
?>
<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

    .group-booking h2 {
        font-family: "Open Sans", sans-serif;
        font-size: 40px;
        font-weight: 700;
        color: #293148;
        z-index: 9;
        position: relative;
    }

    .group-booking .content p {
        font-family: "Open Sans", sans-serif;
        font-size: 18px;
        font-weight: 600;
    }

    .time-info .italic {
        font-family: "Open Sans", sans-serif;
        font-style: italic;
        font-size: 20px;
        font-weight: 600;
    }

    .grp-booking-body {
        border-bottom: 1px solid #C45F44;
        padding: 40px 0px;
    }

    /* section1 */
    /* section2 */
    .select-branch h3 {
        font-family: "Open Sans", sans-serif;
        font-size: 32px;
        font-weight: 700;
    }

    .select-branch .select-date {
        font-family: "Open Sans", sans-serif;
        font-size: 20px;
        font-weight: 700;

    }

    /* booking success */
    .booking-success-content .content {
        font-family: "Open Sans", sans-serif;
        font-size: 14px;
        font-weight: 400;
    }

    .booking-success-content .view-book {
        font-family: "Open Sans", sans-serif;
        font-size: 16px;
        font-weight: 700;
        background: #D87A61;
    }

    .booking-success-content .download {
        font-family: "Open Sans", sans-serif;
        font-size: 16px;
        font-weight: 700;
        color: #D87A61;
    }

    /* My profile */
    .my-profile-banner {
        background: url(../../../img/profile-banner.png) no-repeat bottom center;
        background-size: cover;
        height: 301px;
        width: 100%;
    }

    .edit-btn,
    .change-password {
        font-weight: 700;
        font-size: 16px;
        font-family: "Open Sans", sans-serif;
    }

    .edit-btn {
        margin-top: 30px;
    }

    .change-password {
        color: #D87A61;
        border: 1px solid #D87A61;
    }

    .pro-pic {
        margin-bottom: 60px;
    }

    .my-profile-form input,
    .my-profile-form select,
    .my-profile-form .dob {
        border-radius: 20px;
        font-weight: 600;
        font-family: "Open Sans", sans-serif;
        font-size: 18px;
        background: #EEEEEE;
    }

    .my-profile-form .phone {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }

    .my-profile-form .input-phone {
        border-bottom-left-radius: 0;
        border-top-left-radius: 0px;
    }

    .my-profile-form label {
        color: #231F20;
        font-weight: 600;
        font-family: "Open Sans", sans-serif;
        font-size: 16px;
        text-align: left;
        margin-bottom: 12px;
    }

    @media only screen and (max-width: 700px) {
        .group-booking h2 {
            font-size: 24px;
            font-weight: 700;
        }

        .group-booking .content p {
            font-family: "Poppins", sans-serif;
            font-size: 10px;
            font-weight: 600;
        }

        .select-branch h3 {
            font-family: "Poppins", sans-serif;
            padding: 30px 0px 0px;
            font-size: 14px;
            font-weight: 500;
        }

        .select-branch .select-date {
            font-size: 10px;
            padding: 10px 5px;
            color: #000000;
        }

        .select-branch .select-date:hover {
            font-size: 10px;
            background: #C45F44;
            color: #FFFFFF;
            padding: 10px 5px;
        }

        .grp-booking-body {
            border-bottom: 0px solid #C45F44;
            padding: 40px 0px 0px;
        }

        .grp-booking-body-content {
            border-bottom: 2px solid #C45F44;
            padding: 0px 0px 20px;
        }

        .time-info .italic {
            font-size: 11px;
        }

        .select-branch-grid-content {
            padding: 0px 0px 30px;
        }

        .Participants h1 {
            font-size: 18px;
            font-weight: 700;
        }

        .Participants .head .increment-decrement {
            width: 110px;
            background: #FDEDE0;
            border-radius: 12px;
            padding: 5px;
            margin: 0px;
            justify-content: space-between;
        }

        .Participants {
            background: #FFFFFF;
        }

        .Participants .head .increment-decrement span {
            margin: 0px 5px;
            line-height: 12px;
        }

        .Participants .head .increment-decrement #decreaseBtn,
        .Participants .head .increment-decrement #increaseBtn {
            height: 23px;
            width: 23px !important;
            margin: 0px;
            font-weight: 600;
            line-height: 12px;
            padding-bottom: 3px;
        }

        .Participants #accordionContainer .rounded-md {
            border-radius: 11px;
            padding: 10px;
            font-size: 15px;
            font-weight: 600;
        }

        .form-content-title {
            font-size: 14px;
            font-weight: 400;
        }

        .form-content {
            justify-content: space-between;
        }

        .form-content input,
        .form-content select {
            border: 1px solid #6C6C6C;
            border-radius: 20px;
            background: #fff;
            padding: 13px 5px;
        }

        .form-content select {
            color: #00000033
        }

        .form-content .select-state {
            border: 1px solid #6C6C6C;
            border-radius: 20px;
            background: #fff;
            padding: 5px 10px;
        }

        .form-phone .code {
            border-top-left-radius: 20px;
            border-bottom-left-radius: 20px;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .form-phone .phone {
            border-top-right-radius: 20px;
            border-bottom-right-radius: 20px;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }

        .form-content .country {
            font-size: 16px;
            font-weight: 500;
            color: #344054;
            font-family: "Inter", sans-serif;
        }

        .head-sec-title {
            font-size: 18px;
            font-weight: 700;
            color: #6A9E74;
            text-transform: capitalize;
        }

        .cost-price {
            font-size: 18px;
            font-weight: 700;
            font-family: "Open Sans", sans-serif;

        }

        .cost-title {
            font-family: "Open Sans", sans-serif;
            font-size: 12px;
            font-weight: 600;
            color: #344054;
        }

        .item-left {
            font-family: "Open Sans", sans-serif;
            font-size: 12px;
            font-style: italic;
            font-weight: 600;
            color: #00AE4D;
        }

        .full-booked {
            font-family: "Open Sans", sans-serif;
            font-size: 12px;
            font-style: italic;
            font-weight: 600;
            color: #000000;
        }

        .billingCurrency-body label {
            font-size: 14px;
            font-weight: 600;
            font-family: "Open Sans", sans-serif;
            color: #000000;
        }

        .billingCurrency-body .relative select {
            font-size: 14px;
            font-weight: 700;
            font-family: "Open Sans", sans-serif;
            color: #000000;
        }

        .exc-rate .title {
            font-style: italic;
            font-size: 12px;
            font-weight: 600;
            font-family: "Open Sans", sans-serif;
            text-decoration: underline;
        }

        .exc-rate .list-rate {
            font-style: italic;
            font-size: 12px;
            font-weight: 600;
            color: #000000;
        }

        .exc-rate .Disclaimer {
            color: #888888;
            font-size: 8px;
            font-weight: 600;
            font-style: italic;
        }

        .billingCurrency-element {
            border-radius: 6px;
        }

        .billed .title {
            font-size: 14px;
            font-weight: 400;
            font-family: "Open Sans", sans-serif;
        }

        .billed .body select {
            font-size: 15px;
            color: #983419;
            font-weight: 600;
        }

        .privacy-policy p {
            color: #000000;
            font-size: 10px;
            font-family: "Poppins", sans-serif;
        }

        .privacy-policy a {
            font-weight: 500;
            color: #0F43CA;
            font-size: 10px;
            font-family: "Poppins", sans-serif;
        }

        .payment-summary button {
            font-size: 15px;
            font-weight: 700;
            color: #283148;
            font-family: "Open Sans", sans-serif;
        }

        #payment-summary-content h2 {
            font-size: 14px;
            color: #000000;
            font-weight: 700;
            font-family: "Open Sans", sans-serif;
        }

        .about-user {
            font-size: 12px;
            color: #727272;
            font-weight: 600;
            font-family: "Open Sans", sans-serif;
        }

        .cost-breakdown {
            font-style: italic;
            font-size: 14px;
            font-weight: 400;
            font-family: "Open Sans", sans-serif;
        }

        .cost-breakdown .text-red-600 {
            color: #62200E;
            font-weight: 600;
        }

        .cost-breakdown .discount-applied .text-red-600 {
            color: #FF0000;
            font-weight: 600;
        }

        .cost-breakdown .new {
            font-weight: 700;
            font-family: "Open Sans", sans-serif;
        }

        .cost-breakdown .total .text-red-600 {
            font-weight: 700;
        }

        .discount button {
            color: #479456;
        }

        .total-before-tax span {
            font-weight: 600;
            font-size: 14px;
            font-family: "Open Sans", sans-serif;
            color: #000000;
        }

        .total-before-tax .value {
            font-weight: 700;
            font-size: 16px;
            font-family: "Open Sans", sans-serif;
            color: #000000;
        }

        .Applicable-tax .value {
            font-weight: 700;
            font-size: 12px;
            font-family: "Open Sans", sans-serif;
            color: #505050;
        }

        /* booking success */
        .my-profile-banner {
            background: url(../../../img/profile-banner.png) no-repeat bottom center;
            background-size: cover;
            height: 100%;
            width: 100%;
            bottom: -25%;
        }
    }

    @media only screen and (max-width: 390px) {
        .Participants .head .increment-decrement span {
            margin: 0px 5px;
            line-height: 12px;
            font-size: 17px;
        }

        .my-profile-form input,
        .my-profile-form select,
        .my-profile-form .dob {
            border-radius: 20px;
            font-weight: 600;
            font-family: "Open Sans", sans-serif;
            font-size: 14px;
            background: #EEEEEE;
        }

        .my-profile-form label {
            font-size: 15px;
        }
    }
</style>
  <section class="group-booking grp-booking-body-content">

        <div class="bg-white font-sans flex justify-center py-10">

            <div class="w-full  rounded-lg  ">
                <h2 class="text-2xl font-bold text-center mb-6 text-[#1c274c]">My Profile</h2>

                <!-- Profile Picture -->
                <div class="flex justify-center relative mb-6 pro-pic">
                    <div class="absolute bottom-0 left-0 w-full h-20 my-profile-banner rounded-t-lg z-0"></div>
                    <div class="z-10 mb-6">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Profile"
                            class="w-28 h-28 rounded-full border-4 border-white shadow-md" />
                    </div>
                </div>
                <div class="container mx-auto px-4">
                    <!-- Flash Messages -->
                    <?= $this->Flash->render() ?>

                    <!-- Form Fields -->
                   <?= $this->Form->create(null, [
                    'url' => ['controller' => 'Profile', 'action' => 'updateProfile'],
                    'type' => 'post', // ← This should be outside 'url'
                    'class' => 'space-y-4 my-profile-form'
                ]) ?>

                        <div>
                            <label class="block text-sm font-medium">First Name</label>
                            <?= $this->Form->control('first_name', [
                                'type' => 'text',
                                'value' => $user->first_name ?? '',
                                'class' => 'w-full border rounded-lg px-4 py-2 bg-[#EEEEEE] border border-[#6C6C6C]',
                                'label' => false,
                                'required' => true
                            ]) ?>
                        </div>

                        <div>
                            <label class="block text-sm font-medium">Last Name</label>
                            <?= $this->Form->control('last_name', [
                                'type' => 'text',
                                'value' => $user->last_name ?? '',
                                'class' => 'w-full border rounded-md px-4 py-2',
                                'label' => false,
                                'required' => true
                            ]) ?>
                        </div>

                        <div>
                            <label class="block text-sm font-medium">Email ID</label>
                            <?= $this->Form->control('email', [
                                'type' => 'email',
                                'value' => $user->email ?? '',
                                'class' => 'w-full border rounded-md px-4 py-2',
                                'label' => false,
                                'required' => true,
                                'readonly' => true
                            ]) ?>
                        </div>

                         <div>
                            <label class="block text-sm font-medium">Mobile No</label>
                            <div class="flex">
                                <select class="border rounded-l-md px-2 phone">
                                    <option >+91</option>
                                    <option>+1</option>
                                    <option>+44</option>
                                    <option>+61</option>
                                    <option>+91</option>

                                </select>
                                <input type="text" value="<?= $user->mobile ?? ''?> " name="mobile" required  readonly
                                    class=" input-phone w-full border-t border-b border-r rounded-r-md px-4 py-2"  />
                            </div>
                        </div>

                        <!-- <div>
                            <label class="block text-sm font-medium">Mobile No</label>
                            <div class="flex">
                                <?= $this->Form->control('country_code', [
                                    'type' => 'select',
                                    'options' => [
                                        '+91' => '+91',
                                        '+1' => '+1',
                                        '+44' => '+44',
                                        '+61' => '+61'
                                    ],
                                    'value' => $user->country_code ?? '+91',
                                    'class' => 'border rounded-l-md px-2 phone',
                                    'label' => false,
                                     'readonly' => true
                                ]) ?>
                                <?= $this->Form->control('mobile', [
                                    'type' => 'text',
                                    'value' => $user->mobile ?? '',
                                    'class' => 'input-phone w-full border-t border-b border-r rounded-r-md px-4 py-2',
                                    'label' => false,
                                    'required' => true
                                ]) ?>
                            </div>
                        </div> -->

                        <div class="flex gap-4">
                            <div class="w-1/2">
                                <label class="block text-sm font-medium">Gender</label>
                                <?= $this->Form->control('gender', [
                                    'type' => 'select',
                                    'options' => [
                                        '' => 'Select Gender',
                                        'Male' => 'Male',
                                        'Female' => 'Female',
                                        'Other' => 'Other'
                                    ],
                                    'value' => $customer->gender ?? '',
                                    'class' => 'w-full border rounded-md px-4 py-2',
                                    'label' => false
                                ]) ?>
                            </div>
                            <div class="w-1/2">
                                <label class="block text-sm font-medium">DOB</label>
                                <?= $this->Form->control('dob', [
                                    'type' => 'date',
                                    'value' => $customer->dob ? $customer->dob->format('Y-m-d') : '',
                                    'class' => 'w-full border rounded-md px-4 py-2',
                                    'label' => false
                                ]) ?>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium">Location</label>
                            <input type="text" value="<?= $customer->address ?? ''?>" class="w-full border rounded-md px-4 py-2" />
                        </div>
                       
                       
                        <?= $this->Form->button('UPDATE PROFILE', [
                            'type' => 'submit',
                            'class' => 'edit-btn w-full bg-[#f17957] hover:bg-[#e26c4a] text-white font-semibold py-2 rounded-md'
                        ]) ?>

                        <?= $this->Html->link('CHANGE PASSWORD',
                            ['controller' => 'Profile', 'action' => 'changePassword'],
                            ['class' => 'change-password w-full text-[#f17957] font-semibold border border-[#f17957] hover:bg-[#f1795744] py-2 rounded-md text-center block mt-4']
                        ) ?>

                    <?= $this->Form->end() ?>
                </div>
            </div>
        </div>
    </section>


