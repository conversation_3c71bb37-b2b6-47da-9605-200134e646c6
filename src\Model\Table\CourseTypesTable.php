<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * CourseTypes Model
 *
 * @property \App\Model\Table\CoursesTable&\Cake\ORM\Association\HasMany $Courses
 *
 * @method \App\Model\Entity\CourseType newEmptyEntity()
 * @method \App\Model\Entity\CourseType newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\CourseType> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\CourseType get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\CourseType findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\CourseType patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\CourseType> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\CourseType|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\CourseType saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\CourseType>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\CourseType>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\CourseType>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\CourseType> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\CourseType>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\CourseType>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\CourseType>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\CourseType> deleteManyOrFail(iterable $entities, array $options = [])
 */
class CourseTypesTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('course_types');
        $this->setDisplayField('name');
        $this->setPrimaryKey('id');

        $this->hasMany('Courses', [
            'foreignKey' => 'course_type_id',
        ]);

        $this->belongsToMany('Partners', [
            'through' => 'PartnerCourseTypes',
            'foreignKey' => 'course_type_id',
            'targetForeignKey' => 'partner_id'
        ]);

    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->scalar('name')
            ->maxLength('name', 100)
            ->requirePresence('name', 'create')
            ->notEmptyString('name')
            ->add('name', 'unique', ['rule' => 'validateUnique', 'provider' => 'table']);

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->isUnique(['name']), ['errorField' => 'name']);

        return $rules;
    }

    public function getAll(){
        $result = $query = $this->find()->where([
            'status' => 'A'
        ])->all();

        return $result;
    }

    public function selectInputOptions(){
        $types = $this->find()
        ->where([
            'status' => 'A'
        ])
        ->select(['name'])
        ->all()
        ->indexBy('name')
        ->extract('name')
        ->toArray();

        return $types;
    }

    public function findBySlug($slug){
        $result = $this->find()
        ->select('name')    
        ->where([
            'slug' => $slug
        ])->first();

       return $result;
    }

    public function getNameList(){
         $result = $this->find('list', [
            'keyField' => 'id',  
            'valueField' => 'name' 
        ])->toArray();
        
        return $result;
    }
}
