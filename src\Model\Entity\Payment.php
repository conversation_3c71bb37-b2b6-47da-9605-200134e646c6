<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * Payment Entity
 *
 * @property int $id
 * @property int $booking_id
 * @property int $payment_method_id
 * @property string $total_amount
 * @property string $tax_amount
 * @property string|null $transaction_id
 * @property string $payment_status
 * @property \Cake\I18n\DateTime $payment_date
 * @property \Cake\I18n\DateTime $created_at
 * @property \Cake\I18n\DateTime $modified_at
 *
 * @property \App\Model\Entity\Booking $booking
 * @property \App\Model\Entity\PaymentMethod $payment_method
 */
class Payment extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'booking_id' => true,
        'payment_method_id' => true,
        'total_amount' => true,
        'tax_amount' => true,
        'transaction_id' => true,
        'payment_status' => true,
        'payment_date' => true,
        'created_at' => true,
        'modified_at' => true,
        'booking' => true,
        'payment_method' => true,
    ];
}