<?php

declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\I18n\DateTime;
use Cake\Database\Expression\QueryExpression;
use Cake\Database\Query;

/**
 * Partners Model
 *
 * @property \App\Model\Table\UsersTable&\Cake\ORM\Association\BelongsTo $Users
 * @property \App\Model\Table\CentreTypesTable&\Cake\ORM\Association\BelongsTo $PartnerTypes
 * @property \App\Model\Table\CountriesTable&\Cake\ORM\Association\BelongsTo $Countries
 * @property \App\Model\Table\StatesTable&\Cake\ORM\Association\BelongsTo $States
 * @property \App\Model\Table\CitiesTable&\Cake\ORM\Association\BelongsTo $Cities
 * @property \App\Model\Table\BookingsTable&\Cake\ORM\Association\HasMany $Bookings
 * @property \App\Model\Table\NotificationsTable&\Cake\ORM\Association\HasMany $Notifications
 * @property \App\Model\Table\PartnerSettlementTable&\Cake\ORM\Association\HasMany $PartnerSettlement
 *
 * @method \App\Model\Entity\Partner newEmptyEntity()
 * @method \App\Model\Entity\Partner newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Partner> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Partner get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Partner findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Partner patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Partner> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Partner|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Partner saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Partner>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Partner>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Partner>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Partner> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Partner>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Partner>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Partner>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Partner> deleteManyOrFail(iterable $entities, array $options = [])
 */
class PartnersTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('partners');
        $this->setDisplayField('name');
        $this->setPrimaryKey('id');

        // Primary User relationship (The user who owns this partner)
        $this->belongsTo('Users', [
            'foreignKey' => 'user_id',
            'joinType' => 'LEFT'
        ]);

        // Associated Users relationship (Additional users linked to this partner)
        $this->hasMany('AssociatedUsers', [
            'className' => 'Users',
            'foreignKey' => 'partner_id',
            'dependent' => true,
            'conditions' => ['AssociatedUsers.user_type' => 'partner_user']
        ]);

        $this->hasMany('PartnerYogaStyles', [
            'foreignKey' => 'partner_id',
        ]);

        $this->hasMany('PartnerSpecialNeeds', [
            'foreignKey' => 'partner_id',
        ]);

        $this->belongsToMany('YogaStyles', [
            'foreignKey' => 'partner_id',
            'targetForeignKey' => 'yoga_style_id',
            'joinTable' => 'partner_yoga_styles',
        ]);

        $this->belongsToMany('SpecialNeeds', [
            'foreignKey' => 'partner_id',
            'targetForeignKey' => 'special_need_id',
            'joinTable' => 'partner_special_needs',
        ]);

        $this->belongsToMany('MasterData', [
            'foreignKey' => 'partner_id',
            'targetForeignKey' => 'master_data_id',
            'joinTable' => 'partner_techniques', // assuming you want to map techniques, class types, etc.
            'conditions' => ['MasterData.type IN' => ['technique', 'class_type', 'level']]
        ]);

        // $this->belongsTo('CourseTypes', [
        //     'foreignKey' => 'course_type_id',
        //     'joinType' => 'LEFT', // optional, but common
        // ]);

        $this->hasMany('PartnerCourseTypes', [
            'foreignKey' => 'partner_id',
            'dependent' => true,
            'cascadeCallbacks' => true
        ]);

        $this->belongsToMany('CourseTypes', [
            'through' => 'PartnerCourseTypes',
            'foreignKey' => 'partner_id',
            'targetForeignKey' => 'course_type_id'
        ]);

        $this->belongsToMany('Modalities', [
            'joinTable' => 'partner_modalities',
            'foreignKey' => 'partner_id',
            'targetForeignKey' => 'modality_id',
            'className' => 'Modalities'
        ]);

        $this->belongsTo('PartnerTypes', [
            'foreignKey' => 'partner_type_id'
        ]);

        $this->belongsTo('Countries', [
            'foreignKey' => 'country_id'
        ]);

        $this->belongsTo('States', [
            'foreignKey' => 'state_id'
        ]);

        $this->belongsTo('Cities', [
            'foreignKey' => 'city_id'
        ]);

        $this->hasMany('PartnerGalleries', [
            'foreignKey' => 'partner_id',
        ]);

        $this->hasMany('Courses', [
            'foreignKey' => 'partner_id',
        ]);

        $this->hasMany('ReviewRatings', [
            'foreignKey' => 'partner_id'
        ]);

    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        // $validator
        //     ->nonNegativeInteger('user_id')
        //     ->notEmptyString('user_id');

        $validator
            ->nonNegativeInteger('partner_type_id')
            ->allowEmptyString('partner_type_id');

        $validator
            ->scalar('name')
            ->maxLength('name', 255)
            ->requirePresence('name', 'create')
            ->notEmptyString('name');

        $validator
            ->scalar('address')
            ->maxLength('address', 255)
            ->allowEmptyString('address');

        $validator
            ->scalar('old_url')
            ->maxLength('old_url', 255)
            ->allowEmptyString('old_url');

        $validator
            ->scalar('short_description')
            ->maxLength('short_description', 140)
            ->allowEmptyString('short_description');

        $validator
            ->scalar('street_address')
            ->maxLength('street_address', 255)
            ->allowEmptyString('street_address');

        $validator
            ->nonNegativeInteger('country_id')
            ->allowEmptyString('country_id');

        $validator
            ->nonNegativeInteger('state_id')
            ->allowEmptyString('state_id');

        $validator
            ->nonNegativeInteger('city_id')
            ->allowEmptyString('city_id');

        $validator
            ->scalar('zipcode')
            ->maxLength('zipcode', 20)
            ->allowEmptyString('zipcode');

        $validator
            ->scalar('website')
            ->maxLength('website', 255)
            ->allowEmptyString('website');

        $validator
            ->scalar('message')
            ->allowEmptyString('message');

        $validator
            ->dateTime('created_at')
            ->notEmptyDateTime('created_at');

        $validator
            ->dateTime('modified_at')
            ->notEmptyDateTime('modified_at');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        // $rules->add($rules->existsIn(['user_id'], 'Users'), ['errorField' => 'user_id']);
        // $rules->add($rules->existsIn(['partner_type_id'], 'CentreTypes'), ['errorField' => 'partner_type_id']);
        $rules->add($rules->existsIn(['partner_type_id'], 'PartnerTypes'), ['errorField' => 'partner_type_id']);
        $rules->add($rules->existsIn(['country_id'], 'Countries'), ['errorField' => 'country_id']);
        $rules->add($rules->existsIn(['state_id'], 'States'), ['errorField' => 'state_id']);
        $rules->add($rules->existsIn(['city_id'], 'Cities'), ['errorField' => 'city_id']);

        return $rules;
    }

    public function getFeaturesCentres()
    {
        $result = $this->find()
            ->contain([
               'Courses' => [
                'YogaStyles',
                'Bookings'
                ],
                'Countries',
                'States',
                'Cities',
                'ReviewRatings',
            ])
            ->where([
                'Partners.is_featured' => 1,
                'Partners.status' => 'A'
            ])->all();

        return  $result;
    }

    public function getList($filters)
    {
        $search     = $filters['search'];
        $partnerType = $filters['partner_type'];
        $yogaStyle  = $filters['yoga_style'];
        $location   = $filters['location'];
        $courseDate = $filters['course_date'];
        $language   = $filters['language'];
        $site       = $filters['site'];
        $mode       = $filters['mode'];
        $sort       = $filters['sort'];
        $specialNeeds = $filters['special_needs'];
        $region     = $filters['region'];

        $query = $this->find();

        $query->contain([
            'Users',
            'Courses' => [
                'YogaStyles',
                'Bookings'
            ],
            'Countries',
             'States' => ['Regions'], 
            'Cities',
            'ReviewRatings',
            'PartnerTypes'
        ])

        ->select($this) // Select all course fields
        ->select($this->Countries)
        ->select($this->States)
         ->select($this->States->Regions)
        ->select($this->Cities)
        ->select($this->PartnerTypes)
        // ->select($this->ReviewRatings)
        ->leftJoinWith('Courses.Bookings')
        ->select([
            'bookings_count' => $query->func()->count('Bookings.id')
        ])
        ->distinct(['Partners.id']); // Important when using matching()

        if ($search) {
            $query->where(['Partners.name like' => '%' . trim($search) . '%']);
        }

        if (!empty($partnerType) && $partnerType != 'all') {
            $types = array_map('urldecode', explode(',', $partnerType));
            $types = array_filter($types);

            $ptypes = $this->PartnerTypes
                ->find('list', ['keyField' => 'slug', 'valueField' => 'id'])
                ->where(['slug IN' => $types])
                ->toArray();

            if (!empty($ptypes)) {
                $query->where(['Partners.partner_type_id IN' => array_values($ptypes)]);
            }
        }

        if (!empty($yogaStyle) && $yogaStyle != 'all') {
            $styles = array_map('urldecode', explode(',', $yogaStyle));
            $styles = array_filter($styles);

            // $query->matching('Courses.YogaStyles', function ($q) use ($styles) {
            //     return $q->where(['YogaStyles.slug IN' => $styles]);
            // });

            $query->matching('YogaStyles', function ($q) use ($styles) {
                return $q->where(['YogaStyles.slug IN' => $styles]);
            });
        }

        if (!empty($specialNeeds) && $specialNeeds != 'all') {
            $special = array_map('urldecode', explode(',', $specialNeeds));
            $special = array_filter($special);

            // $query->matching('Courses.SpecialNeeds', function ($q) use ($special) {
            //     return $q->where(['SpecialNeeds.slug IN' => $special]);
            // });

            $query->matching('SpecialNeeds', function ($q) use ($special) {
                return $q->where(['SpecialNeeds.slug IN' => $special]);
            });
        }

          if (!empty($region) && $region != 'all') {
            $regionName = str_replace('-', ' ' ,trim($region));
        
            // Get region IDs matching the region name
            $regionIds = $this->States->Regions->find()
                ->select(['id'])
                ->where(['Regions.name LIKE' => '%' . $regionName . '%'])
                ->enableHydration(false)
                ->all()
                ->extract('id')
                ->toArray();
            
            if (!empty($regionIds)) {
                // Get state IDs for those regions
                $stateIds = $this->States->find()
                    ->select(['id'])
                    ->where(['region_id IN' => $regionIds])
                    ->enableHydration(false)
                    ->all()
                    ->extract('id')
                    ->toArray();

                if (!empty($stateIds)) {
                    // Filter courses whose state_id is in those states
                    $query->where(['Partners.state_id IN' => $stateIds]);
                }
            }
        }

        if (!empty($location)) {
            $location =  trim($location);
            $conditions = [];

            $countryIds = $this->Countries->find()
                ->select(['id'])
                ->where(['name LIKE' => '%' . $location . '%'])
                ->enableHydration(false)
                ->all()
                ->extract('id')
                ->toArray();

            if (!empty($countryIds)) {
                $conditions[] = ['Partners.country_id IN' => $countryIds];
            }

            $stateIds = $this->States->find()
                ->select(['id'])
                ->where(['name LIKE' => '%' . $location . '%'])
                ->enableHydration(false)
                ->all()
                ->extract('id')
                ->toArray();

            if (!empty($stateIds)) {
                $conditions[] = ['Partners.state_id IN' => $stateIds];
            }

            $cityIds = $this->Cities->find()
                ->select(['id'])
                ->where(['name LIKE' => '%' . $location . '%'])
                ->enableHydration(false)
                ->all()
                ->extract('id')
                ->toArray();

            if (!empty($cityIds)) {
                $conditions[] = ['Partners.city_id IN' => $cityIds];
            }

            $conditions[] = ['Partners.address LIKE' => '%' . $location . '%'];

            // Apply OR conditions
            $query->where(function ($exp, $q) use ($conditions) {
                return $exp->or($conditions);
            });
        }

        if (!empty($courseDate) && $courseDate != 'all') {
            $start_dates = array_map('urldecode', explode(',', $courseDate));
            $startDates = array_filter($start_dates);
            $today = DateTime::today();
            $formattedDates = [];

            foreach ($startDates as $monthYear) {
                $date = DateTime::createFromFormat('F Y', $monthYear);
                if ($date) {
                    $formattedDates[] = $date->format('Y-m');
                }
            }

            if (!empty($formattedDates)) {
                $query->matching('Courses.CourseBatches', function ($q) use ($formattedDates, $today) {
                    return $q->where(function ($exp, $q) use ($formattedDates, $today) {
                $monthConditions = [];

                         foreach ($formattedDates as $formatted) {
                    $monthConditions[] = "DATE_FORMAT(CourseBatches.start_date, '%Y-%m') = '{$formatted}'";
                }

                        if (!empty($monthConditions)) {
                            $exp->add(['(' . implode(' OR ', $monthConditions) . ')']);
                        }

                        // Only future or present dates
                        $exp->gte('CourseBatches.start_date', $today->format('Y-m-d'));

                        return $exp;
                    });
                });
            }
        }

        if (!empty($language) && $language != 'all') {
            $languages = array_map('urldecode', explode(',', $language));
            $languages = array_filter($languages);

            $query->matching('Courses', function ($q) use ($languages) {
                return $q->where(['Courses.language IN' => $languages]);
            });

            // from partner
            // $query->where(['Partners.language IN' => $languages]);

        }

        // if (!empty($site) && $site == 'on_site') {
        //     $query->matching('Courses', function ($q) {
        //         return $q->where(['Courses.on_site' => 1]);
        //     });
        // }

        // if (!empty($mode)) {
        //     $modesArr = array_map('urldecode', explode(',', $mode));
        //     $conditions = [];

        //     if (in_array('live', $modesArr)) {
        //         $conditions[] = ['Courses.online_live' => 1];
        //     }

        //     if (in_array('vod', $modesArr)) {
        //         $conditions[] = ['Courses.online_vod' => 1];
        //     }

        //     if (count($conditions) > 0) {
        //         $query->matching('Courses', function ($q) use ($conditions) {
        //             return $q->where([
        //                 'OR' => $conditions
        //             ]);
        //         });
        //     }
        // }

        $query->where([
            'Partners.status' => 'A',
            'PartnerTypes.name !=' => 'Teachers'
        ]);


        switch ($sort) {
            case 'popular':
                $query->orderDesc('bookings_count');
                break;
            case 'featured':
                $query->orderDesc('Partners.is_featured');
                break;
                
            case 'rating':
                // $query->orderDesc('ReviewRatings.rating');
                break;
            case 'newest':
                $query->orderDesc('Partners.created_at');
                break;
            case 'oldest':
                $query->orderAsc('Partners.created_at');
                break;
            default:
                $query->orderDesc('Partners.created_at');
                break;
        }

        return  $query;
    }

    public function getFilterCounts($baseFilters)
    {
        $results = [];

        // Prepare each filter type
        $partnerTypes = $this->PartnerTypes->getSlugNameList();
        $yogaStyles = $this->Courses->YogaStyles->getSlugNameList();
        $specialNeeds = $this->Courses->SpecialNeeds->getSlugNameList();
        $languages = $this->Courses->find()->distinct(['language'])->all()->extract('language')->toArray();
      
        // Site type: On Site
        $siteTypes = ['on_site' => 1];

        // Modes
        // $modes = ['live', 'vod'];
        $modes = $this->Modalities->selectInputOptions();

        $courseBatchesTable = \Cake\ORM\TableRegistry::getTableLocator()->get('CourseBatches');

       $dateStrings = $courseBatchesTable->find()
        ->select(['start_date'])
        ->contain(['Courses' => function ($q) {
            return $q->where(['Courses.status' => 'A']);
        }])
        ->where(['CourseBatches.status' => 'active'])
        ->enableHydration(false)
        ->all()
        ->extract(function ($row) {
            return !empty($row['start_date']) ? $row['start_date']->format('F Y') : null;
        })
        ->filter() // remove nulls
        ->unique()
        ->toArray();

        $courseDates = array_values($dateStrings);

        // === PARTNER TYPES ===
        $partnerCounts = [];
        foreach ($partnerTypes as $slug => $name) {
            $filters = $baseFilters;
            $filters['partner_type'] = urlencode($slug);
            $partnerCounts[$slug] = $this->getList($filters)->count();
        }
        $results['partner_types'] = $partnerCounts;

        // === YOGA STYLES ===
        $styleCounts = [];
        foreach ($yogaStyles as $slug => $name) {
            $filters = $baseFilters;
            $filters['yoga_style'] = urlencode($slug);
            $styleCounts[$slug] = $this->getList($filters)->count();
        }
        $results['yoga_styles'] = $styleCounts;

        // === LANGUAGES ===
        $langCounts = [];
        foreach ($languages as $lang) {
            $filters = $baseFilters;
            $filters['language'] = $lang;
            $langCounts[$lang] = $this->getList($filters)->count();
        }
        $results['languages'] = $langCounts;

        // === SPECIAL NEEDS ===
        $specialCounts = [];
        foreach ($specialNeeds as $slug => $name) {
            $filters = $baseFilters;
            $filters['special_needs'] = urlencode($slug);
            $specialCounts[$slug] = $this->getList($filters)->count();
        }
        $results['special_needs'] = $specialCounts;

        // === SITE TYPES ===
        $siteCounts = [];
        foreach ($siteTypes as $key => $value) {
            $filters = $baseFilters;
            $filters['site'] = $key;
            $siteCounts[$key] = $this->getList($filters)->count();
        }
        $results['site_types'] = $siteCounts;

        // === COURSE DATES ===
        $dateCounts = [];
        foreach ($courseDates as $dateStr) {
            if ($dateStr) {
                $filters = $baseFilters;
                $filters['course_date'] = urlencode($dateStr);
                $dateCounts[$dateStr] = $this->getList($filters)->count();
            }
        }

        $results['course_dates'] = $dateCounts;

        // === MODES ===
        $modeCounts = [];
        foreach ($modes as $mode) {
            $filters = $baseFilters;
            $filters['mode'] = $mode;
            $modeCounts[$mode] = $this->getList($filters)->count();
        }
        $results['modes'] = $modeCounts;

        return $results;
    }
}
