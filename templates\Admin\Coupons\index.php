<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Coupon> $coupons
 */
?>
<?= $this->Html->meta('csrfToken', $this->request->getAttribute('csrfToken')) ?>
<style>
    th.no-sort::after,
    th.no-sort::before {
        display: none !important;
    }

    th.no-sort {
        cursor: default !important;
        background-image: none !important;
    }
    
    td a {
        color: black;
    }
    
    .badge {
        font-size: 0.75em;
    }
    
    .coupon-code {
        font-family: monospace;
        background: #f8f9fa;
        padding: 2px 6px;
    }

    .table td, .table th {
        vertical-align: middle;
        text-align: center;
    }

    .table td:first-child, .table th:first-child {
        text-align: left;
    }

    .form-control, .form-select {
        height: 38px;
    }

    .row.g-3 .col-md-2, .row.g-3 .col-md-3 {
        margin-bottom: 1rem;
        border-radius: 3px;
        font-weight: bold;
    }
    #filterForm {
        padding-left: 25px;
    }

    .active-filters .badge {
        font-size: 0.85em;
        margin-right: 5px;
        margin-bottom: 5px;
    }

    .filter-toggle .badge {
        font-size: 0.7em;
        margin-left: 5px;
    }

    .alert-info {
        border-left: 4px solid #17a2b8;
    }

    /* Sortable column styling */
    .sortable {
        cursor: pointer;
        user-select: none;
        transition: background-color 0.2s ease;
    }

    .sortable:hover {
        background-color: #f8f9fa;
    }

    .sortable i {
        margin-left: 5px;
        opacity: 0.6;
    }

    .sortable:hover i {
        opacity: 1;
    }
</style>
<?php $this->append('style'); ?>
<?php $this->end(); ?>

<div class="main-content">
    <section class="section">
        <div class="section-body1">
            <div class="container-fluid">
                <?php
                $successMessage = $this->Flash->render('success');
                $errorMessage = $this->Flash->render('error');
                ?>
                <?php if (!empty($successMessage)): ?>
                    <?= $successMessage ?>
                <?php elseif (!empty($errorMessage)): ?>
                    <?= $errorMessage ?>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="section-body" id="list">
            <div class="container-fluid">
                <div class="card card-primary">
                    <ul class="breadcrumb breadcrumb-style">
                        <li class="breadcrumb-item">Marketing</li>
                        <li class="breadcrumb-item">Coupons List</li>
                    </ul>
                    
                    <div class="card-header">
                        <div class="d-block d-sm-flex actions">
                            <div class="action-header">
                                <h4>Coupons Management</h4>
                            </div>
                            <div class="filter-options">
                                <div class="action-button">
                                    <button onclick="window.location.href='<?= $this->Url->build(['controller' => 'Coupons', 'action' => 'add']) ?>'">
                                        <i class="fas fa-plus"></i> <?= __("Add Coupon") ?>
                                    </button>
                                </div>
                                <div class="filter-button">
                                    <div class="btn-group">
                                        <?php
                                        $hasFilters = !empty($this->request->getQuery('status')) ||
                                                     !empty($this->request->getQuery('discount_type')) ||
                                                     !empty($this->request->getQuery('search'));
                                        ?>
                                        <button type="button" class="btn <?= $hasFilters ? 'btn-success' : 'btn-primary' ?> filter-toggle">
                                            <i class="fas fa-filter"></i> Filter
                                            <!-- <?php if ($hasFilters): ?>
                                                <span class="badge bg-light text-dark ms-1"><?= count(array_filter($this->request->getQueryParams())) ?></span>
                                            <?php endif; ?> -->
                                        </button>
                                    </div>
                                </div>
                                <div class="download-icon">
                                    <button class="btn btn-primary" id="downloadCoupons">
                                        <i class="fas fa-file-download"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Active Filters Display -->
                    <!-- <?php if ($hasFilters): ?>
                        <div class="alert alert-info mb-3 active-filters">
                            <strong><i class="fas fa-filter"></i> Active Filters:</strong>
                            <?php if (!empty($this->request->getQuery('status'))): ?>
                                <span class="badge bg-primary me-1">Status: <?= h($this->request->getQuery('status')) ?></span>
                            <?php endif; ?>
                            <?php if (!empty($this->request->getQuery('discount_type'))): ?>
                                <span class="badge bg-primary me-1">Type: <?= ucfirst(h($this->request->getQuery('discount_type'))) ?></span>
                            <?php endif; ?>
                            <?php if (!empty($this->request->getQuery('search'))): ?>
                                <span class="badge bg-primary me-1">Search: "<?= h($this->request->getQuery('search')) ?>"</span>
                            <?php endif; ?>
                            <a href="<?= $this->Url->build(['action' => 'index']) ?>" class="btn btn-sm btn-outline-secondary ms-2">
                                <i class="fas fa-times"></i> Clear All
                            </a>
                        </div>
                    <?php endif; ?> -->

                    <!-- Filters -->
                        <div id="filterForm" style="display: <?= empty($this->request->getQuery()) ? 'none' : 'block' ?>;">
                            <?= $this->Form->create(null, ['type' => 'get', 'class' => 'mb-4']) ?>

                            <!-- Preserve current page when filtering -->
                            <?php if ($this->request->getQuery('page')): ?>
                                <?= $this->Form->hidden('page', ['value' => $this->request->getQuery('page')]) ?>
                            <?php endif; ?>

                            <div class="row">
                                <div class="col-md-3">
                                    <?= $this->Form->control('status', [
                                        'label' => 'Status',
                                        'type' => 'select',
                                        'options' => ['' => 'All', 'Active' => 'Active', 'Inactive' => 'Inactive'],
                                        'value' => $this->request->getQuery('status'),
                                        'class' => 'form-control'
                                    ]) ?>
                                </div>
                                <div class="col-md-3">
                                    <?= $this->Form->control('discount_type', [
                                        'label' => 'Discount Type',
                                        'type' => 'select',
                                        'options' => ['' => 'All', 'percentage' => 'Percentage', 'fixed' => 'Fixed'],
                                        'value' => $this->request->getQuery('discount_type'),
                                        'class' => 'form-control'
                                    ]) ?>
                                </div>
                                <div class="col-md-2">
                                    <?= $this->Form->control('search', [
                                        'label' => 'Search',
                                        'value' => $this->request->getQuery('search'),
                                        'class' => 'form-control',
                                        'placeholder' => 'Search coupons...'
                                    ]) ?>
                                </div>
                                <div class="col-md-2">
                                    <?= $this->Form->control('limit', [
                                        'label' => 'Show Entries',
                                        'type' => 'select',
                                        'options' => [10 => '10', 25 => '25', 50 => '50', 100 => '100'],
                                        'value' => $this->request->getQuery('limit', 10),
                                        'class' => 'form-control',
                                        'onchange' => 'this.form.submit()'
                                    ]) ?>
                                </div>
                                <div class="col-md-2 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary me-2">Filter</button>
                                    <a href="<?= $this->Url->build(['action' => 'index']) ?>" class="btn btn-secondary">Reset</a>
                                </div>
                            </div>
                            <?= $this->Form->end() ?>
                        </div>
                    <!-- Filters -->
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped dataTable no-footer display nowrap table-hover border" id="coupon-table">
                                <thead>
                                    <tr>
                                        <th class="no-sort"><?= __("Actions") ?></th>
                                        <th class="sortable" data-sort="id">
                                            <?= __("ID") ?>
                                            <?php if (isset($sort) && $sort === 'id'): ?>
                                                <i class="fas fa-sort-<?= strtolower($direction) === 'asc' ? 'up' : 'down' ?>"></i>
                                            <?php else: ?>
                                                <i class="fas fa-sort"></i>
                                            <?php endif; ?>
                                        </th>
                                        <th class="sortable" data-sort="code">
                                            <?= __("Code") ?>
                                            <?php if (isset($sort) && $sort === 'code'): ?>
                                                <i class="fas fa-sort-<?= strtolower($direction) === 'asc' ? 'up' : 'down' ?>"></i>
                                            <?php else: ?>
                                                <i class="fas fa-sort"></i>
                                            <?php endif; ?>
                                        </th>
                                        <th class="sortable" data-sort="title">
                                            <?= __("Title") ?>
                                            <?php if (isset($sort) && $sort === 'title'): ?>
                                                <i class="fas fa-sort-<?= strtolower($direction) === 'asc' ? 'up' : 'down' ?>"></i>
                                            <?php else: ?>
                                                <i class="fas fa-sort"></i>
                                            <?php endif; ?>
                                        </th>
                                        <th class="sortable" data-sort="discount_type">
                                            <?= __("Type") ?>
                                            <?php if (isset($sort) && $sort === 'discount_type'): ?>
                                                <i class="fas fa-sort-<?= strtolower($direction) === 'asc' ? 'up' : 'down' ?>"></i>
                                            <?php else: ?>
                                                <i class="fas fa-sort"></i>
                                            <?php endif; ?>
                                        </th>
                                        <th class="sortable" data-sort="discount_value">
                                            <?= __("Value") ?>
                                            <?php if (isset($sort) && $sort === 'discount_value'): ?>
                                                <i class="fas fa-sort-<?= strtolower($direction) === 'asc' ? 'up' : 'down' ?>"></i>
                                            <?php else: ?>
                                                <i class="fas fa-sort"></i>
                                            <?php endif; ?>
                                        </th>
                                        <th class="no-sort"><?= __("Usage") ?></th>
                                        <th class="sortable" data-sort="end_date">
                                            <?= __("Valid Until") ?>
                                            <?php if (isset($sort) && $sort === 'end_date'): ?>
                                                <i class="fas fa-sort-<?= strtolower($direction) === 'asc' ? 'up' : 'down' ?>"></i>
                                            <?php else: ?>
                                                <i class="fas fa-sort"></i>
                                            <?php endif; ?>
                                        </th>
                                        <th class="sortable" data-sort="status">
                                            <?= __("Status") ?>
                                            <?php if (isset($sort) && $sort === 'status'): ?>
                                                <i class="fas fa-sort-<?= strtolower($direction) === 'asc' ? 'up' : 'down' ?>"></i>
                                            <?php else: ?>
                                                <i class="fas fa-sort"></i>
                                            <?php endif; ?>
                                        </th>
                                        <th class="sortable" data-sort="created_at">
                                            <?= __("Created") ?>
                                            <?php if (isset($sort) && $sort === 'created_at'): ?>
                                                <i class="fas fa-sort-<?= strtolower($direction) === 'asc' ? 'up' : 'down' ?>"></i>
                                            <?php else: ?>
                                                <i class="fas fa-sort"></i>
                                            <?php endif; ?>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($coupons as $coupon): ?>
                                    <tr>
                                        <td class="no-sort">

                                                <a href="/admin/coupons/view/<?= $coupon->id ?>"
                                                   class="" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="/admin/coupons/edit/<?= $coupon->id ?>"
                                                   class="" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <!-- <button class="toggle-status" 
                                                        data-id="<?= $coupon->id ?>" 
                                                        data-status="<?= $coupon->status ?>" 
                                                        title="Toggle Status">
                                                    <i class="fas fa-power-off"></i>
                                                </button>
                                                <button class="delete-coupon" 
                                                        data-id="<?= $coupon->id ?>" 
                                                        data-code="<?= h($coupon->code) ?>" 
                                                        title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button> -->
                                                <!-- <a href="#" 
                                            class="toggle-status" 
                                            data-id="<?= $coupon->id ?>" 
                                            data-status="<?= $coupon->status ?>" 
                                            title="Toggle Status" 
                                            role="button">
                                                <i class="fas fa-power-off"></i>
                                            </a> -->

                                            <a href="#" 
                                            class="delete-coupon" 
                                            data-id="<?= $coupon->id ?>" 
                                            data-code="<?= h($coupon->code) ?>" 
                                            title="Delete" 
                                            role="button">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                           
                                        </td>
                                        <td><?= $coupon->id ?></td>
                                        <td>
                                            <span class="coupon-code"><?= h($coupon->code) ?></span>
                                        </td>
                                        <td>
                                            <?= h($coupon->title) ?>
                                            <?php if ($coupon->description): ?>
                                                <br><small class="text-muted"><?= $this->Text->truncate(h($coupon->description), 50) ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $coupon->discount_type === 'percentage' ? 'success' : 'primary' ?>">
                                                <?= ucfirst(h($coupon->discount_type)) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <strong><?= $coupon->formatted_discount ?></strong>
                                            <?php if ($coupon->min_cart_value): ?>
                                                <br><small class="text-muted">Min: <?= $this->Number->currency($coupon->min_cart_value) ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($coupon->usage_limit): ?>
                                                <?= $this->Number->format($coupon->usage_limit) ?>
                                                <?php if ($coupon->per_user_limit): ?>
                                                    <br><small class="text-muted">Per user: <?= $coupon->per_user_limit ?></small>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-muted">Unlimited</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?= $coupon->end_date ? $coupon->end_date->format('M d, Y') : '<span class="text-muted">No limit</span>' ?>
                                        </td>
                                        <td>
                                            <span class="badge <?= $coupon->status_badge_class ?>">
                                                <?= h($coupon->status) ?>
                                            </span>
                                            <?php if (!$coupon->is_active && $coupon->status === 'Active'): ?>
                                                <br><small class="text-warning">Expired/Not started</small>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= $coupon->created_at->format('M d, Y') ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                <?= $this->Paginator->counter(__('Showing {{start}} to {{end}} of {{count}} entries')) ?>
                            </div>
                            <div>
                                <ul class="pagination">
                                    <?= $this->Paginator->prev('< ' . __('previous'), [
                                        'class' => $this->Paginator->hasPrev() ? '' : 'disabled'
                                    ]) ?>
                                    <?= $this->Paginator->numbers() ?>
                                    <?= $this->Paginator->next(__('next') . ' >', [
                                        'class' => $this->Paginator->hasNext() ? '' : 'disabled'
                                    ]) ?>
                                    <!-- <?= $this->Paginator->last(__('last') . ' >>') ?> -->
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalTitle">Delete Confirmation</h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p class="icon"><i class="fas fa-exclamation-triangle danger"></i></p>
                    <p>Are you sure you want to delete coupon <strong id="couponCode"></strong>?</p>
                </div>
                <div class="modal-footer br">
                    <button type="button" class="btn btn-primary confirm-delete" id="confirmDeleteBtn">Delete</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script>
$(document).ready(function() {
    // Initialize DataTable (disabled for server-side sorting)
    var table = $('#coupon-table').DataTable({
        "paging": false,
        "searching": false,
        "info": false,
        "ordering": false, // Disable client-side sorting
        "columnDefs": [
            { "orderable": false, "targets": 0 }
        ]
    });

    // Reset filters
    window.resetFilters = function() {
        window.location.href = '<?= $this->Url->build(['action' => 'index']) ?>';
    };

    // Delete coupon
    $('.delete-coupon').click(function() {
        var couponId = $(this).data('id');
        var couponCode = $(this).data('code');
        
        $('#couponCode').text(couponCode);
        $('#confirmDeleteBtn').data('id', couponId);
        $('#deleteModal').modal('show');
    });

    $('#confirmDeleteBtn').click(function() {
        var couponId = $(this).data('id');

        var form = $('<form>', {
            'method': 'POST',
            'action': '/admin/coupons/delete/' + couponId
        });

        var csrfToken = $('<input>', {
            'type': 'hidden',
            'name': '_csrfToken',
            'value': $('meta[name="csrfToken"]').attr('content')
        });

        form.append(csrfToken);
        $('body').append(form);
        form.submit();

        $('#deleteModal').modal('hide');
    });

    // Toggle status
    $('.toggle-status').click(function(e) {
        e.preventDefault();
        var couponId = $(this).data('id');
        var currentStatus = $(this).data('status');
        var newStatus = currentStatus === 'Active' ? 'Inactive' : 'Active';

        if (confirm('Are you sure you want to change the status to ' + newStatus + '?')) {
            // Create a form and submit it
            var form = $('<form>', {
                'method': 'POST',
                'action': '/admin/coupons/toggle-status/' + couponId
            });

            var csrfToken = $('<input>', {
                'type': 'hidden',
                'name': '_csrfToken',
                'value': $('meta[name="csrfToken"]').attr('content')
            });

            form.append(csrfToken);
            $('body').append(form);
            form.submit();
        }
    });

    // Download export with current filters
    $('#downloadCoupons').click(function() {
        var exportUrl = '<?= $this->Url->build(['action' => 'export']) ?>';
        var currentParams = window.location.search;

        if (currentParams) {
            exportUrl += currentParams;
        }

        window.open(exportUrl, '_blank');
    });

   $('.filter-toggle').click(function () {
    $('#filterForm').slideToggle();

    // Focus on first filter field when opening
    if ($('#filterForm').is(':visible')) {
        setTimeout(function() {
            $('#filterForm select:first, #filterForm input:first').focus();
        }, 300);
    }
});

// Auto-submit form when filter values change (optional - remove if not desired)
$('#filterForm select').change(function() {
    // Optional: Auto-submit when dropdown changes
    // $(this).closest('form').submit();
});

// Clear individual filter badges
$('.badge').click(function(e) {
    if ($(this).hasClass('clickable')) {
        e.preventDefault();
        // You can add individual filter clearing logic here
    }
});

// Sorting click handler
$('.sortable').click(function(e) {
    e.preventDefault();

    var sortField = $(this).data('sort');
    var currentSort = new URLSearchParams(window.location.search).get('sort');
    var currentDirection = new URLSearchParams(window.location.search).get('direction') || 'desc';

    // Determine new direction
    var newDirection = 'asc';
    if (currentSort === sortField && currentDirection === 'asc') {
        newDirection = 'desc';
    }

    // Build new URL with sorting parameters
    var url = new URL(window.location.href);
    url.searchParams.set('sort', sortField);
    url.searchParams.set('direction', newDirection);
    url.searchParams.set('page', '1'); // Reset to page 1 when sorting

    // Navigate to new URL
    window.location.href = url.toString();
});
});
</script>
<?php $this->end(); ?>
