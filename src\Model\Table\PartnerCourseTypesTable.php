<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * PartnerCourseTypes Model
 *
 * @property \App\Model\Table\PartnersTable&\Cake\ORM\Association\BelongsTo $Partners
 * @property \App\Model\Table\CourseTypesTable&\Cake\ORM\Association\BelongsTo $CourseTypes
 *
 * @method \App\Model\Entity\PartnerCourseType newEmptyEntity()
 * @method \App\Model\Entity\PartnerCourseType newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\PartnerCourseType> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\PartnerCourseType get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\PartnerCourseType findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\PartnerCourseType patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\PartnerCourseType> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\PartnerCourseType|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\PartnerCourseType saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\PartnerCourseType>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\PartnerCourseType>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\PartnerCourseType>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\PartnerCourseType> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\PartnerCourseType>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\PartnerCourseType>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\PartnerCourseType>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\PartnerCourseType> deleteManyOrFail(iterable $entities, array $options = [])
 */
class PartnerCourseTypesTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('partner_course_types');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->belongsTo('Partners', [
            'foreignKey' => 'partner_id',
            'joinType' => 'INNER',
        ]);
        $this->belongsTo('CourseTypes', [
            'foreignKey' => 'course_type_id',
            'joinType' => 'INNER',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->nonNegativeInteger('partner_id')
            ->notEmptyString('partner_id');

        $validator
            ->nonNegativeInteger('course_type_id')
            ->notEmptyString('course_type_id');

        $validator
            ->dateTime('created_at')
            ->allowEmptyDateTime('created_at');

        $validator
            ->dateTime('modified_at')
            ->allowEmptyDateTime('modified_at');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['partner_id'], 'Partners'), ['errorField' => 'partner_id']);
        $rules->add($rules->existsIn(['course_type_id'], 'CourseTypes'), ['errorField' => 'course_type_id']);

        return $rules;
    }
}
