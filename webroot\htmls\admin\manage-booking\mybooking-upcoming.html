<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
</head>

<body>
    <section class="group-booking grp-booking-body-content">

        <div class="mx-auto p-4">
            <h1 class="page-title text-2xl font-bold text-center mb-4">My Bookmarks</h1>
            <!-- Tabs -->
            <div id="tabs" class="flex bg-[#FFEFE9] p-1 rounded-xl mb-4">
                <button data-tab="upcoming"
                    class="tab-btn flex-1 bg-[#D87A61] text-white font-semibold py-2 rounded-lg">Upcoming</button>
                <button data-tab="completed"
                    class="tab-btn flex-1 text-black font-medium py-2 rounded-lg hover:bg-orange-100">Completed</button>
                <button data-tab="cancelled"
                    class="tab-btn flex-1 text-black font-medium py-2 rounded-lg hover:bg-orange-100">Cancelled</button>
            </div>

            <!-- Tab Contents -->
            <div id="tab-content">
                <div id="upcoming" class="tab-content-block">
                    <!-- Card 1 -->
                    <div class=" cards border rounded-lg  mb-2 pb-4">
                        <div class="flex items-center bg-[#D9D9D9] booknumber justify-between">
                            <p class="text-[#000000]">Booking No: 407-9914751-562757</p><a class="text-[#014F9A]"
                                href="/">Invoice</a>
                        </div>
                        <div class="flex ">
                            <img src="https://media-cdn.tripadvisor.com/media/photo-s/0e/77/5a/1a/firecamp.jpg"
                                alt="Course Image" class="w-20 h-20 rounded-lg object-cover mr-4" />
                            <div>
                                <p class="text-sm text-[#D87A61] font-semibold art">@The Art of Living</p>
                                <h3 class="font-bold card-title text-lg text-[#293148]">200-Hour Yoga Teacher Training
                                    in India
                                </h3>
                                <p class="text-sm text-[#D87A61] font-semibold location">Location:Chennai</p>
                            </div>
                        </div>
                        <div class="flex items-center text-sm text-gray-600 mt-1 date-content justify-between py-2">
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"
                                    fill="none">
                                    <path d="M5.33398 1.33301V3.33301" stroke="#C45F44" stroke-miterlimit="10"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                    <path d="M10.666 1.33301V3.33301" stroke="#C45F44" stroke-miterlimit="10"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                    <path opacity="0.4" d="M2.33398 6.05957H13.6673" stroke="#C45F44"
                                        stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                                    <path
                                        d="M14.6673 12.6667C14.6673 13.1667 14.5273 13.64 14.2807 14.04C13.8207 14.8133 12.974 15.3333 12.0007 15.3333C11.3273 15.3333 10.714 15.0867 10.2473 14.6667C10.0407 14.4933 9.86065 14.28 9.72065 14.04C9.47399 13.64 9.33398 13.1667 9.33398 12.6667C9.33398 11.1933 10.5273 10 12.0007 10C12.8007 10 13.514 10.3533 14.0007 10.9067C14.414 11.38 14.6673 11.9933 14.6673 12.6667Z"
                                        stroke="#C45F44" stroke-miterlimit="10" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                    <path d="M10.9609 12.667L11.6209 13.327L13.0409 12.0137" stroke="#C45F44"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                    <path
                                        d="M14 5.66634V10.9063C13.5133 10.353 12.8 9.99967 12 9.99967C10.5267 9.99967 9.33333 11.193 9.33333 12.6663C9.33333 13.1663 9.47333 13.6397 9.72 14.0397C9.86 14.2797 10.04 14.493 10.2467 14.6663H5.33333C3 14.6663 2 13.333 2 11.333V5.66634C2 3.66634 3 2.33301 5.33333 2.33301H10.6667C13 2.33301 14 3.66634 14 5.66634Z"
                                        stroke="#C45F44" stroke-miterlimit="10" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                    <path opacity="0.4" d="M7.99764 9.13314H8.00363" stroke="#C45F44"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                    <path opacity="0.4" d="M5.52889 9.13314H5.53488" stroke="#C45F44"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                    <path opacity="0.4" d="M5.5293 11.1338H5.53528" stroke="#C45F44"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <span class="date pl-2">06 Mar 2025 - 06 Apr 2025</span>
                            </div>
                            <span class="ml-4 flex items-center date">
                                <span class="px-2 text-[#00AE4D] text-[10px] border border-[#00AE4D] rounded-full">2
                                    Days to Go</span>
                            </span>
                        </div>

                        <div class="grid grid-cols-3 mt-2 gap-0 card-button">
                            <button class="bg-[#D87A61] text-[#000000] py-2 rounded enrolltwo">View Booking</button>
                            <button class="bg-[#ECCEC4] text-[#000000] py-2 rounded reschedule">Reschedule </button>
                            <button class="bg-[#D87A61] text-[#000000] py-2 rounded cancle">Cancle</button>
                        </div>
                    </div>
                </div>
                <div id="completed" class="tab-content-block hidden">
                    <div class=" cards border rounded-lg  mb-2 pb-4">
                        <div class="flex items-center bg-[#D9D9D9] booknumber justify-between">
                            <p class="text-[#000000]">Booking No: 407-9914751-562757</p><a class="text-[#014F9A]"
                                href="/">Invoice</a>
                        </div>
                        <div class="flex ">
                            <img src="https://media-cdn.tripadvisor.com/media/photo-s/0e/77/5a/1a/firecamp.jpg"
                                alt="Course Image" class="w-20 h-20 rounded-lg object-cover mr-4" />
                            <div>
                                <p class="text-sm text-[#D87A61] font-semibold art">@The Art of Living</p>
                                <h3 class="font-bold card-title text-lg text-[#293148]">200-Hour Yoga Teacher Training
                                    in India
                                </h3>
                                <p class="text-sm text-[#D87A61] font-semibold location">Location:Chennai</p>
                            </div>
                        </div>
                        <div class="flex items-center text-sm text-gray-600 mt-1 date-content justify-between py-2">
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"
                                    fill="none">
                                    <path d="M5.33398 1.33301V3.33301" stroke="#C45F44" stroke-miterlimit="10"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                    <path d="M10.666 1.33301V3.33301" stroke="#C45F44" stroke-miterlimit="10"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                    <path opacity="0.4" d="M2.33398 6.05957H13.6673" stroke="#C45F44"
                                        stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                                    <path
                                        d="M14.6673 12.6667C14.6673 13.1667 14.5273 13.64 14.2807 14.04C13.8207 14.8133 12.974 15.3333 12.0007 15.3333C11.3273 15.3333 10.714 15.0867 10.2473 14.6667C10.0407 14.4933 9.86065 14.28 9.72065 14.04C9.47399 13.64 9.33398 13.1667 9.33398 12.6667C9.33398 11.1933 10.5273 10 12.0007 10C12.8007 10 13.514 10.3533 14.0007 10.9067C14.414 11.38 14.6673 11.9933 14.6673 12.6667Z"
                                        stroke="#C45F44" stroke-miterlimit="10" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                    <path d="M10.9609 12.667L11.6209 13.327L13.0409 12.0137" stroke="#C45F44"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                    <path
                                        d="M14 5.66634V10.9063C13.5133 10.353 12.8 9.99967 12 9.99967C10.5267 9.99967 9.33333 11.193 9.33333 12.6663C9.33333 13.1663 9.47333 13.6397 9.72 14.0397C9.86 14.2797 10.04 14.493 10.2467 14.6663H5.33333C3 14.6663 2 13.333 2 11.333V5.66634C2 3.66634 3 2.33301 5.33333 2.33301H10.6667C13 2.33301 14 3.66634 14 5.66634Z"
                                        stroke="#C45F44" stroke-miterlimit="10" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                    <path opacity="0.4" d="M7.99764 9.13314H8.00363" stroke="#C45F44"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                    <path opacity="0.4" d="M5.52889 9.13314H5.53488" stroke="#C45F44"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                    <path opacity="0.4" d="M5.5293 11.1338H5.53528" stroke="#C45F44"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <span class="date pl-2">06 Mar 2025 - 06 Apr 2025</span>
                            </div>

                        </div>

                        <div class="grid grid-cols-2 mt-2 gap-0 card-button">
                            <button class="bg-[#D87A61] text-[#000000] py-2 rounded enrolltwo">View Booking</button>
                            <button class="bg-[#ECCEC4] text-[#000000] py-2 rounded remove">Reschedule </button>
                        </div>
                    </div>
                </div>
                <div id="cancelled" class="tab-content-block hidden">
                    <div class=" cards border rounded-lg  mb-2 pb-4">
                        <div class="flex items-center bg-[#D9D9D9] booknumber justify-between">
                            <p class="text-[#000000]">Booking No: 407-9914751-562757</p><a class="text-[#014F9A]"
                                href="/">Invoice</a>
                        </div>
                        <div class="flex ">
                            <img src="https://media-cdn.tripadvisor.com/media/photo-s/0e/77/5a/1a/firecamp.jpg"
                                alt="Course Image" class="w-20 h-20 rounded-lg object-cover mr-4" />
                            <div>
                                <p class="text-sm text-[#D87A61] font-semibold art">@The Art of Living</p>
                                <h3 class="font-bold card-title text-lg text-[#293148]">200-Hour Yoga Teacher Training
                                    in India
                                </h3>
                                <p class="text-sm text-[#D87A61] font-semibold location">Location:Chennai</p>
                            </div>
                        </div>
                        <div class="flex items-center text-sm text-gray-600 mt-1 date-content justify-between py-2">
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"
                                    fill="none">
                                    <path d="M5.33398 1.33301V3.33301" stroke="#C45F44" stroke-miterlimit="10"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                    <path d="M10.666 1.33301V3.33301" stroke="#C45F44" stroke-miterlimit="10"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                    <path opacity="0.4" d="M2.33398 6.05957H13.6673" stroke="#C45F44"
                                        stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                                    <path
                                        d="M14.6673 12.6667C14.6673 13.1667 14.5273 13.64 14.2807 14.04C13.8207 14.8133 12.974 15.3333 12.0007 15.3333C11.3273 15.3333 10.714 15.0867 10.2473 14.6667C10.0407 14.4933 9.86065 14.28 9.72065 14.04C9.47399 13.64 9.33398 13.1667 9.33398 12.6667C9.33398 11.1933 10.5273 10 12.0007 10C12.8007 10 13.514 10.3533 14.0007 10.9067C14.414 11.38 14.6673 11.9933 14.6673 12.6667Z"
                                        stroke="#C45F44" stroke-miterlimit="10" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                    <path d="M10.9609 12.667L11.6209 13.327L13.0409 12.0137" stroke="#C45F44"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                    <path
                                        d="M14 5.66634V10.9063C13.5133 10.353 12.8 9.99967 12 9.99967C10.5267 9.99967 9.33333 11.193 9.33333 12.6663C9.33333 13.1663 9.47333 13.6397 9.72 14.0397C9.86 14.2797 10.04 14.493 10.2467 14.6663H5.33333C3 14.6663 2 13.333 2 11.333V5.66634C2 3.66634 3 2.33301 5.33333 2.33301H10.6667C13 2.33301 14 3.66634 14 5.66634Z"
                                        stroke="#C45F44" stroke-miterlimit="10" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                    <path opacity="0.4" d="M7.99764 9.13314H8.00363" stroke="#C45F44"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                    <path opacity="0.4" d="M5.52889 9.13314H5.53488" stroke="#C45F44"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                    <path opacity="0.4" d="M5.5293 11.1338H5.53528" stroke="#C45F44"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <span class="date pl-2">06 Mar 2025 - 06 Apr 2025</span>
                            </div>

                        </div>

                        <div class="grid grid-cols-1 mt-2 gap-0 card-button">
                            <button class="bg-[#D87A61] text-[#000000] py-2 rounded view-booking">View Booking</button>
                        </div>
                    </div>
                </div>
            </div>



        </div>

    </section>
</body>


<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');


    .group-booking .page-title {
        color: #293148;
        font-size: 24px;
        font-weight: 700;
        text-align: center;
        font-family: "Open Sans", sans-serif;
    }

    .group-booking .cards .art {
        font-family: "Poppins", sans-serif;
        font-size: 10px;
        font-weight: 600;
    }

    .group-booking .cards .location {
        font-family: "Open Sans", sans-serif;
        font-size: 10px;
        font-weight: 600;
        text-transform: uppercase;
        font-style: italic;
        color: #000000;
    }

    .group-booking .cards .card-title {
        font-weight: 700;
        color: #293148;
        font-size: 14px;
        text-decoration: underline;
        margin-bottom: 10px;
    }

    .group-booking .cards {
        border-top: 1px solid #D87A61 !important;
        border: 0px;
    }

    .group-booking .cards .date-content .date {
        font-size: 11px;
        font-style: italic;
        font-family: "Open Sans", sans-serif;
        font-weight: 600;
        color: #000000;
    }

    .group-booking .cards .upcoming {
        color: #BF0A30;
        font-size: 10px;
        font-style: italic;
        font-weight: 700;
        font-family: "Open Sans", sans-serif;
    }

    .group-booking .cards .card-button .enroll {
        border-bottom-left-radius: 10px !important;
        border-radius: 0px;
        font-size: 12px;
        font-weight: 600;
        font-family: "Open Sans", sans-serif;
    }

    .group-booking .cards .card-button .enrolltwo {
        border-bottom-left-radius: 10px !important;
        border-radius: 0px;
        font-size: 10px;
        font-weight: 600;
        font-family: "Open Sans", sans-serif;
    }

    .group-booking .cards .card-button .view-booking {
        border-bottom-left-radius: 10px !important;
        border-bottom-right-radius: 10px !important;
        border-radius: 0px;
        font-size: 10px;
        font-weight: 600;
        font-family: "Open Sans", sans-serif;
    }

    .group-booking .cards .card-button .cancle {
        border-bottom-right-radius: 10px !important;
        border-radius: 0px;
        font-size: 12px;
        font-weight: 600;
        font-family: "Open Sans", sans-serif;
    }

    .group-booking .cards .card-button .remove {
        border-bottom-right-radius: 10px !important;
        border-radius: 0px;
        font-size: 10px;
        font-weight: 600;
        font-family: "Open Sans", sans-serif;
    }

    .group-booking .cards .card-button .reschedule {
        border-radius: 0px;
        font-size: 10px;
        font-weight: 600;
        font-family: "Open Sans", sans-serif;
    }

    .group-booking #tab-content .cards {
        border: 0px !important;
    }

    .group-booking #tabs .tab-btn {
        font-size: 12px;
        font-weight: 700;
        font-family: "Open Sans", sans-serif;
    }

    #tab-content .booknumber {
        border: 0px;
        border-top-right-radius: 10px;
        border-top-left-radius: 10px;
        padding: 5px 10px;
        margin-bottom: 10px;
    }

    #tab-content .booknumber p {
        font-size: 10px;
        font-weight: 600;
        color: #000000;
    }

    #tab-content .booknumber a {
        color: #014F9A;
        font-size: 10px;
        font-weight: 600;
        text-decoration: underline;
    }

    @media only screen and (max-width: 700px) {}

    @media only screen and (max-width: 390px) {}
</style>
<script>
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content-block');

    tabButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            // Remove active class from all buttons
            tabButtons.forEach(b => {
                b.classList.remove('bg-[#D87A61]', 'text-white');
                b.classList.add('text-black');
            });

            // Hide all contents
            tabContents.forEach(tc => tc.classList.add('hidden'));

            // Activate clicked tab
            const tabId = btn.getAttribute('data-tab');
            document.getElementById(tabId).classList.remove('hidden');
            btn.classList.add('bg-[#D87A61]', 'text-white');
        });
    });
</script>


</html>