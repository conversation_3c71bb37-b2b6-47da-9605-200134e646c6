<?php
declare(strict_types=1);

namespace App\Controller;

/**
 * Profile Controller
 *
 * Customer profile management for frontend
 */
class ProfileController extends AppController
{
    public function initialize(): void
    {
        parent::initialize();
        $this->loadComponent('Flash');
        $this->viewBuilder()->setLayout('default');
        $this->Users = $this->fetchTable('Users');
        $this->Customers = $this->fetchTable('Customers');
        $this->Countries = $this->fetchTable('Countries');
         $this->Bookmarks = $this->fetchTable('Bookmarks');
        // $this->loadModel('Users');
        // $this->loadModel('Customers');
        // $this->loadModel('Countries');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
        // Require authentication for all profile actions
        $this->Authentication->addUnauthenticatedActions([]);
    }

    /**
     * Customer Profile Dashboard
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function index()
    {
        
        // Get authenticated user
        $identity = $this->Authentication->getIdentity();
        if (!$identity) {
            $this->Flash->error(__('Please login to access your profile.'));
            return $this->redirect(['controller' => 'Login', 'action' => 'index']);
        }

        $userId = $identity->get('id');

        // Get user data with customer profile
        $user = $this->Users->get($userId, [
            'contain' => []
        ]);

        // Get or create customer profile
        $customer = $this->Customers->find()
            ->where(['user_id' => $userId])
            ->first();

        if (!$customer) {
            // Create new customer profile if doesn't exist
            $customer = $this->Customers->newEmptyEntity();
            $customer->user_id = $userId;
        }

        // Get countries for dropdown
        $countries = $this->Countries->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->toArray();

        $title = 'My Profile';
        $this->set(compact('title', 'user', 'customer', 'countries'));
    }

    /**
     * Update Profile
     *
     * @return \Cake\Http\Response|null|void Redirects on successful update, renders view otherwise.
     */
    public function updateProfile()
    {
        
        $this->request->allowMethod(['post', 'put']);

        // Get authenticated user
        $identity = $this->Authentication->getIdentity();
        if (!$identity) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode([
                    'success' => false,
                    'message' => 'Please login to update your profile.'
                ]));
        }

        $userId = $identity->get('id');

        try {
            // Get user data
            $user = $this->Users->get($userId);
            $data = $this->request->getData();

            // Update user basic information
            $userFields = [
                'first_name' => $data['first_name'] ?? '',
                'last_name' => $data['last_name'] ?? '',
                'email' => $data['email'] ?? '',
                'mobile' => $data['mobile'] ?? '',
                'country_code' => $data['country_code'] ?? '+91'
            ];

            // Generate full name
            $userFields['full_name'] = trim($userFields['first_name'] . ' ' . $userFields['last_name']);

            $user = $this->Users->patchEntity($user, $userFields, [
                'validate' => 'profileUpdate'
            ]);

            // Get or create customer profile
            $customer = $this->Customers->find()
                ->where(['user_id' => $userId])
                ->first();

            if (!$customer) {
                $customer = $this->Customers->newEmptyEntity();
                $customer->user_id = $userId;
            }

            // Update customer profile fields
            $customerFields = [
                'gender' => $data['gender'] ?? null,
                'dob' => !empty($data['dob']) ? $data['dob'] : null,
                'address' => $data['address'] ?? null,
            ];

            $customer = $this->Customers->patchEntity($customer, $customerFields);

            // Save both entities in a transaction
            $connection = $this->Users->getConnection();
            $connection->transactional(function () use ($user, $customer) {
                if (!$this->Users->save($user)) {
                    throw new \Exception('Failed to update user information');
                }
                if (!$this->Customers->save($customer)) {
                    throw new \Exception('Failed to update customer profile');
                }
            });

            $this->Flash->success(__('Profile updated successfully!'));
            return $this->redirect(['action' => 'index']);

        } catch (\Exception $e) {
            $this->Flash->error(__('Failed to update profile: {0}', $e->getMessage()));
            return $this->redirect(['action' => 'index']);
        }
    }

    /**
     * My Orders
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function orders()
    {
        $title = 'My Orders';
        $this->set(compact('title'));
        $this->set('title', $title);
    }

    /**
     * My Wishlist
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function wishlist()
    {
        $title = 'My Bookmarks';
        $this->set(compact('title'));
        $this->set('title', $title);
    }

    /**
     * Change Password
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function changePassword()
    {
        $title = 'Change Password';
        $this->set(compact('title'));
        $this->set('title', $title);
    }
    public function toggle()
{
    $this->request->allowMethod(['post']);
    

    // Get authenticated user
    $identity = $this->Authentication->getIdentity();
    if (!$identity) {
        return $this->response->withType('application/json')
            ->withStringBody(json_encode([
                'success' => false,
                'message' => 'Please login to bookmark this item.',
                'redirect' => '/login/customer-login'
            ]));
    }

    $userId = $identity->get('id');
    $data = $this->request->getData();

    if (empty($data['type']) || empty($data['ref_id'])) {
        return $this->response->withType('application/json')
            ->withStringBody(json_encode([
                'success' => false,
                'message' => 'Missing bookmark data.'
            ]));
    }

    // Check for existing bookmark
    $existing = $this->Bookmarks->find()
        ->where([
            'customer_id' => $userId,
            'type' => $data['type'],
            'ref_id' => $data['ref_id']
        ])
        ->first();

    if ($existing) {
        $this->Bookmarks->delete($existing);
        return $this->response->withType('application/json')
            ->withStringBody(json_encode([
                'success' => true,
                'status' => 'removed'
            ]));
    } else {
        $bookmark = $this->Bookmarks->newEntity([
            'customer_id' => $userId,
            'type' => $data['type'],
            'ref_id' => $data['ref_id']
        ]);

        if ($this->Bookmarks->save($bookmark)) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode([
                    'success' => true,
                    'status' => 'added'
                ]));
        } else {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode([
                    'success' => false,
                    'message' => 'Unable to bookmark item.'
                ]));
        }
    }
}



    public function toggle_old()
    {
        $this->request->allowMethod(['post']);
       
           $identity = $this->Authentication->getIdentity();

        if (!$identity) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode([
                    'status' => 'unauthenticated',
                    'redirect' => '/login/customer-login',
                ]));
        }
        $userId = $identity->get('id');

        $data = $this->request->getData();
        $existing = $this->Bookmarks->find()
            ->where([
                'customer_id' => $user['id'],
                'type' => $data['type'],
                'ref_id' => $data['ref_id']
            ])
            ->first();

        if ($existing) {
            $this->Bookmarks->delete($existing);
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['status' => 'removed']));
        } else {
            $bookmark = $this->Bookmarks->newEntity([
                'customer_id' => $user['id'],
                'type' => $data['type'],
                'ref_id' => $data['ref_id'],
            ]);
            $this->Bookmarks->save($bookmark);
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['status' => 'added']));
        }
    }

    public function check()
    {
        $this->request->allowMethod(['get']);
        $user = $this->request->getSession()->read('Auth.User');

        if (!$user) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['bookmarked' => false]));
        }

        $type = $this->request->getQuery('type');
        $ref_id = $this->request->getQuery('id');

        $bookmark = $this->Bookmarks->find()
            ->where([
                'customer_id' => $user['id'],
                'type' => $type,
                'ref_id' => $ref_id
            ])
            ->first();

        return $this->response->withType('application/json')
            ->withStringBody(json_encode(['bookmarked' => !empty($bookmark)]));
    }
}
