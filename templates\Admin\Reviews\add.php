<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Review $review
 * @var array $statusOptions
 * @var array $reviewTypeOptions
 * @var array $ratingOptions
 */
?>

<div class="main-content">
    <section class="section">
        <div class="section-body1">
            <div class="container-fluid">
                <?php
                $successMessage = $this->Flash->render('success');
                $errorMessage = $this->Flash->render('error');
                ?>
                <?php if (!empty($successMessage)): ?>
                    <?= $successMessage ?>
                <?php endif; ?>
                <?php if (!empty($errorMessage)): ?>
                    <?= $errorMessage ?>
                <?php endif; ?>

                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4>Add New Review</h4>
                                <div class="card-header-action">
                                    <?= $this->Html->link('Back to List', ['action' => 'index'], ['class' => 'btn btn-secondary']) ?>
                                </div>
                            </div>
                            <div class="card-body">
                                <?= $this->Form->create($review) ?>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Customer ID</label>
                                            <?= $this->Form->control('customer_id', [
                                                'label' => false,
                                                'type' => 'number',
                                                'class' => 'form-control',
                                                'required' => true,
                                                'min' => 1
                                            ]) ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Review Type</label>
                                            <?= $this->Form->control('review_type', [
                                                'label' => false,
                                                'options' => $reviewTypeOptions,
                                                'empty' => 'Select Review Type',
                                                'class' => 'form-control',
                                                'required' => true
                                            ]) ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Reference ID</label>
                                            <?= $this->Form->control('reference_id', [
                                                'label' => false,
                                                'type' => 'number',
                                                'class' => 'form-control',
                                                'required' => true,
                                                'min' => 1
                                            ]) ?>
                                            <small class="form-text text-muted">ID of the course, teacher, or center being reviewed</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Rating</label>
                                            <?= $this->Form->control('rating', [
                                                'label' => false,
                                                'options' => $ratingOptions,
                                                'empty' => 'Select Rating',
                                                'class' => 'form-control',
                                                'required' => true
                                            ]) ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Booking ID (Optional)</label>
                                            <?= $this->Form->control('booking_id', [
                                                'label' => false,
                                                'type' => 'number',
                                                'class' => 'form-control',
                                                'min' => 1
                                            ]) ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Booking Item ID (Optional)</label>
                                            <?= $this->Form->control('booking_item_id', [
                                                'label' => false,
                                                'type' => 'number',
                                                'class' => 'form-control',
                                                'min' => 1
                                            ]) ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label>Comment</label>
                                            <?= $this->Form->control('comment', [
                                                'label' => false,
                                                'type' => 'textarea',
                                                'class' => 'form-control',
                                                'rows' => 4,
                                                'maxlength' => 1000,
                                                'placeholder' => 'Enter review comment (optional)'
                                            ]) ?>
                                            <small class="form-text text-muted">Maximum 1000 characters</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Status</label>
                                            <?= $this->Form->control('status', [
                                                'label' => false,
                                                'options' => $statusOptions,
                                                'class' => 'form-control',
                                                'default' => 'Pending'
                                            ]) ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mt-4">
                                    <?= $this->Form->button('Save Review', [
                                        'class' => 'btn btn-primary',
                                        'type' => 'submit'
                                    ]) ?>
                                    <?= $this->Html->link('Cancel', ['action' => 'index'], [
                                        'class' => 'btn btn-secondary ms-2'
                                    ]) ?>
                                </div>
                                <?= $this->Form->end() ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>


