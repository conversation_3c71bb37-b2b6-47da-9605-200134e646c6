<?php

use Cake\Utility\Security;

$rememberData = $rememberData ?? [];
$password = '';
if (!empty($rememberData['password'])) {
    try {
        $password = Security::decrypt(base64_decode($rememberData['password']), Security::getSalt());
    } catch (Exception $e) {
        $password = '';
    }
}
?>

<head>
    <link rel="stylesheet" href="<?= $this->Url->webroot('css/login.css'); ?>">
</head>
<section class="login-container px-6 md:px-10 lg:px-25 xl:px-40">
    <div class="mx-auto">
        <div class="block md:flex md:items-normal justify-center">
            <!-- Card 1 -->
            <!-- <div class="bg-white rounded-lg left-img-content desktop-view">
                <img src="<?= $this->Url->webroot('img/yoga-login.png') ?>" alt="Login Yoga image" />

            </div> -->

            <!-- Card 2 -->
            <div class="bg-white rounded-lg login-wrapper rounded shadow">
                <a href="/"><img src="<?= $this->Url->webroot('img/yoga-big.png') ?>" alt="Yoga logo" class="yoga-logo"></a>
                <div class="img-container">
                    <img src="/img/login-img.png" alt="Login image" class="login-img">
                </div>
                <div class="hr-container">
                    <div class="hr"></div>
                    <h2 class="text-center heading">Login</h2>
                </div>
                <div class="social-media px-0 py-0">
                    <button id="google-signin-btn" class="google"><img src="<?= $this->Url->webroot('img/google-icon.png') ?>" alt="Google Icon"></button>
                    <button class="fb"><img src="<?= $this->Url->webroot('img/facebook-icon.png') ?>" alt="Facebook Icon"></button>
                </div>
                <!-- Flash messages -->
                <div class="px-4"><?= $this->Flash->render() ?></div>

                <div x-data="{ tab: 'first' }" class="max-w-2xl mx-auto bg-white px-4 py-2">

                    <!-- Tabs -->
                    <div class="flex mb-2 bg-[#FFEFE9] rounded-[8px] px-[6px] py-[4px]">
                        <button @click="tab = 'first'" :class="tab === 'first' ? 'border-b-2 border-[#d87a61] active' : 'text-[#293148]'" class="px-2 py-1 focus:outline-none btn-noactive">With Password</button>
                        <button @click="tab = 'second'" :class="tab === 'second' ? 'border-b-2 border-[#d87a61] active' : 'text-[#293148]'" class="px-2 py-1 focus:outline-none btn-noactive">With OTP</button>
                    </div>

                    <!-- Tab Panels -->
                    <div x-show="tab === 'first'" class="h-77 first-tab">
                        <form class="login-form" id="login-form" method="post" action="<?= $this->Url->build('/customer-login') ?>">
                            <input type="hidden" name="_csrfToken" value="<?= $this->request->getAttribute('csrfToken') ?>">
                            <input type="hidden" name="redirect" value="<?= h($this->request->getQuery('redirect')) ?>">
                            <div class="form-container">
                                <div class="radio-option-container">
                                    <div class="radio-email">
                                        <input type="radio" id="email" name="login" value="email" checked  onclick="checkRadio(id)"><span>Email</span>
                                    </div>
                                    <div class="radio-phone">
                                        <input type="radio" id="phone" name="login" value="phone" onclick="checkRadio(id)"><span>Mobile No</span>
                                    </div>
                                </div>
                                <div class="relative form-field">
                                    <label class="email-id">Email ID</label>
                                    <input type="text" name="email_or_mobile" class="form-control email-id" required />
                                    <span class="mobile">
                                        <label class="mobile-label">Mobile No</label>
                                        <select name="country_code" class="code">
                                            <option value="1">+1</option>
                                            <option value="7">+7</option>
                                            <option value="43">+43</option>
                                            <option value="44">+44</option>
                                            <option value="60">+60</option>
                                            <option value="65">+65</option>
                                            <option value="91">+91</option>
                                            <option value="92">+92</option>
                                            <option value="1 648">******</option>
                                        </select>
                                        <input type="text" name="email_or_mobile" maxlength="10" class="form-control" required onkeypress="validNumber(event);" />
                                    </span>
                                </div>
                                <div class="relative form-field">
                                    <label>Password</label>
                                    <!-- <input type="password" name="password" class="form-control password" required /> -->
                                    <input type="password" name="password" value="<?= h($password) ?>" class="form-control password" required />

                                    <div class="eye-icon">
                                        <i class="fas fa-eye-slash"></i>
                                    </div>
                                </div>
                                <div class="remember flex items-center">
                                    <label>
                                        <input type="checkbox" name="remember_me" /> <span>Remember Me</span>
                                    </label>
                                        <a href="<?= $this->Url->build(['controller' => 'Login', 'action' => 'forgotPassword']) ?>" class="text-small">Forgot Password?</a>
                                </div>
                                <button type="submit" class="btn-login">Login</button>
                                <p class="small-text terms"><span>By continuing, you agree to <a href="<?= h($configSettings['WP_TERMS_CONDITIONS']) ?>">Terms of Use</a> and <a href="<?= h($configSettings['WP_PRIVACY_POLICY']) ?>">Privacy Policy</a> of yoga.in</span></p>
                            </div>
                        </form>
                    </div>
                    <div x-show="tab === 'second'" class="h-77 second-tab">
                        <form class="login-form" id="otp-form">
                            <input type="hidden" name="_csrfToken" value="<?= $this->request->getAttribute('csrfToken') ?>">
                            <input type="hidden" name="redirect" value="<?= h($this->request->getQuery('redirect')) ?>">
                            <div class="form-container">
                                <div class="radio-option-container">
                                    <div class="radio-email">
                                        <input type="radio" id="otp-email" name="login" value="email" checked  onclick="checkOtpRadio(id)"><span>Email</span>
                                    </div>
                                    <div class="radio-phone">
                                        <input type="radio" id="otp-phone" name="login" value="phone" onclick="checkOtpRadio(id)"><span>Mobile No</span>
                                    </div>
                                </div>
                                <div class="relative form-field">
                                    <label class="otp-email">Email ID</label>
                                    <!-- <div class="block"> -->
                                    <input type="text" name="otp_email_or_mobile" id="otp_email_or_mobile" class="form-control otp-email mb-2" required />
                                    <span class="otp-mobile">
                                        <label class="mobile-label">Mobile No</label>
                                        <select name="country_code" class="code">
                                            <option value="1">+1</option>
                                            <option value="7">+7</option>
                                            <option value="43">+43</option>
                                            <option value="44">+44</option>
                                            <option value="60">+60</option>
                                            <option value="65">+65</option>
                                            <option value="91">+91</option>
                                            <option value="92">+92</option>
                                            <option value="1 648">******</option>
                                        </select>
                                        <input type="text" name="email_or_mobile" maxlength="10" class="form-control" required onkeypress="validNumber(event);" />
                                    </span>
                                    <button type="button" class="btn-send-otp" id="send-otp-btn" onclick="sendOTP();">Send OTP</button>
                                    <!-- </div> -->
                                </div>
                                <div class="relative form-field otp-wrapper">
                                    <div class="otp-form">
                                        <p class="otp-sent-msg text-success"></p>
                                        
                                        <div class="otp-msg">
                                            <label>Enter Verification Code</label>
                                            <div class="flex">
                                                <input type="text" class="form-control form-otp otp-input" maxlength="1" data-index="1" />
                                                <input type="text" class="form-control form-otp otp-input" maxlength="1" data-index="2" />
                                                <input type="text" class="form-control form-otp otp-input" maxlength="1" data-index="3" />
                                                <input type="text" class="form-control form-otp otp-input" maxlength="1" data-index="4" />
                                            </div>
                                            <input type="hidden" name="otp" id="complete-otp" />
                                            <p class="text-[12px] text-[#BE1F2E] text-bold" id="otp-timer"></p>
                                            <span class="resend-span">Didn't receive a code? <button type="button" class="btn-link" id="resend-otp-btn" disabled>Resend OTP</button></span>
                                            <span class="verify-msg">Verification code sent to <span class="verify-mail"><EMAIL></span></span>
                                        </div>
                                        
                                    </div>
                                </div>
                                <button type="button" class="btn-otp" id="verify-otp-btn">Login</button>
                                <p class="small-text terms"><span>By continuing, you agree to <a href="<?= h($configSettings['WP_TERMS_CONDITIONS']) ?>">Terms of Use</a> and <a href="<?= h($configSettings['WP_PRIVACY_POLICY']) ?>">Privacy Policy</a> of yoga.in</span></p>
                            </div>
                        </form>
                    </div>
                </div>
                <hr />
                <div class="text-center">
                    <span class="new-account text-center inline-block">New User? 
                        <a href="<?= $this->Url->build('/signup') ?>?redirect=<?= $qryRedirect ?>">Create an Account</a>
                    </span>
                </div>
                <!-- <div class="divider-container">
                    <span class="divider">Or</span>
                </div> -->
            </div>
        </div>
    </div>
</section>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Common elements
        const loginForm = document.getElementById('login-form');
        const otpForm = document.getElementById('otp-form');
        const emailOrMobileInput = document.querySelector('input[name="email_or_mobile"]');
        const passwordInput = document.querySelector('input[name="password"]');
        const otpEmailOrMobileInput = document.getElementById('otp_email_or_mobile');
        const sendOtpBtn = document.getElementById('send-otp-btn');
        const verifyOtpBtn = document.getElementById('verify-otp-btn');
        const resendOtpBtn = document.getElementById('resend-otp-btn');
        const otpInputs = document.querySelectorAll('.otp-input');
        const completeOtpInput = document.getElementById('complete-otp');
        const otpTimer = document.getElementById('otp-timer');
        const queryRedirect = '<?php echo  $qryRedirect ?>'

        // Toggle password visibility
        const eyeIcons = document.querySelectorAll('.eye-icon');
        eyeIcons.forEach(icon => {
            icon.addEventListener('click', function() {
                const passwordField = this.previousElementSibling;
                const eyeIcon = this.querySelector('i');

                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    eyeIcon.classList.remove('fa-eye-slash');
                    eyeIcon.classList.add('fa-eye');
                } else {
                    passwordField.type = 'password';
                    eyeIcon.classList.remove('fa-eye');
                    eyeIcon.classList.add('fa-eye-slash');
                }
            });
        });

        // Email/Mobile validation for first tab
        if (emailOrMobileInput) {
            let debounceTimer;

            emailOrMobileInput.addEventListener('input', function() {
                clearTimeout(debounceTimer);

                // Remove validation messages while typing
                this.classList.remove('is-invalid', 'is-valid');
                const feedbackElement = this.parentElement.querySelector('.validation-feedback');
                if (feedbackElement) feedbackElement.remove();

                // Debounce the validation
                debounceTimer = setTimeout(() => {
                    const value = this.value.trim();

                    // Skip validation if empty
                    if (!value) return;

                    // Check if it looks like an email attempt
                    const looksLikeEmail = /[a-zA-Z@]/.test(value);

                    if (looksLikeEmail) {
                        // Validate email format
                        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                        const isValidEmail = emailRegex.test(value);
                     
                        // Show validation feedback
                        this.classList.add(isValidEmail ? 'is-valid' : 'is-invalid');
                       
                        clearInlineMessage(this.parentElement);
                        if (!isValidEmail) {
                            const parent = this.parentElement;
                            // Remove existing validation feedback if present
                            const existingFeedback = parent.querySelector('.validation-feedback');
                            if (existingFeedback) {
                                existingFeedback.remove();
                            }
                           
                            // Create and append new feedback
                            const feedback = document.createElement('div');
                            feedback.className = 'validation-feedback text-red-500 text-xs mt-1';
                            feedback.textContent = 'Please enter a valid email address';
                            parent.appendChild(feedback);
                        }
                    } else {
                        // Validate mobile number (exactly 10 digits)
                        const isValidMobile = /^\d{10}$/.test(value);

                        // Show validation feedback
                        this.classList.add(isValidMobile ? 'is-valid' : 'is-invalid');

                        if (!isValidMobile) {
                            clearInlineMessage(this.parentElement);
                            const parent = this.parentElement;
                            // Remove existing validation feedback if present
                            const existingFeedback = parent.querySelector('.validation-feedback');
                            if (existingFeedback) {
                                existingFeedback.remove();
                            }

                            // Create and append new feedback
                            const feedback = document.createElement('div');
                            feedback.className = 'validation-feedback text-red-500 text-xs mt-1';
                            feedback.textContent = 'Please enter a valid 10-digit mobile number';
                            parent.appendChild(feedback);
                        }
                    }
                }, 500);
            });
        }

        // Email/Mobile validation for OTP tab
        if (otpEmailOrMobileInput) {
            let debounceTimer;
            
            const showValidationFeedback = (input, message) => {
                const parent = input.closest('.form-field');
                removeValidationFeedback(input);
                const feedback = document.createElement('div');
                feedback.className = 'validation-feedback text-red-500 text-xs mt-1';
                feedback.textContent = message;
                parent.appendChild(feedback);
            };

            const removeValidationFeedback = (input) => {
                const parent = input.closest('.form-field');
                const existingFeedback = parent.querySelector('.validation-feedback');
                if (existingFeedback) existingFeedback.remove();
            };


            otpEmailOrMobileInput.addEventListener('input', function() {
                clearTimeout(debounceTimer);
                removeValidationFeedback(this);
                // Remove validation messages while typing
                this.classList.remove('is-invalid', 'is-valid');
                const feedbackElement = this.parentElement.querySelector('.validation-feedback');
                if (feedbackElement) feedbackElement.remove();

                // Debounce the validation
                debounceTimer = setTimeout(() => {
                    const value = this.value.trim();

                    // Skip validation if empty
                    if (!value) return;

                    // Check if it looks like an email attempt
                    const looksLikeEmail = /[a-zA-Z@]/.test(value);

                    if (looksLikeEmail) {
                        // Validate email format
                        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                        const isValidEmail = emailRegex.test(value);
                      
                        // Show validation feedback
                        this.classList.add(isValidEmail ? 'is-valid' : 'is-invalid');
                      
                        if (!isValidEmail) {   
                            clearInlineMessage(this.parentElement.parentElement);
                            showValidationFeedback(this, 'Please enter a valid email address');
                        } else {
                            removeValidationFeedback(this);
                        }

                    } else {
                        // Validate mobile number (exactly 10 digits)
                        const isValidMobile = /^\d{10}$/.test(value);

                        // Show validation feedback
                        this.classList.add(isValidMobile ? 'is-valid' : 'is-invalid');

                        if (!isValidMobile) {
                            clearInlineMessage(this.parentElement.parentElement);
                            showValidationFeedback(this, 'Please enter a valid 10-digit mobile number');
                        } else {
                            removeValidationFeedback(this);
                        }
                    }
                }, 500);
            });
        }

        // Initialize OTP inputs
        if (otpInputs.length) {
            otpInputs.forEach(input => {
                input.addEventListener('input', function(e) {
                    // Allow only one digit
                    this.value = this.value.replace(/\D/g, '').substring(0, 1);

                    const index = parseInt(this.dataset.index);

                    // Collect all OTP digits
                    let otp = '';
                    otpInputs.forEach(input => {
                        otp += input.value;
                    });
                    completeOtpInput.value = otp;

                    // Auto-focus next input
                    if (this.value && index < 4) {
                        otpInputs[index].focus();
                    }
                });

                // Handle backspace to go to previous input
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Backspace' && !this.value) {
                        const index = parseInt(this.dataset.index);
                        if (index > 1) {
                            otpInputs[index - 2].focus();
                        }
                    }
                });
            });
        }

        // Timer functions for OTP
        let countdown;
        let remainingTime = 60;

        function startTimer() {
            remainingTime = 60;
            updateTimerText();

            if (countdown) {
                clearInterval(countdown);
            }

            countdown = setInterval(function() {
                remainingTime--;
                updateTimerText();

                if (remainingTime <= 0) {
                    clearInterval(countdown);
                    if (resendOtpBtn) {
                        resendOtpBtn.disabled = false;
                        resendOtpBtn.classList.remove('disabled:cursor-not-allowed');
                    }
                }
            }, 1000);
        }

        function updateTimerText() {
            if (otpTimer) {
                otpTimer.textContent = `Resend OTP in ${remainingTime} seconds`;
            }
        }

        // Handle login form submission with inline validation
        if (loginForm) {
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Clear previous validation messages
                const existingFeedbacks = loginForm.querySelectorAll('.validation-feedback');
                existingFeedbacks.forEach(fb => fb.remove());
                emailOrMobileInput.classList.remove('is-invalid');
                passwordInput.classList.remove('is-invalid');

                const emailOrMobile = emailOrMobileInput.value.trim();
                const password = passwordInput.value.trim();

                // Validation flag
                let isValid = true;

                // Helper function to show validation feedback
                function showValidationError(inputElement, message) {
                    inputElement.classList.add('is-invalid');
                    const feedback = document.createElement('div');
                    feedback.className = 'validation-feedback text-red-500 text-xs mt-1';
                    feedback.textContent = message;
                    inputElement.parentElement.appendChild(feedback);
                }

                // Validate email or mobile
                if (!emailOrMobile) {
                    showValidationError(emailOrMobileInput, 'Please enter your email or mobile number');
                    isValid = false;
                } else {
                    const looksLikeEmail = /[a-zA-Z@]/.test(emailOrMobile);
                    if (looksLikeEmail) {
                        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                        if (!emailRegex.test(emailOrMobile)) {
                            showValidationError(emailOrMobileInput, 'Please enter a valid email address');
                            isValid = false;
                        }
                    } else {
                        if (!/^\d{10}$/.test(emailOrMobile)) {
                            showValidationError(emailOrMobileInput, 'Please enter a valid 10-digit mobile number');
                            isValid = false;
                        }
                    }
                }

                // Validate password
                if (!password) {
                    showValidationError(passwordInput, 'Please enter your password');
                    isValid = false;
                }

                if (!isValid) {
                    // Don't proceed with form submission
                    return;
                }

                // If valid, submit via fetch
                const formData = new FormData(this);
                const csrfToken = document.querySelector('input[name="_csrfToken"]').value;
                formData.append('reqdirect_url', queryRedirect);
                
                fetch('<?= $this->Url->build(['controller' => 'Login', 'action' => 'customerLogin']) ?>', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-Token': csrfToken,
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json'
                        },
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.href = data.redirect;
                            // Swal.fire({
                            //     icon: 'success',
                            //     title: 'Success!',
                            //     text: data.message,
                            //     confirmButtonText: 'OK'
                            // }).then(() => {
                            //     if (data.redirect) {
                            //         window.location.href = data.redirect;
                            //     }
                            // });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: data.message || 'Login failed. Please check your credentials.',
                                confirmButtonText: 'OK'
                            });
                        }
                    })
                    .catch(error => {
                        let formError = document.getElementById('form-error');
                        if (!formError) {
                            formError = document.createElement('div');
                            formError.id = 'form-error';
                            formError.className = 'validation-feedback text-red-600 text-sm mb-3';
                            loginForm.prepend(formError);
                        }
                        formError.textContent = 'An unexpected error occurred. Please try again.';
                    });
            });
        }

        // Handle Send OTP button click
        if (sendOtpBtn) {
            sendOtpBtn.addEventListener('click', function() {
                // Clear any previous inline messages
                clearInlineMessage(otpEmailOrMobileInput.parentElement);

                const emailOrMobile = otpEmailOrMobileInput.value.trim();

                if (!emailOrMobile) {
                    showInlineMessage(otpEmailOrMobileInput.parentElement.parentElement, 'Please enter your email or mobile number', 'error');
                    return;
                }

                // Validate email or mobile format
                const looksLikeEmail = /[a-zA-Z@]/.test(emailOrMobile);
                let isValid = true;

                if (looksLikeEmail) {
                    // Validate email format
                    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                    if (!emailRegex.test(emailOrMobile)) {
                        clearInlineMessage(otpEmailOrMobileInput.parentElement.parentElement);
                        showInlineMessage(otpEmailOrMobileInput.parentElement.parentElement, 'Please enter a valid email address', 'error');
                        isValid = false;
                    }
                } else {
                    // Validate mobile number (exactly 10 digits)
                    if (!/^\d{10}$/.test(emailOrMobile)) {
                        clearInlineMessage(otpEmailOrMobileInput.parentElement.parentElement);
                        showInlineMessage(otpEmailOrMobileInput.parentElement.parentElement, 'Please enter a valid 10-digit mobile number', 'error');
                        isValid = false;
                    }
                }

                if (!isValid) {
                    return;
                }

                // Show loading state
                sendOtpBtn.textContent = 'Sending...';
                sendOtpBtn.disabled = true;

                // Send OTP request
                const formData = new FormData();
                formData.append('email_or_mobile', emailOrMobile);
                const csrfToken = document.querySelector('input[name="_csrfToken"]').value;

                fetch('<?= $this->Url->build(['controller' => 'Login', 'action' => 'sendOtp']) ?>', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-Token': csrfToken,
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json'
                        },
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        // Reset button state
                        sendOtpBtn.textContent = 'Send OTP';
                        sendOtpBtn.disabled = false;

                        // Clear old messages
                        clearInlineMessage(otpEmailOrMobileInput.parentElement);

                        if (data.success) {
                            // Update message based on input type
                            const otpSentMsg = document.querySelector('.otp-sent-msg');
                            if (otpSentMsg) {
                                otpSentMsg.textContent = data.input_type === 'mobile' ?
                                    'The OTP is sent to your mobile number' :
                                    'The OTP is sent to your email address';
                            }

                            // Start countdown timer
                            startTimer();

                            // Disable resend button initially
                            if (resendOtpBtn) {
                                resendOtpBtn.disabled = true;
                                resendOtpBtn.classList.add('disabled:cursor-not-allowed');
                            }

                            // Focus first OTP input
                            if (otpInputs.length) {
                                otpInputs[0].focus();
                            }

                            // Show success message
                            Swal.fire({
                                icon: 'success',
                                title: 'OTP Sent',
                                text: data.message,
                                confirmButtonText: 'OK'
                            });
                        } else {
                            // Show error message
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: data.message || 'Failed to send OTP. Please try again.',
                                confirmButtonText: 'OK'
                            });
                        }
                    })
                    .catch(error => {
                        // Reset button state
                        sendOtpBtn.textContent = 'Send OTP';
                        sendOtpBtn.disabled = false;

                        // Show error message below input
                        clearInlineMessage(otpEmailOrMobileInput.parentElement);
                        showInlineMessage(otpEmailOrMobileInput.parentElement, 'An unexpected error occurred. Please try again.', 'error');
                    });
            });
        }

        // Helper functions for inline messages
        function showInlineMessage(container, message, type = 'error') {
            // Remove existing message
            const existingMsg = container.querySelector('.inline-message');
            if (existingMsg) existingMsg.remove();

            const msgDiv = document.createElement('div');
            msgDiv.className = 'inline-message mt-1 text-sm ' + (type === 'error' ? 'text-red-600' : 'text-green-600');
            msgDiv.textContent = message;
            container.appendChild(msgDiv);
        }

        function clearInlineMessage(container) {
            const existingMsg = container.querySelector('.inline-message');
            if (existingMsg) existingMsg.remove();
        }


        // Handle Verify OTP button click
        if (verifyOtpBtn) {
            verifyOtpBtn.addEventListener('click', function() {
                const otp = completeOtpInput.value;
                const emailOrMobile = otpEmailOrMobileInput.value.trim();

                if (!otp || otp.length !== 4) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Please enter the complete 4-digit OTP',
                        confirmButtonText: 'OK'
                    });
                    return;
                }

                // Show loading state
                verifyOtpBtn.textContent = 'Verifying...';
                verifyOtpBtn.disabled = true;

                // Verify OTP request
                const formData = new FormData();
                formData.append('email_or_mobile', emailOrMobile);
                formData.append('otp', otp);
                formData.append('reqdirect_url', queryRedirect);
                const csrfToken = document.querySelector('input[name="_csrfToken"]').value;

                fetch('<?= $this->Url->build(['controller' => 'Login', 'action' => 'verifyOtp']) ?>', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-Token': csrfToken,
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json'
                        },
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        // Reset button state
                        verifyOtpBtn.textContent = 'Login';
                        verifyOtpBtn.disabled = false;

                        if (data.success) {
                            // Show success message
                            window.location.href = data.redirect;
                            // Swal.fire({
                            //     icon: 'success',
                            //     title: 'Success!',
                            //     text: data.message,
                            //     confirmButtonText: 'OK'
                            // }).then(() => {
                            //     if (data.redirect) {
                            //         window.location.href = data.redirect;
                            //     }
                            // });
                        } else {
                            // Show error message
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: data.message || 'Invalid OTP. Please try again.',
                                confirmButtonText: 'OK'
                            });
                        }
                    })
                    .catch(error => {
                        // Reset button state
                        verifyOtpBtn.textContent = 'Login';
                        verifyOtpBtn.disabled = false;

                        // Show error message
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'An unexpected error occurred. Please try again.',
                            confirmButtonText: 'OK'
                        });
                    });
            });
        }

        // Handle Resend OTP button click
        if (resendOtpBtn) {
            resendOtpBtn.addEventListener('click', function() {
                // Trigger the send OTP function
                if (sendOtpBtn) {
                    sendOtpBtn.click();
                }
            });
        }

        // Add CSS for validation styling
        document.head.insertAdjacentHTML('beforeend', `
        <style>
        .is-invalid {
            border-color: #dc3545 !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
        .is-valid {
            border-color: #28a745 !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 0 0s.46 1.4.0 2.47z'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
        </style>
        `);

        const mobregExp = /^[0-9\b]+$/;
        document.getElementsByClassName("mobile")[0].style.display = "none";
        window.checkRadio = function (id) {
            console.log("The id is:", id);
            if(id == "phone"){
                document.getElementsByClassName("mobile")[0].style.display = "block";
                document.getElementsByClassName("email-id")[0].style.display = "none";
                document.getElementsByClassName("email-id")[1].style.display = "none";
            }
            else{
                document.getElementsByClassName("mobile")[0].style.display = "none";
                document.getElementsByClassName("email-id")[0].style.display = "block";
                document.getElementsByClassName("email-id")[1].style.display = "block";
            }
        };
        window.validNumber = function(event){
            var mobno = event.target.value + event.key;
            if(!mobregExp.test(mobno)){
                event.preventDefault();
            }
        }

        //disable otp wrapper and enable after click send otp button
        document.getElementsByClassName("otp-wrapper")[0].style.display = "none";
        document.getElementById("verify-otp-btn").style.display = "none";
        window.sendOTP = function(){
            document.getElementsByClassName("otp-wrapper")[0].style.display = "block";
            document.getElementById("verify-otp-btn").style.display = "block";
            document.getElementById("send-otp-btn").style.display = "none";
        }

        //Code for mobile view
        document.getElementsByClassName("otp-mobile")[0].style.display = "none";
        window.checkOtpRadio = function (id) {
            console.log("The id is:", id);
            if(id == "otp-phone"){
                document.getElementsByClassName("otp-mobile")[0].style.display = "block";
                document.getElementsByClassName("otp-email")[0].style.display = "none";
                document.getElementsByClassName("otp-email")[1].style.display = "none";
            }
            else{
                document.getElementsByClassName("otp-mobile")[0].style.display = "none";
                document.getElementsByClassName("otp-email")[0].style.display = "block";
                document.getElementsByClassName("otp-email")[1].style.display = "block";
            }
        };
        window.validNumber = function(event){
            var mobno = event.target.value + event.key;
            if(!mobregExp.test(mobno)){
                event.preventDefault();
            }
        }
    });

</script>