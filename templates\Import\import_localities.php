<?php
/**
 * @var \App\Controller\ImportController $this
 * @var string $message
 */
?>
<div class="import-localities-result">
    <h2>Localities Import Summary</h2>
    <?php if (!empty($error)): ?>
        <div class="error">
            <strong>Error:</strong> <?= h($error) ?>
            <?php if (!empty($excelPath)): ?>
                <div>Path: <?= h($excelPath) ?></div>
            <?php endif; ?>
        </div>
    <?php else: ?>
        <h3>Imported Cities</h3>
        <table border="1">
            <tr>
                <th>Sheet Name</th>
                <th>City Name</th>
                <th>Localities Inserted</th>
                <th>Localities Skipped</th>
                <th>Localities Already Exist</th>
            </tr>
            <?php foreach ($cityStats as $stat): ?>
                <tr>
                    <td><?= h($stat['sheet']) ?></td>
                    <td><?= h($stat['city']) ?></td>
                    <td><?= h($stat['inserted']) ?></td>
                    <td><?= h($stat['skipped']) ?></td>
                    <td><?= h($stat['existsCount']) ?></td>
                </tr>
            <?php endforeach; ?>
        </table>
        <h3>Overall Summary</h3>
        <ul>
            <li>Total Localities Inserted: <strong><?= h($overallInserted) ?></strong></li>
            <li>Total Localities Skipped: <strong><?= h($overallSkipped) ?></strong></li>
        </ul>
        <?php if (!empty($missingCities)): ?>
            <h3>Missing Cities (Not Found in DB)</h3>
            <ul>
                <?php foreach ($missingCities as $missing): ?>
                    <li><?= h($missing) ?></li>
                <?php endforeach; ?>
            </ul>
        <?php endif; ?>
    <?php endif; ?>
</div>
