<?php
declare(strict_types=1);

namespace App\Controller\Admin;

use App\Controller\AppController;
use Cake\Routing\Router;
use Cake\Core\Configure;
use Cake\Utility\Security;
use Cake\I18n\FrozenTime;
use Authentication\PasswordHasher\DefaultPasswordHasher;
use Cake\Http\Cookie\Cookie;
use DateTime;
use Cake\Http\Response;
use Cake\Cache\Cache;
use Cake\Log\Log;


/**
 * Master Controller
 *
 */
class MasterDataController extends AppController
{
    public function initialize(): void
    {
        parent::initialize();
        $this->loadComponent('Flash');
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->loadComponent('CustomPaginator');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
    }

    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function index()
    {
        $status = $this->request->getQuery('status');
        $type = $this->request->getQuery('type');
        $search = $this->request->getQuery('search');
        $page = (int)$this->request->getQuery('page', 1);

        // Get sorting parameters
        $sort = $this->request->getQuery('sort', 'id');
        $direction = $this->request->getQuery('direction', 'desc');

        // Validate sort field to prevent SQL injection
        $allowedSortFields = ['id', 'type', 'title', 'description', 'status'];
        if (!in_array($sort, $allowedSortFields)) {
            $sort = 'id';
        }

        // Validate direction
        $direction = strtolower($direction) === 'asc' ? 'ASC' : 'DESC';

        $query = $this->MasterData->find()
            ->where(['MasterData.status !=' => 'D']);

        // Apply filters if provided
        if (!empty($status)) {
            $query->where(['MasterData.status' => $status]);
        }

        if (!empty($type)) {
            $query->where(['MasterData.type' => $type]);
        }

        // Apply search if provided
        if (!empty($search)) {
            $query->where([
                'OR' => [
                    'MasterData.title LIKE' => '%' . $search . '%',
                    'MasterData.description LIKE' => '%' . $search . '%',
                    'MasterData.type LIKE' => '%' . $search . '%'
                ]
            ]);
        }

        // Apply sorting BEFORE pagination
        $query->orderBy(['MasterData.' . $sort => $direction]);
         $limit = $this->request->getQuery('limit') ?? 10;
        $result = $this->CustomPaginator->paginate($query, [
            'limit' => (int)$limit,
            'page' => $page
        ]);

        $masters = $result['items'];
        $pagination = $result['pagination'];

        // AJAX request for pagination/filter
        if ($this->request->is('ajax') || $this->request->getQuery('ajax')) {
            $this->set(compact('masters', 'pagination', 'sort', 'direction'));
            $this->viewBuilder()->setLayout('ajax');
            $this->render('/element/Admin/MasterData/table_content');
            return $this->response;
        }

        $title = 'Master Data List';
        $this->set(compact('masters', 'pagination', 'status', 'type', 'search', 'title', 'sort', 'direction'));
    }

    /**
     * View method
     *
     * @param string|null $id Master id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $master = $this->MasterData->get($id, contain: []);
        $title = 'Master Data View';
        $this->set(compact('master', 'title'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add_old()
    {
        $master = $this->MasterData->newEmptyEntity();
        if ($this->request->is('post')) {
            $data = $this->request->getData();
            $data['description'] = strip_tags($data['description']);

            $master = $this->MasterData->patchEntity($master, $data);
            if ($this->MasterData->save($master)) {
                $this->Flash->success(__('The master has been saved.'));
                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The master could not be saved. Please, try again.'));
        }
        $this->set(compact('master'));
    }
    

    
    public function add()
    {
        $master = $this->MasterData->newEmptyEntity();

        if ($this->request->is('post')) {
            $data = $this->request->getData();
            $data['description'] = strip_tags($data['description']);

            // Check for duplicate entry
            $exists = $this->MasterData->find()
                ->where([
                    'type' => $data['type'],
                    'title' => $data['title']
                ])
                ->first();

            if ($exists) {
                $this->Flash->error(__('Duplicate entry: Master data with same type and title already exists.'));
            } else {
                $master = $this->MasterData->patchEntity($master, $data);

                if ($this->MasterData->save($master)) {
                    $this->Flash->success(__('The master has been saved.'));
                    return $this->redirect(['action' => 'index']);
                }

                $this->Flash->error(__('The master could not be saved. Please, try again.'));
            }
        }

        $this->set(compact('master'));
    }


    /**
     * Edit method
     *
     * @param string|null $id Master id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit_old($id = null)
    {
        $master = $this->MasterData->get($id, contain: []);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $data = $this->request->getData();
            $master = $this->MasterData->patchEntity($master, $data);
            if ($this->MasterData->save($master)) {
                $this->Flash->success(__('The master has been saved.'));
                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The master could not be saved. Please, try again.'));
        }
        $this->set(compact('master'));
    }

    public function edit($id = null)
    {
        $master = $this->MasterData->get($id, contain: []);

        if ($this->request->is(['patch', 'post', 'put'])) {
            $data = $this->request->getData();

            // Check for duplicates excluding the current record
            $exists = $this->MasterData->find()
                ->where([
                    'type' => $data['type'],
                    'title' => $data['title'],
                    'id !=' => $id // Exclude current record
                ])
                ->first();

            if ($exists) {
                $this->Flash->error(__('Duplicate entry: Another record with the same type and title already exists.'));
            } else {
                $master = $this->MasterData->patchEntity($master, $data);

                if ($this->MasterData->save($master)) {
                    $this->Flash->success(__('The master has been saved.'));
                    return $this->redirect(['action' => 'index']);
                }

                $this->Flash->error(__('The master could not be saved. Please, try again.'));
            }
        }

        $this->set(compact('master'));
    }


    /**
     * Delete method
     *
     * @param string|null $id Master id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $master = $this->MasterData->get($id);

        // Check if master data is being used in partners table
        $isUsedInPartners = $this->checkMasterDataUsageInPartners($master->id, $master->type);

        // Check if master data is being used in courses table
        $isUsedInCourses = $this->checkMasterDataUsageInCourses($master->id, $master->type);

        if ($isUsedInPartners || $isUsedInCourses) {
            $usedIn = [];
            if ($isUsedInPartners) $usedIn[] = 'partners';
            if ($isUsedInCourses) $usedIn[] = 'courses';

            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'message' => 'Cannot delete this master data as it is being used by one or more ' . implode(' and ', $usedIn) . '.'
            ]));
        }
        if ($this->MasterData->delete($master)) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => true,
                'message' => 'The master has been deleted.'
            ]));
        } else {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'message' => 'The master could not be deleted. Please, try again.'
            ]));
        }
        return $this->redirect(['action' => 'index']);
    }

    public function approve($id)
    {
        $master = $this->MasterData->get($id);
        $master->status = ($master->status === 'A') ? 'I' : 'A';

        if ($this->MasterData->save($master)) {
            $this->Flash->success(__('Master data status updated successfully.'), [
                'key' => 'master_success'
            ]);
        } else {
            $this->Flash->error(__('Unable to update master data status. Please try again.'), [
                'key' => 'master_error'
            ]);
        }

        return $this->redirect(['action' => 'index']);
    }

    public function exportmasters()
    {
        $status = $this->request->getQuery('status');
        $type = $this->request->getQuery('type');
        $query = $this->MasterData->find();

        // Apply filters
        if (!empty($status)) {
            $query->where(['MasterData.status' => $status]);
        } else {
            $query->where(['MasterData.status !=' => 'D']);
        }

        if (!empty($type)) {
            $query->where(['MasterData.type' => $type]);
        }

        $masters = $query->all();
        $filename = "masters_list_" . date('Y-m-d') . ".csv";

        $this->response = $this->response->withDownload($filename);
        $this->response = $this->response->withType('text/csv');

        $csvData = fopen('php://output', 'w');
        fputcsv($csvData, ['S.No', 'Type', 'Title', 'Description', 'Status']);

        $counter = 1;
        $statusMap = [
            'A' => 'Active',
            'I' => 'Inactive',
        ];

        $typeMap = [
            'yoga_style' => 'Yoga Style',
            'special_need' => 'Special Need',
            'accommodation' => 'Accommodation',
            'food' => 'Food',
            'technique' => 'Technique',
            'addon' => 'Addon',
            'level' => 'Level',
            'location_type' => 'Location Type',
            'climate' => 'Climate'
        ];

        foreach ($masters as $master) {
            $typeLabel = $typeMap[$master->type] ?? $master->type;
            $statusLabel = $statusMap[$master->status] ?? 'Unknown';
            fputcsv($csvData, [
                $counter++,
                $typeLabel,
                $master->title ?? 'None',
                // $master->slug ?? 'None',
                $master->description ?? 'None',
                $statusLabel
            ]);
        }

        fclose($csvData);
        return $this->response;
    }

    public function filterSearch()
    {
        $this->request->allowMethod(['get']);
        $status = $this->request->getQuery('filterStatus');
        $type = $this->request->getQuery('filterType');
        $page = $this->request->getQuery('page', 1);

        // Get sorting parameters
        $sort = $this->request->getQuery('sort', 'id');
        $direction = $this->request->getQuery('direction', 'desc');

        // Validate sort field to prevent SQL injection
        $allowedSortFields = ['id', 'type', 'title', 'description', 'status'];
        if (!in_array($sort, $allowedSortFields)) {
            $sort = 'id';
        }

        // Validate direction
        $direction = strtolower($direction) === 'asc' ? 'ASC' : 'DESC';

        if ($this->request->is('ajax')) {
            $query = $this->MasterData->find()
                ->where(['MasterData.status !=' => 'D']);

            if (!empty($status)) {
                $query->where(['MasterData.status' => $status]);
            }
            if (!empty($type)) {
                $query->where(['MasterData.type' => $type]);
            }

            // Apply sorting BEFORE pagination
            $query->orderBy(['MasterData.' . $sort => $direction]);

            $result = $this->CustomPaginator->paginate($query, [
                'limit' => 10,
                'page' => $page
            ]);

            $masters = $result['items'];
            $pagination = $result['pagination'];

            $statusMap = [
                'A' => ['label' => __('Active'), 'class' => 'badge-outline col-green'],
                'I' => ['label' => __('Inactive'), 'class' => 'badge-outline col-red'],
                'D' => ['label' => __('Deleted'), 'class' => 'badge-outline col-red'],
            ];

            $mastersData = [];
            $i = 1;

            foreach ($masters as $master) {
                $statusInfo = $statusMap[$master->status] ?? ['label' => __('Unknown'), 'class' => 'badge-outline col-grey'];
                $statusToggleUrl = Router::url(['controller' => 'MasterData', 'action' => 'approve', $master->id], true);
                $approveClass = ' btn-sm approve approve ms-2';
                $approveTitle = $master->status === 'A' ? 'Mark as Inactive' : 'Mark as Active';

                if(!empty($master->type)){
                    if($master->type == 'yoga_style') {
                        $masterType = 'Yoga Style';
                    } elseif($master->type == 'special_need') {
                        $masterType = 'Special Need';
                    } elseif($master->type == 'accommodation') {
                        $masterType = 'Accommodation';
                    } elseif($master->type == 'food') {
                        $masterType = 'Food';
                    } elseif($master->type == 'technique') {
                        $masterType = 'Technique';
                    } elseif($master->type == 'addon') {
                        $masterType = 'Addon';
                    } elseif($master->type == 'level') {
                        $masterType = 'Level';
                    } elseif($master->type == 'location_type') {
                        $masterType = 'Location Type';
                    } elseif($master->type == 'climate') {
                        $masterType = 'Climate';
                    } else {
                        $masterType = 'None';
                    }
                }
                
                $mastersData[] = [
                    'actions' =>
                    '<a href="' . Router::url(['controller' => 'MasterData', 'action' => 'view', $master->id], true) . '" class="btn-view" title="View"><i class="far fa-eye"></i></a> ' .
                        '<a href="' . Router::url(['controller' => 'MasterData', 'action' => 'edit', $master->id], true) . '" class="btn-edit ms-2" title="Edit"><i class="far fa-edit"></i></a> ' .
                        '<a href="javascript:void(0);" class="delete-button ms-2" data-id="' . $master->id . '" data-bs-toggle="modal" data-bs-target="#exampleModalCenter" title="Delete"><i class="far fa-trash-alt"></i></a> ' .
                        '<a href="' . $statusToggleUrl . '" class="' . $approveClass . '" title="' . $approveTitle . '"><i class="fas fa-check"></i></a>',
                    'id' => $master->id,
                    'type' =>  $masterType,
                    'title' => !empty($master->title) ? h($master->title) : 'None',
                    'slug' => !empty($master->slug) ? h($master->slug) : 'None',
                    'description' => !empty($master->description) ? h($master->description) : 'None',
                    'status' => '<div class="' . h($statusInfo['class']) . '">' . h($statusInfo['label']) . '</div>'

                ];

                $i++;
            }

            $this->set([
                'masters' => $mastersData,
                'pagination' => $pagination,
                '_serialize' => ['masters', 'pagination'],
            ]);

            return $this->response->withType('application/json')
                ->withStringBody(json_encode([
                    'data' => $mastersData,
                    'pagination' => $pagination
                ]));
        }

        return null;
    }

    /**
     * Check if master data is being used in partners table
     *
     * @param int $masterDataId The master data ID to check
     * @param string $masterDataType The type of master data
     * @return bool True if master data is being used, false otherwise
     */
    private function checkMasterDataUsageInPartners($masterDataId, $masterDataType)
    {
        $this->Partners = $this->fetchTable('Partners');

        switch ($masterDataType) {
            case 'yoga_style':
                // Check in partner_yoga_styles junction table
                $this->PartnerYogaStyles = $this->fetchTable('Partners');
                $count = $this->PartnerYogaStyles->find()
                    ->where(['yoga_style_id' => $masterDataId])
                    ->count();
                return $count > 0;

            case 'special_need':
                // Check in partner_special_needs junction table
                $this->PartnerSpecialNeeds = $this->fetchTable('Partners');
                $count = $this->PartnerSpecialNeeds->find()
                    ->where(['special_need_id' => $masterDataId])
                    ->count();
                return $count > 0;

            case 'technique':
                // Check in partners.techniques field (comma-separated values)
                $count = $this->Partners->find()
                    ->where([
                        'OR' => [
                            'Partners.techniques LIKE' => '%,' . $masterDataId . ',%',
                            'Partners.techniques LIKE' => $masterDataId . ',%',
                            'Partners.techniques LIKE' => '%,' . $masterDataId,
                            'Partners.techniques' => $masterDataId
                        ]
                    ])
                    ->count();
                return $count > 0;

            case 'level':
                // Check in partners.level field (comma-separated values)
                $count = $this->Partners->find()
                    ->where([
                        'OR' => [
                            'Partners.level LIKE' => '%,' . $masterDataId . ',%',
                            'Partners.level LIKE' => $masterDataId . ',%',
                            'Partners.level LIKE' => '%,' . $masterDataId,
                            'Partners.level' => $masterDataId
                        ]
                    ])
                    ->count();
                return $count > 0;

            default:
                // For other types, check if they might be used in any comma-separated fields
                // This is a fallback check for any other master data types
                $fieldsToCheck = ['techniques', 'level', 'domestic_tax_percentage', 'international_tax_percentage'];

                foreach ($fieldsToCheck as $field) {
                    $count = $this->Partners->find()
                        ->where([
                            'OR' => [
                                "Partners.{$field} LIKE" => '%,' . $masterDataId . ',%',
                                "Partners.{$field} LIKE" => $masterDataId . ',%',
                                "Partners.{$field} LIKE" => '%,' . $masterDataId,
                                "Partners.{$field}" => $masterDataId
                            ]
                        ])
                        ->count();

                    if ($count > 0) {
                        return true;
                    }
                }
                return false;
        }
    }


    private function checkMasterDataUsageInCourses($masterDataId, $masterDataType)
    {
        $this->Courses = $this->fetchTable('Courses');

        switch ($masterDataType) {
            case 'yoga_style':
                // Check in course_yoga_styles junction table
                $this->CourseYogaStyles = $this->fetchTable('CourseYogaStyles');
                $count = $this->CourseYogaStyles->find()
                    ->where(['yoga_style_id' => $masterDataId])
                    ->count();
                return $count > 0;

            case 'special_need':
                // Check in course_special_needs junction table
                $this->CourseSpecialNeeds = $this->fetchTable('CourseSpecialNeeds');
                $count = $this->CourseSpecialNeeds->find()
                    ->where(['special_need_id' => $masterDataId])
                    ->count();
                return $count > 0;

            case 'technique':
                // Check in courses.techniques field (comma-separated values)
                $count = $this->Courses->find()
                    ->where([
                        'OR' => [
                            'Courses.techniques LIKE' => '%,' . $masterDataId . ',%',
                            'Courses.techniques LIKE' => $masterDataId . ',%',
                            'Courses.techniques LIKE' => '%,' . $masterDataId,
                            'Courses.techniques' => $masterDataId
                        ]
                    ])
                    ->count();
                return $count > 0;

            case 'level':
                // Check in courses.level field (comma-separated values)
                $count = $this->Courses->find()
                    ->where([
                        'OR' => [
                            'Courses.level LIKE' => '%,' . $masterDataId . ',%',
                            'Courses.level LIKE' => $masterDataId . ',%',
                            'Courses.level LIKE' => '%,' . $masterDataId,
                            'Courses.level' => $masterDataId
                        ]
                    ])
                    ->count();
                return $count > 0;

            default:
                // For other types, check if they might be used in any comma-separated fields
                $fieldsToCheck = ['techniques', 'level'];

                foreach ($fieldsToCheck as $field) {
                    $count = $this->Courses->find()
                        ->where([
                            'OR' => [
                                "Courses.{$field} LIKE" => '%,' . $masterDataId . ',%',
                                "Courses.{$field} LIKE" => $masterDataId . ',%',
                                "Courses.{$field} LIKE" => '%,' . $masterDataId,
                                "Courses.{$field}" => $masterDataId
                            ]
                        ])
                        ->count();

                    if ($count > 0) {
                        return true;
                    }
                }
                return false;
        }
    }
}
