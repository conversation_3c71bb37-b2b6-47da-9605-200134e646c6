<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * PartnerCourseType Entity
 *
 * @property int $id
 * @property int $partner_id
 * @property int $course_type_id
 * @property \Cake\I18n\DateTime|null $created_at
 * @property \Cake\I18n\DateTime|null $modified_at
 *
 * @property \App\Model\Entity\Partner $partner
 * @property \App\Model\Entity\CourseType $course_type
 */
class PartnerCourseType extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'partner_id' => true,
        'course_type_id' => true,
        'created_at' => true,
        'modified_at' => true,
        'partner' => true,
        'course_type' => true,
    ];
}
