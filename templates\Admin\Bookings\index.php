<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Booking> $bookings
 */
?>
<?= $this->Html->meta('csrfToken', $this->request->getAttribute('csrfToken')) ?>

<!-- DataTable CSS -->
<?= $this->Html->css('https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap4.min.css') ?>

<style>
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        color: white;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    .stats-card.confirmed { background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); }
    .stats-card.pending { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
    .stats-card.cancelled { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
    .stats-card.revenue { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .stats-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .booking-status {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-confirmed { background: #d4edda; color: #155724; }
    .status-pending { background: #fff3cd; color: #856404; }
    .status-cancelled { background: #f8d7da; color: #721c24; }
    
    .payment-status {
        padding: 4px 10px;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .payment-paid { background: #d1ecf1; color: #0c5460; }
    .payment-pending { background: #ffeaa7; color: #6c5ce7; }
    .payment-cancelled { background: #fab1a0; color: #e17055; }
    .payment-refunded { background: #fd79a8; color: #e84393; }
    
    #filterSection {
        background: #ffffff;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        animation: slideDown 0.4s ease-out;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    #bookingFilterToggle {
        transition: all 0.3s ease !important;
        position: relative;
        border: 2px solid #007bff;
        background-color: #007bff;
        color: white;
    }

    #bookingFilterToggle:hover {
        background-color: #0056b3 !important;
        border-color: #0056b3 !important;
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0,123,255,0.3);
        color: white !important;
    }

    #bookingFilterToggle.active {
        background-color: #28a745 !important;
        border-color: #28a745 !important;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(40,167,69,0.4);
        color: white !important;
    }

    #bookingFilterToggle.active:hover {
        background-color: #218838 !important;
        border-color: #218838 !important;
        color: white !important;
    }

    #filterSection .card-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }

    #filterSection .form-group {
        margin-bottom: 20px;
    }

    #filterSection .form-group label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }

    #closeFilter {
        border: none;
        background: transparent;
        color: #6c757d;
        font-size: 16px;
        padding: 5px 10px;
    }

    #closeFilter:hover {
        color: #dc3545;
        background-color: #f8f9fa;
    }
    
    .table-hover tbody tr:hover {
        background-color: #f5f5f5;
        transform: scale(1.01);
        transition: all 0.2s ease;
    }
    
    .action-buttons .btn {
        margin-right: 5px;
        border-radius: 20px;
        padding: 5px 15px;
    }

    /* Header Search and Filter Styles */
    .filter-options {
        display: flex;
        align-items: center;
        gap: 15px;
        flex-wrap: wrap;
    }

    .search-container {
        min-width: 300px;
    }

    .search-container .form-control {
        font-size: 14px;
        height: 42px;
        transition: all 0.3s ease;
    }

    .search-container .form-control:focus {
        border-color: #667eea !important;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .search-container .input-group-text {
        height: 42px;
        display: flex;
        align-items: center;
    }

    .filter-button .btn {
        border-radius: 8px;
        font-weight: 600;
        padding: 10px 20px;
        height: 42px;
        transition: all 0.3s ease;
    }

    .filter-button .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .download-icon .btn {
        border-radius: 8px;
        font-weight: 600;
        padding: 10px 20px;
        height: 42px;
        transition: all 0.3s ease;
    }

    .download-icon .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    @media (max-width: 768px) {
        .filter-options {
            flex-direction: column;
            align-items: stretch;
            gap: 10px;
        }

        .search-container {
            min-width: 100%;
        }

        .filter-button, .download-icon {
            width: 100%;
        }

        .filter-button .btn, .download-icon .btn {
            width: 100%;
        }
    }
    
    th.no-sort::after, th.no-sort::before {
        display: none !important;
    }
    th.no-sort {
        cursor: default !important;
        background-image: none !important;
    }
</style>

<div class="main-content">
    <section class="section">
        <div class="section-body1">
            <div class="container-fluid">
                <?php
                $successMessage = $this->Flash->render('success');
                $errorMessage = $this->Flash->render('error');
                ?>
                <?php if (!empty($successMessage)): ?>
                    <?= $successMessage ?>
                <?php elseif (!empty($errorMessage)): ?>
                    <?= $errorMessage ?>
                <?php endif; ?>
            </div>
        </div>

       

        <!-- Main Content -->
        <div class="section-body" id="list">
            <div class="container-fluid">
                <div class="card card-primary">
                    <ul class="breadcrumb breadcrumb-style">
                        <li class="breadcrumb-item">Bookings</li>
                        <li class="breadcrumb-item">Bookings List</li>
                    </ul>
                    
                    <div class="card-header">
                        <div class="d-block d-sm-flex actions">
                            <div class="action-header">
                                <h4>Bookings Management</h4>
                            </div>
                            <div class="filter-options d-flex align-items-center gap-3">
                                <!-- Search Input -->
                                <div class="search-container">
                                    <div class="input-group">
                                        <input type="text"
                                               id="searchInput"
                                               class="form-control"
                                               placeholder="Search bookings, customers, courses..."
                                               value="<?= h($search ?? '') ?>"
                                               style="border: 2px solid #e0e0e0; border-radius: 8px 0 0 8px; padding: 19px 12px!important ;">
                                        <div class="input-group-append">
                                            <span class="input-group-text" style="border: 2px solid #e0e0e0; border-left: none; border-radius: 0 8px 8px 0; background: #f8f9fa;">
                                                <i class="fas fa-search text-muted"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Filter Button -->
                                <div class="filter-button">
                                    <button type="button" class="btn btn-primary" id="bookingFilterToggle" onclick="toggleBookingFilter(event)">
                                        <i class="fas fa-filter"></i> <span id="filterButtonText">Show Filters</span>
                                    </button>
                                </div>

                                <!-- Export Button -->
                                <div class="download-icon">
                                    <a href="<?= $this->Url->build(['action' => 'export']) ?>" class="btn btn-success">
                                        <i class="fas fa-file-download"></i> Export
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filter Section -->
                    <div class="card mt-3" id="filterSection" style="display: none;">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-filter"></i> Filter Bookings
                                <button type="button" class="btn btn-sm btn-outline-secondary float-end" onclick="closeBookingFilter(event)">
                                    <i class="fas fa-times"></i>
                                </button>
                            </h6>
                            <?= $this->Form->create(null, ['type' => 'get', 'class' => 'filter-form']) ?>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="status">Booking Status</label>
                                        <?= $this->Form->select('status', [
                                            '' => 'All Status',
                                            'pending' => 'Pending',
                                            'confirmed' => 'Confirmed',
                                            'cancelled' => 'Cancelled'
                                        ], [
                                            'value' => $status,
                                            'class' => 'form-control',
                                            'id' => 'status'
                                        ]) ?>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="payment_status">Payment Status</label>
                                        <?= $this->Form->select('payment_status', [
                                            '' => 'All Payment Status',
                                            'pending' => 'Pending',
                                            'paid' => 'Paid',
                                            'cancelled' => 'Cancelled',
                                            'refunded' => 'Refunded'
                                        ], [
                                            'value' => $paymentStatus,
                                            'class' => 'form-control',
                                            'id' => 'payment_status'
                                        ]) ?>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label for="date_from">From Date</label>
                                        <?= $this->Form->input('date_from', [
                                            'type' => 'date',
                                            'value' => $dateFrom,
                                            'class' => 'form-control',
                                            'id' => 'date_from',
                                            'label' => false
                                        ]) ?>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label for="date_to">To Date</label>
                                        <?= $this->Form->input('date_to', [
                                            'type' => 'date',
                                            'value' => $dateTo,
                                            'class' => 'form-control',
                                            'id' => 'date_to',
                                            'label' => false
                                        ]) ?>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <div class="d-flex">
                                            <button type="submit" class="btn btn-primary me-2">
                                                    <i class="fas fa-filter"></i> Apply
                                                </button>
                                            <a href="<?= $this->Url->build(['action' => 'index']) ?>" class="btn btn-outline-secondary">
                                                <i class="fas fa-redo"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?= $this->Form->end() ?>
                        </div>
                    </div>

                    <!-- Search and Pagination Component -->
                  



                    <div class="card-body">
                        <!-- Error/Success Message Area -->
                        <div id="messageContainer"></div>

                        <div id="table-container">
                            <?= $this->element('Admin/Bookings/table_content', compact('bookings', 'pagination')) ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<!-- DataTable JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap4.min.js"></script>



<script>

    var isBookingFilterOpen = false;

function toggleBookingFilter(event) {

    // Prevent any default behavior
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    var filterSection = document.getElementById('filterSection');
    var buttonText = document.getElementById('filterButtonText');
    var toggleButton = document.getElementById('bookingFilterToggle');

    if (!filterSection) {
        return;
    }

    if (isBookingFilterOpen) {
        // Close filter
        console.log('Closing filter');
        filterSection.style.display = 'none';
        buttonText.textContent = 'Show Filters';
        toggleButton.classList.remove('active');
        isBookingFilterOpen = false;
    } else {
        // Open filter
        console.log('Opening filter');
        filterSection.style.display = 'block';
        buttonText.textContent = 'Hide Filters';
        toggleButton.classList.add('active');
        isBookingFilterOpen = true;
    }
    return false;
}

function closeBookingFilter(event) {
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    var filterSection = document.getElementById('filterSection');
    var buttonText = document.getElementById('filterButtonText');
    var toggleButton = document.getElementById('bookingFilterToggle');

    if (filterSection) {
        filterSection.style.display = 'none';
        buttonText.textContent = 'Show Filters';
        toggleButton.classList.remove('active');
        isBookingFilterOpen = false;
    }

    return false;
}
    // Search functionality with real-time search on keypress
    var searchTimeout;

    $('#searchInput').on('input keyup', function() {
        clearTimeout(searchTimeout);
        var searchTerm = $(this).val().trim();

        // Debounce search - wait 500ms after user stops typing
        searchTimeout = setTimeout(function() {
            performSearch(searchTerm);
        }, 500);
    });

    // Clear search when input is completely empty
    $('#searchInput').on('keyup', function(e) {
        if (e.which === 8 || e.which === 46) { // Backspace or Delete
            var searchTerm = $(this).val().trim();
            if (searchTerm === '') {
                clearTimeout(searchTimeout);
                performSearch('');
            }
        }
    });

    function performSearch(searchTerm) {
        if (searchTerm === undefined) {
            searchTerm = $('#searchInput').val().trim();
        }

        loadDataWithSearch(1, searchTerm);
    }

    function loadDataWithSearch(page, searchTerm) {
        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Bookings', 'action' => 'index']) ?>',
            type: 'GET',
            data: {
                search: searchTerm,
                page: page,
                ajax: 1
            },
            beforeSend: function() {
                $('#table-container').html('<div class="text-center p-4"><i class="fas fa-spinner fa-spin"></i> Loading...</div>');
            },
            success: function(response) {
                $('#table-container').html(response);

                // Update URL without page reload
                var url = new URL(window.location.href);
                if (searchTerm) {
                    url.searchParams.set('search', searchTerm);
                } else {
                    url.searchParams.delete('search');
                }
                if (page > 1) {
                    url.searchParams.set('page', page);
                } else {
                    url.searchParams.delete('page');
                }
                window.history.replaceState({}, '', url.toString());
            },
            error: function() {
                console.error('Failed to load search results');
                $('#table-container').html('<div class="text-center p-4 text-danger">Failed to load results. Please try again.</div>');
            }
        });
    }

    // Load data function for pagination
    function loadData(page = 1) {
        var searchTerm = $('#searchInput').val().trim();
        loadDataWithSearch(page, searchTerm);
    }

    // Sorting is now handled by DataTable on the frontend

    // Filter functionality is now handled by the search_pagination component
</script>
<?php $this->end(); ?>



