
<?php $this->assign('title', h(!empty($course->meta_title) ? $course->meta_title : $course->name)); ?>
<?php $this->assign('meta_desc', h(!empty($course->meta_description) ? $course->meta_description : $course->short_description)); ?>
<?php $this->assign('keywords', h($course->meta_keywords)); ?>
<?php $this->assign('meta_robots', h($course->meta_robots)); ?>
<?php
// for og tags
ob_start();
foreach ($ogTags as $property => $content) {
    echo '<meta property="' . h($property) . '" content="' . h($content) . '">' . "\n";
}
$this->assign('og_tags', ob_get_clean());
?>
<!-- end of og tags -->
<!-- for schema generator -->
<?php
ob_start();
echo '<script type="application/ld+json">' . json_encode($schemaJson, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . '</script>';
$this->assign('json_ld_schema', ob_get_clean());
?>
<!-- end of schema generator -->

<link rel="stylesheet" href="<?= $this->Url->webroot('css/course.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/testimonial.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/card.css') ?>">

<section class="px-6 course-detail-container">
    <div class="pt-2 md:px-10 lg:px-25 xl:pl-40 xl:pr-21 2xl:px-60">
        <nav class="flex breadcrumb-nav desktop-view" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-1 md:space-x-0 rtl:space-x-reverse">
                <li>
                    <a href="<?= $this->Url->build(['controller' => 'Home', 'action' => 'index']) ?>"
                        class="flex items-center text-sm text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">Home</a>
                </li>
                <li class="flex items-center">
                    <svg class="rtl:rotate-180 w-[5px] h-3 text-[#1C1B1F] mx-2" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 9 4-4-4-4" />
                    </svg>
                    <a href="<?= $this->Url->build([
                                    'lang' => $this->request->getParam('lang'),
                                    'controller' => 'Courses',
                                    'action' => 'index',
                                    'country' => $this->request->getParam('country') ?? 'india'
                                ]) ?>"
                        class="flex items-center text-sm text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">Courses</a>
                </li>

                <?php if (!empty($country)): ?>
                    <li class="flex items-center">
                        <svg class="rtl:rotate-180 w-[5px] h-3 text-[#1C1B1F] mx-2" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="m1 9 4-4-4-4" />
                        </svg>
                        <span
                            class="flex items-center text-sm text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"><?= h($country) ?></span>

                    </li>
                <?php endif; ?>

                <?php if (!empty($state)): ?>
                    <li class="flex items-center">
                        <svg class="rtl:rotate-180 w-[5px] h-3 text-[#1C1B1F] mx-2" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="m1 9 4-4-4-4" />
                        </svg>
                        <span
                            class="flex items-center text-sm text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"><?= h($state) ?></span>
                    </li>
                <?php endif; ?>

                <?php if (!empty($city)): ?>
                    <li class="flex items-center">
                        <svg class="rtl:rotate-180 w-[5px] h-3 text-[#1C1B1F] mx-2" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="m1 9 4-4-4-4" />
                        </svg>
                        <span
                            class="flex items-center text-sm text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"><?= h($city) ?></span>
                    </li>
                <?php endif; ?>

                <li class="flex items-center" aria-current="page">
                    <svg class="rtl:rotate-180 w-[5px] h-3 text-[#1C1B1F] mx-2" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 9 4-4-4-4" />
                    </svg>
                    <span
                        class="flex items-center text-sm text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"><?= h($course->name) ?></span>
                </li>
            </ol>
        </nav>
        <h1 class="heading"><?= h($course->name) ?> <span class='rating'><span class="rate-point">4.5</span> <i
                    class='fas fa-star'></i> <span class="review-info">(36 reviews)</span></span> </h1>
        <div class="yoga-detail-wrapper">
            <div class="block lg:flex lg:items-start">
                <div x-data="{  
                            autoplayIntervalTime: 4000,
                            slides: [
                                <?php
                                $hasGalleries = false;
                                if (!empty($course->course_galleries)) {
                                    $hasGalleries = true;
                                    foreach ($course->course_galleries as $gallery):
                                        $mediaPath = $gallery->media;
                                        $mediaUrl = $this->Url->webroot("uploads/" . h($mediaPath));
                                        $extension = strtolower(pathinfo($mediaPath, PATHINFO_EXTENSION));
                                        $isVideo = in_array($extension, ['mp4', 'webm', 'ogg', 'mov', 'mkv', 'avi']);
                                ?>
                                    {
                                        type: '<?= $isVideo ? 'video' : 'image' ?>',
                                        src: '<?= $mediaUrl ?>',
                                        alt: '<?= h($course->name) ?>'
                                    },
                                <?php endforeach;
                                }
                                if (!$hasGalleries): ?>
                                    {
                                        type: 'image',
                                        src: '<?= $this->Url->webroot("img/default-course.png") ?>',
                                        alt: '<?= h($course->name) ?>'
                                    },
                                <?php endif; ?>
                            ],
                            currentSlideIndex: 0,
                            isPaused: false,
                            autoplayInterval: null,
                            previous() {
                                this.currentSlideIndex = this.currentSlideIndex > 0 ? this.currentSlideIndex - 1 : this.slides.length - 1;
                            },
                            next() {
                                this.currentSlideIndex = this.currentSlideIndex < this.slides.length - 1 ? this.currentSlideIndex + 1 : 0;
                            },
                            autoplay() {
                                this.autoplayInterval = setInterval(() => {
                                    if (!this.isPaused) {
                                        this.next();
                                    }
                                }, this.autoplayIntervalTime);
                            },
                            setAutoplayInterval(newIntervalTime) {
                                clearInterval(this.autoplayInterval);
                                this.autoplayIntervalTime = newIntervalTime;
                                this.autoplay();
                            },
                        }" x-init="autoplay" class="relative w-full detail-slider">

                    <!-- Slider Area -->
                    <div class="relative w-full min-h-[272px] lg:min-h-[472px] yoga-retreat-slider overflow-hidden">
                        <template x-for="(slide, index) in slides" :key="index">
                            <div x-show="currentSlideIndex === index" class="absolute inset-0"
                                x-transition.opacity.duration.1000ms>

                                <!-- Image Slide -->
                                <template x-if="slide.type === 'image'">
                                    <img class="absolute w-full h-full object-cover rounded-[10px]" :src="slide.src"
                                        :alt="slide.alt" />
                                </template>

                                <!-- Video Slide -->
                                <template x-if="slide.type === 'video'">
                                    <video class="absolute w-full h-full object-cover rounded-[10px]" :src="slide.src"
                                        autoplay muted playsinline @playing="isPaused = true" @ended="isPaused = false"
                                        loop></video>
                                </template>
                            </div>
                        </template>
                    </div>

                    <!-- Navigation Buttons -->
                    <!-- <div class="absolute inset-0 flex items-center justify-between p-4">
                        <button @click="previous(); isPaused = true; setTimeout(() => { isPaused = false }, 1500)"
                            class="p-1 rounded-full shadow bg-white/80 text-gray-800 hover:bg-white">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                stroke="currentColor" class="w-6 h-6">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
                            </svg>
                        </button>
                        <button @click="next(); isPaused = true; setTimeout(() => { isPaused = false }, 1500)"
                            class="p-1 rounded-full shadow bg-white/80 text-gray-800 hover:bg-white">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                stroke="currentColor" class="w-6 h-6">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                            </svg>
                        </button>
                    </div> -->

                    <!-- Indicators -->
                    <div class="indicator absolute rounded-radius bottom-3 md:bottom-5 left-1/2 z-20 flex -translate-x-1/2 gap-4 md:gap-3 bg-transparent px-1.5 py-1 md:px-2"
                        role="group" aria-label="slides">
                        <template x-for="(slide, index) in slides" :key="index">
                            <button class="size-3 rounded-full transition bg-on-surface dark:bg-on-surface-dark"
                                x-on:click="currentSlideIndex = index; isPaused = true; setTimeout(() => { isPaused = false }, 1500)"
                                x-bind:class="[currentSlideIndex === index ? 'bg-[#D87A61]' : 'bg-[#D87A61]/40']"
                                x-bind:aria-label="'slide ' + (index + 1)">
                            </button>
                        </template>
                    </div>
                </div>
                <div class="yoga-details-content">
                    <div class="block lg:flex lg:items-start">
                        <div class="left-contents">
                            <label class="course-title">Course Type</label>
                            <p><?= !empty($course->course_type) ? h($course->course_type->name) : 'Yoga Teacher Training Course' ?>
                            </p>
                        </div>
                        <div class="right-contents">
                            <label class="course-title">Language</label>
                            <p><?= !empty($course->language) ? h($course->language) : 'English' ?></p>
                        </div>
                    </div>
                    <div class="location-content">
                        <label class="course-title">Where and When</label>
                       <?php
                        if (!empty($modes)) {
                            echo '<p class="flex items-center gap-2">' . $modes . '</p>';
                        }
                        ?>
                        <div class="flex items-center gap-2">
                            <i class="fas fa-calendar-alt text-[#D87A61]"></i>
                            <?php
                           $course_date = '';
                            if (count($course->course_batches) > 0 && isset($course->course_batches[0])):
                                $course_date = $course->course_batches[0]->start_date->format('d-m-Y');
                                if(count($course->course_batches) > 1){
                                    $course_date .= ' <small>+' . (count($course->course_batches) - 1) . ' more</small>';
                                }
                           
                                echo  $course_date;
                            ?>
                                <div x-data="batchViewer()" x-cloak>
                                    <!-- Trigger Button -->
                                    <a href="#"
                                        class="text-blue-600 text-sm underline"
                                        data-batches='<?= json_encode($course->course_batches, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_AMP | JSON_HEX_QUOT) ?>'
                                        @click.prevent="openBatchModal(JSON.parse($el.getAttribute('data-batches')))">
                                        View All
                                    </a>

                                    <!-- Modal -->
                                    <div x-show="showModal"
                                        x-transition
                                        class="fixed inset-0 bg-opacity-50 z-40 flex items-center justify-center">
                                        <div class="bg-white p-6 rounded shadow-lg max-w-200 w-full relative z-50">
                                            <button @click="showModal = false"
                                                class="absolute top-2 right-2 text-gray-600 hover:text-black text-xl">&times;</button>

                                            <!-- <h3 class="text-lg font-bold mb-4">All Course Batches</h3> -->
                                            <ul class="grid grid-cols-1 sm:grid-cols-2 gap-3 max-h-[300px] overflow-y-auto text-sm">
                                                <template x-for="(batch, index) in batches" :key="index">
                                                <li>
                                                    <button  class="border-1 w-full text-left p-3 rounded transition flex flex-col">
                                                        <div class="flex items-center space-x-2">
                                                            <i class="fas fa-calendar-alt text-gray-500"></i>
                                                            <span x-text="formatDate(batch.start_date) + ' to ' + formatDate(batch.end_date)"></span>
                                                        </div>
                                                        <div class="ml-5 text-gray-500">
                                                            (<span title="Time is shown in 24-hour format" x-text="formatTime(batch.start_time) + ' to ' + formatTime(batch.end_time)"></span>)
                                                        </div>
                                                        <!-- <template x-if="batch.name">
                                                            <div class="mt-1 text-gray-700" x-text="'(' + batch.name + ')'"></div>
                                                        </template> -->
                                                    </button>
                                                </li>
                                            </template>
                                            </ul>
                                                <!-- <div class="mt-4 text-center">
                                                    <button @click="enroll"
                                                        class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                                                        :disabled="!selectedBatchId">
                                                    Enroll
                                                </button>
                                            </div> -->
                                        </div>
                                    </div>
                                </div>
                            <?php 
                            else:
                                echo "NA";
                            endif;
                            ?>
                            (<?= $course->duration_details ?>)
                           
                        </div>
                    </div>
                    <div class="block lg:flex lg:items-start">
                        <div class="left-contents">
                            <label class="course-title">Yoga Style</label>
                            <p title="See Fee in detail section"><?php
                                if (!empty($yogaStyles)) {
                                    echo implode(' | ', $yogaStyles);
                                } else {
                                    echo 'NA';
                                }
                                ?></p>
                        </div>
                        <div class="right-contents">
                            <label class="course-title">Levels</label>
                            <p><?php
                                if (!empty($levels)) {
                                    echo implode('<span class="h-divider"></span>', array_map('trim', $levels));
                                } else {
                                    echo 'NA';
                                }
                                ?></p>
                        </div>
                    </div>

                    <div id="moreContent" class="hidden lg:block">
                        <div class="block lg:flex lg:items-start">
                            <div class="left-contents">
                                <label class="course-title">Accommodation</label>
                                <p><?php
                                    if (!empty($course->is_accommodation_included) && $course->accommodation_options) {
                                        echo $course->accommodation_options;
                                    } else {
                                        echo 'NA';
                                    }
                                    ?></p>
                            </div>
                            <div class="right-contents">
                                <label class="course-title">Food</label>
                                <p><?php
                                    if (!empty($course->is_food_included) && $course->food_options) {
                                    echo $course->food_options;
                                    } else {
                                        echo 'NA';
                                    }
                                    ?></p>
                            </div>
                        </div>
                        <div class="block lg:flex lg:items-start fees-enroll">
                            <div class="left-contents">
                                <label class="course-title">Fee: </label>
                                <p>
                                    <?= !empty($firstBasePrice) ? $firstBasePrice->price.' '.$firstBasePrice->currency->name.'<span>*</span>' : ''; ?>
                                </p>
                            </div>
                            <div class=" faq-container flex">
                                <div class="contents">
                                    <button
                                        class="rounded-[5px] w-[137px] h-[52px] bg-[#D87A61] hover:bg-[#D87A61] text-white enroll"
                                        x-data="enrollmentHandler()" @click="handleEnrollClick">
                                        Enroll Now
                                    </button>
                                </div>
                                <div class="contents">
                                    <button class="rounded-[5px] w-[54px] h-[52px] btn-heart"><img
                                            src="<?= $this->Url->webroot('img/offer-heart.png') ?>" alt="Heart icon"></button>
                                </div>
                            </div>

                        </div>
                    </div>
                    <!-- Toggle Button (mobile only) -->
                    <div class="block lg:hidden mt-4 text-center">
                        <button id="toggleMoreBtn" class="text-primary underline font-semibold text-sm see-more-cta ">
                            See More
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="py-5 px-6 detail-offer-container">
    <div class="md:px-10 lg:px-25 xl:pl-40 xl:pr-21 2xl:px-60">
        <div class="yoga-detail-wrapper">
            <div class="block lg:flex ">
                <div class="relative w-full detail-content">
                    <h2 class="heading"><?= h($course->desc1_label) ?></h2>
                    <?php echo !empty($course->desc1_text) ? ($course->desc1_text) : ''; ?>
                </div>
                <div class="offer-content w-[520px]">
                    <h2>Offered By</h2>
                    <div class="block lg:flex lg:items-center">
                        <div class="offer-image">
                            <?php if (!empty($course->partner->logo)): ?>
                                <img src="<?php echo $this->Url->webroot('uploads/partners/logo/' . $course->partner->logo); ?>"
                                    alt="<?= h($course->partner->name) ?>">
                            <?php else: ?>
                                <img src="<?php echo $this->Url->webroot('img/laxman-jhula.png'); ?>"
                                    alt="<?= h($course->partner->name ?? 'Center') ?>">
                            <?php endif; ?>
                        </div>
                        <div class="profile">
                            <p>
                                <?php if (!empty($course->partner)): 
                                    $lang = h($this->request->getParam('lang'));
                                    $baseUrl = $this->Url->build('/') . $lang . '/yoga-centers';

                                    $segments = [];

                                    if (!empty($course->partner->country->name)) {
                                        $segments[] = h($course->partner->country->name);
                                    }

                                    if (!empty($course->partner->state->name)) {
                                        $segments[] = 'region';
                                        $segments[] = h($course->partner->state->name);
                                    }

                                    if (!empty($course->partner->city->name)) {
                                        $segments[] = h($course->partner->city->name);
                                    }

                                    if (!empty($course->partner->slug)) {
                                        $segments[] = h($course->partner->slug);
                                    }

                                    $finalUrl = $baseUrl . '/' . implode('/', $segments);
                                    ?>
                                    <a href="<?= $finalUrl ?>">
                                        <?= h($course->partner->name) ?>
                                    </a>
                                <?php endif; ?>

                            </p>
                            <p><?= h(!empty($course->partner->city) ? $course->partner->city->name : '') ?><?= !empty($course->partner->city) && !empty($course->partner->state) ? ', ' : '' ?><?= h(!empty($course->partner->state) ? $course->partner->state->name : '') ?>
                            </p>
                        </div>
                    </div>
                    <!-- <div class="location-content">
                                <p><i class="fas fa-map-marker-alt"></i> <span>On-site in <a>Rishikesh, Uttarakhand, North India</a></span></p>
                                <p><i class='fas fa-calendar-alt'></i> <span>30-03-25  to  10-06-25</span> <span class="sm-font">(3 months)</span></p>
                            </div> -->
                    <?php if($course->faq){ ?>
                        <div class="faq">
                            <button id="openFaqBtn" class="rounded-[5px] lg:w-[165px] h-[52px] text-[#222222] btn-faq">Frequently Asked Questions</button>
                        </div>
                        <!-- Modal -->
                        <!-- Modal (Initially hidden) -->
                       <div id="faqModal" class="hidden fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 
                      bg-white border border-gray-300 shadow-lg p-6 rounded-lg max-w-xl w-full z-50">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-xl font-bold">Frequently Asked Questions</h2>
                                <button id="closeFaqBtn" class="text-gray-600 hover:text-black text-lg font-bold">✖</button>
                            </div>

                            <div class="max-h-[400px] overflow-y-auto pr-2">
                                <?php echo html_entity_decode($course->faq); ?>
                            </div>
                        </div>
                    <?php } ?>
                    <div class="hidden lg:flex lg:items-center faq-container mt-1 desktop">
                        <div class="faq">
                            <button class="rounded-[5px] lg:w-[165px] lg:h-[52px] text-[#222222]">Ask a
                                Question</button>
                        </div>
                        <div class="contents">
                            <button
                                class="rounded-[5px] w-[137px] h-[52px] bg-[#D87A61] hover:bg-[#D87A61] text-white enroll" x-data="enrollmentHandler()"
    @click="handleEnrollClick">Enroll
                                Now</button>
                        </div>
                        <div class="contents">
                            <button class="rounded-[5px] w-[54px] h-[52px] btn-heart"><img
                                    src="<?= $this->Url->webroot('img/offer-heart.png') ?>" alt="Heart icon"></button>
                        </div>
                    </div>
                    <div class="block lg:hidden faq-container">
                        <div class="faq">
                            <button class="rounded-[5px] w-[165px] h-[52px] text-[#222222]">Ask a Question</button>
                        </div>
                        <div class="flex items-center justify-between w-full mb-4">
                            <div class="contents">
                                <button
                                    class="rounded-[5px] w-[137px] h-[52px] bg-[#D87A61] hover:bg-[#D87A61] text-white enroll py-3 px-3" x-data="enrollmentHandler()"
    @click="handleEnrollClick">Enroll
                                    Now</button>
                            </div>
                            <div class="contents">
                                <button class="rounded-[5px] w-[54px] h-[52px] btn-heart"><img
                                        src="<?= $this->Url->webroot('img/offer-heart.png') ?>" alt="Heart icon"></button>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
</section>
<section class="py-5 px-6 course-curriculum">
    <div class="md:px-10 lg:px-25 xl:pl-40 xl:pr-21 2xl:px-60">
        <h2><?= h($course->desc2_label) ?></h2>
        <?php echo !empty($course->desc2_text) ? ($course->desc2_text) : ''; ?>
    </div>
</section>
<?php if(!empty($course->desc3_text)): ?>
<section class="py-5 px-6 detail-content">
    <div class="md:px-10 lg:px-25 xl:pl-40 xl:pr-21 2xl:px-60">
        <h2><?= h($course->desc3_label) ?></h2>
        <?php echo !empty($course->desc3_text) ? ($course->desc3_text) : ''; ?>
    </div>
</section>
<?php endif; ?>
<section class="testimonial">

    <div class="px-6 md:px-10 lg:px-25 xl:pl-40 xl:pr-21 2xl:px-60">
        <h2>Testimonials</h2>
        <div x-data="testimonialCarousel()" x-init="init()" class="mx-auto relative">
            <div class="relative overflow-hidden">
                <div class="flex transition-transform duration-500 w-100"
                    :style="`transform: translateX(-${current * (100 / itemsPerView)}%); `" @touchstart="handleTouchStart" @touchend="handleTouchEnd">
                    <template x-for="testimonial in testimonials" :key="testimonial.name">
                        <div class="w-full sm:w-1/2 flex-none p-2">
                            <div class="bg-transparent py-6 rounded-lg h-full">
                                <div class="flex items-center testi-wrapper">
                                    <img :src="testimonial.image" alt="Avatar" class="rounded-full mr-4 testi-img">
                                    <div class="testi-name-rating">
                                        <p class="text-lg font-semibold" x-text="testimonial.name"></p>
                                        <span class='rating'>4.5</span> <i class='fas fa-star'></i>
                                    </div>
                                </div>
                                <p class="text-gray-700 testi-desc" x-text="testimonial.quote"></p>
                            </div>
                        </div>
                    </template>
                </div>

                <!-- Dots -->
                <div class="mt-4 flex justify-center space-x-2 indicator desktop-view">
                    <template x-for="(testimonial, index) in testimonials.length - 1" :key="index">
                        <button @click="current = index"
                            :class="{'active': current === index, 'testimonial-dots': current !== index}"
                            class="w-3 h-3 rounded-full"></button>
                    </template>
                </div>
                <div class="mt-4 flex justify-center space-x-2 indicator mobile-view">
                    <template x-for="(testimonial, index) in testimonials.length" :key="index">
                        <button @click="current = index"
                            :class="{'active': current === index, 'testimonial-dots': current !== index}"
                            class="w-3 h-3 rounded-full"></button>
                    </template>
                </div>
            </div>
            <!-- Navigation Buttons -->
            <div class="absolute inset-y-0 left-[-40px] flex items-center nav-arrows desktop-view">
                <button @click="prev" :disabled="current <= 0" :class="{
                'opacity-50 cursor-not-allowed': current <= 0
                }" class="text-gray-500 hover:text-gray-800 p-2 bg-white rounded-full shadow btn-left"><img src="/img/arrow-back-black.png" alt="left arrow" /></button>
            </div>
            <div class="absolute inset-y-0 right-[-20px] flex items-center nav-arrows desktop-view">
                <button @click="next" :disabled="current >= testimonials.length - 2" :class="{
                'opacity-50 cursor-not-allowed': current >= testimonials.length - 2
                }" class="text-gray-500 hover:text-gray-800 p-2 bg-white rounded-full shadow btn-right"><img src="/img/arrow-next-black.png" alt="right arrow" /></button>
            </div>
            <div class="absolute inset-y-0 left-[-20px] flex items-center nav-arrows mobile-view">
                <button @click="prev" :disabled="current <= 0" :class="{
                'opacity-50 cursor-not-allowed': current <= 0
                }" class="text-gray-500 hover:text-gray-800 p-2 bg-white rounded-full shadow btn-left"><img src="/img/arrow-back-black.png" alt="left arrow" /></button>
            </div>
            <div class="absolute inset-y-0 right-[-20px] flex items-center nav-arrows mobile-view">
                <button @click="next" :disabled="current >= testimonials.length - 1" :class="{
                'opacity-50 cursor-not-allowed': current >= testimonials.length - 1
                }" class="text-gray-500 hover:text-gray-800 p-2 bg-white rounded-full shadow btn-right"><img src="/img/arrow-next-black.png" alt="right arrow" /></button>
            </div>
        </div>
    </div>
</section>
<?php
$moreCoursesJson = json_encode($moreCourses->toArray() ?? [], JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP);

?>
<section class="featured-yoga relative py-5 px-6 md:py-5 lg:py-5 xl:py-5 2xl:py-5 px-6 md:px-10 lg:px-25 xl:px-40 2xl:px-60">
    <img src="<?= $this->Url->webroot('img/flower.png') ?>" class="footer-flower block md:hidden" alt="flower" />
    <h2 class="">More Courses by <span
            class="text-[#D87A61]"><?= isset($course->partner) ? ucfirst(h($course->partner->name)) : '-' ?></span></h2>

    <div class="mx-auto mt-4">
        <div class="featured-yoga course-card-container grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 gap-4 desktop-view">
            <?php if (!empty($moreCourses) && count($moreCourses) > 0): ?>
            <template x-data="courseList()" x-for="item in moreCoursesJson" :key="item.id">
                <?= $this->element('frontend/Courses/card_alpine') ?>
            </template>
            <?php else: ?>
                <div class="col-span-3 text-center py-8">
                    <p>No other courses available from this center at the moment.</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Mobile view slider - ONLY ONE INSTANCE -->
        <?php if (!empty($moreCourses) && count($moreCourses) > 0): ?>
            <!-- New slider -->
            <div x-data="{  
                autoplayIntervalTime: 4000,          
                slides:[
                     <?php foreach ($moreCourses as $index => $moreCourse): ?>
                    {
                        imgSrc: '<?= !empty($moreCourse->image) ? $this->Url->webroot('uploads/' . h($moreCourse->image)) : $this->Url->webroot('img/default-course.png') ?>',
                        imgAlt: '<?= h($moreCourse->name) ?>',
                        info: '@ <?= isset($moreCourse->partner) ? h($moreCourse->partner->name) : h($course->partner->name ?? '') ?>',
                        title: '<?= h($moreCourse->name) ?>',
                        desc: '<?= !empty($moreCourse->short_description) ? h($moreCourse->short_description) : substr(strip_tags($moreCourse->desc1_text ?? ''), 0, 120) . '...' ?>',
                        date: '<?= !empty($moreCourse->start_date) ? $moreCourse->start_date->format('d-m-y') . (!empty($moreCourse->end_date) ? ' to ' . $moreCourse->end_date->format('d-m-y') : '') : '' ?>',
                        mode: '<?= !empty($moreCourse->mode) ? h($moreCourse->mode) : (!empty($moreCourse->on_site) && $moreCourse->on_site ? (!empty($moreCourse->city) ? h($moreCourse->city->name) : 'On-site') : (!empty($moreCourse->online_live) && $moreCourse->online_live ? 'Online - Live' : (!empty($moreCourse->online_vod) && $moreCourse->online_vod ? 'Online - VOD' : ''))) ?>',
                        lang: '<?= !empty($moreCourse->language) ? h($moreCourse->language) : '' ?>',
                        url: '<?= $this->Url->build('/') ?><?= h($this->request->getParam('lang')) ?>/yoga-courses/<?=  !empty($moreCourse->country) ? h($moreCourse->country->name) : '' ?>/region/<?= !empty($moreCourse->state) ? h($moreCourse->state->name) : '' ?>/<?= !empty($moreCourse->city) ? h($moreCourse->city->name) : '' ?>/<?= !empty($moreCourse->slug) ? h($moreCourse->slug) : '' ?>'
                    },
                    <?php endforeach; ?>
                ],        
                currentSlideIndex: 1,
                isPaused: false,
                autoplayInterval: null,

                startX: 0,
                endX: 0,
                handleSwipeStart(event) {
                    this.startX = event.type.includes('mouse') ? event.clientX : event.touches[0].clientX;
                },
                handleSwipeEnd(event) {
                    this.endX = event.type.includes('mouse') ? event.clientX : event.changedTouches[0].clientX;
                    const diff = this.startX - this.endX;

                    if (Math.abs(diff) > 50) {
                        if (diff > 0) {
                            this.next();
                        } else {
                            this.previous();
                        }
                    }
                },

                previous() {                
                    if (this.currentSlideIndex > 1) {                    
                        this.currentSlideIndex = this.currentSlideIndex - 1                
                    } else {           
                        this.currentSlideIndex = this.slides.length                
                    }            
                },            
                next() {                
                    if (this.currentSlideIndex < this.slides.length) {                    
                        this.currentSlideIndex = this.currentSlideIndex + 1                
                    } else {                   
                        this.currentSlideIndex = 1                
                    }            
                },  
                autoplay() {
                    this.autoplayInterval = setInterval(() => {
                        if (! this.isPaused) {
                            this.next()
                        }
                    }, this.autoplayIntervalTime)
                },
                setAutoplayInterval(newIntervalTime) {
                    clearInterval(this.autoplayInterval)
                    this.autoplayIntervalTime = newIntervalTime
                    this.autoplay()
                },
                generateSeoLink(item) {
                    const parts = [baseUrl + lang + '/yoga-courses'];

                    if (item.country) {
                        parts.push(item.country.replace(/\s+/g, '-').toLowerCase());
                    }
                
                    if (item.state) {
                        parts.push('region', item.state.replace(/\s+/g, '-').toLowerCase());
                    }

                    if (item.city) {
                        parts.push(item.city.replace(/\s+/g, '-').toLowerCase());
                    }

                    if (item.slug) {
                        parts.push(item.slug);
                    }

                    return parts.join('/');
                }
            }" x-init="currentSlideIndex = 1" class="relative w-full featured-slider mobile-view">

                <!-- SLIDES -->
                <div class="card-container relative w-full mb-[20px] min-h-[634px] overflow-hidden"
                    @touchstart="handleSwipeStart" @touchend="handleSwipeEnd" @mousedown="handleSwipeStart"
                    @mouseup="handleSwipeEnd">
                    <div class="flex transition-transform duration-500 ease-linear" :style="`transform: translateX(-${(currentSlideIndex - 1) * 100}%);`">
                        <template x-for="(slide, index) in slides" :key="index">
                                <!-- for clickable -->
                                <a :href="generateSeoLink(slide)" class="link w-full flex-shrink-0">

                                <!-- <div x-show="currentSlideIndex == index + 1" class="absolute inset-0"
                                    x-transition.opacity.duration.1000ms> -->
                                    <!-- <img class="absolute w-full h-full inset-0 object-cover text-on-surface dark:text-on-surface-dark" x-bind:src="slide.imgSrc" x-bind:alt="slide.imgAlt" /> -->
                                    <div class="course-card bg-white rounded-lg h-auto">
                                        <img class="w-full h-56 object-cover rounded-md yoga-img" x-bind:src="slide.imgSrc"
                                        x-bind:alt="slide.imgAlt">
                                        <div class="card-body">
                                            <!-- <div class="card-star">
                                                <span class="star on"></span>
                                                <span class="star on"></span>
                                                <span class="star on"></span>
                                                <span class="star half"></span>
                                                <span class="star"></span>
                                            </div> -->
                                            <p class="info line-clamp-2 h-[40px]" x-text="slide.info"></p>
                                            <h3 class="text-xl font-semibold yoga-name line-clamp-2 h-[68px]">
                                                <span x-html="slide.title" class="line-clamp-2"></span>
                                                <span class='rating-wrapper'><span class='rating'>4.5</span> <i class='fas fa-star'></i></span>
                                            </h3>
                                            <p class="text-gray-600 mt-2 line-clamp-4 yoga-description line-clamp-4 h-[72px]" x-text="slide.desc"></p>
                                            <p class="time" x-html="`<i class='fas fa-calendar-alt'></i> `+slide.date"></p>
                                            <p class="mode" x-html="`<i class='fas fa-laptop-code'></i> `+slide.mode"></p>
                                            <p class="lang mt-2" x-html="`<i class='fas fa-globe'></i> `+slide.lang"></p>
                                            <!-- <button class="mt-4 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Learn More</button> -->
                                        </div>
                                    </div>
                                <!-- </div> -->
                            </a>
                        </template>
                    </div>
                </div>

                <!-- indicators -->
                <!-- <div class="indicator absolute rounded-radius bottom-3 md:bottom-5 left-1/2 z-20 flex -translate-x-1/2 gap-4 md:gap-3 bg-transparent px-1.5 py-1 md:px-2" role="group" aria-label="slides" >
                    <template x-for="(slide, index) in slides">
                        <button class="size-3 rounded-full transition bg-on-surface dark:bg-on-surface-dark" x-on:click="currentSlideIndex = index + 1" x-bind:class="[currentSlideIndex === index + 1 ? 'bg-[#D87A61]' : 'bg-[#D87A61]/40']" x-bind:aria-label="'slide ' + (index + 1)"></button>
                    </template>
                </div> -->
                <button type="button" @click="previous()" :disabled="currentSlideIndex === 1" class="absolute top-[20%] start-[10px] end-[0px] z-30 flex items-center justify-center px-0 cursor-pointer group focus:outline-none disabled:opacity-50 prev">
                    <img src="<?= $this->Url->webroot('img/arrow-back-black.png') ?>" alt="Arrow Back">
                </button>
                <button type="button" @click="next()" :disabled="currentSlideIndex === slides.length" class="absolute top-[20%] end-[10px] z-30 flex items-center justify-center px-0 cursor-pointer group focus:outline-none disabled:opacity-50 next">
                    <img src="<?= $this->Url->webroot('img/arrow-next-black.png') ?>" alt="Arrow Next">
                </button>
            </div>
        <?php else: ?>
            <div class="text-center py-8 mobile-view">
                <p>There are no other courses available from this center at the moment.</p>
            </div>
        <?php endif; ?>
    </div>

</section>
<!-- REMOVE THIS DUPLICATE MOBILE SLIDER -->
<!-- The duplicate mobile slider that was here has been removed -->
</div>
</section>
<script>
const moreCoursesJson = <?= $moreCoursesJson ?>;
function courseList() {
    return {
        baseUrl: '<?= $this->Url->build('/') ?>',
        lang: '<?= $this->request->getParam('lang') ?>',
        moreCoursesJson: moreCoursesJson,
        getMode(item) {
            if (item.online_live) {
                return `<i class='fas fa-laptop-code'></i> Online - Live`;
            } else if (item.online_vod) {
                return `<i class='fas fa-play-circle'></i> Online - VOD`;
            } else if (item.on_site) {
                const location = item.state?.name ?? '';
                return `<i class='fas fa-map-marker-alt'></i> ${location}`;
            } else {
                return '';
            }
        }
    };
}
  /*const openBtn = document.getElementById('openFaqBtn');
  const closeBtn = document.getElementById('closeFaqBtn');
  const modal = document.getElementById('faqModal');

  if(openBtn){
    openBtn.addEventListener('click', () => {
        modal.classList.remove('hidden');
    });
  }

  if(openBtn){
    openBtn.addEventListener('click', () => {
        modal.classList.add('hidden');
    });
    }

  // Optional: Close modal when clicking outside the box
  window.addEventListener('click', (e) => {
    if (e.target === modal) {
      modal.classList.add('hidden');
    }
  });*/

document.addEventListener('DOMContentLoaded', function() {
    const toggleBtn = document.getElementById('toggleMoreBtn');
    const moreContent = document.getElementById('moreContent');

    if(moreContent){
        if(toggleBtn){
            toggleBtn.addEventListener('click', function() {
                const isHidden = moreContent.classList.contains('hidden');

                if (isHidden) {
                    moreContent.classList.remove('hidden');
                    toggleBtn.textContent = 'See Less';
                } else {
                    moreContent.classList.add('hidden');
                    toggleBtn.textContent = 'See More';
                }
            });
        }


        // Optional: make sure it's always shown on large screens
        function handleResize() {
            if (window.innerWidth >= 1024) {
                moreContent.classList.remove('hidden');
            } else {
                moreContent.classList.add('hidden');
                toggleBtn.textContent = 'See More';
            }
        }

        window.addEventListener('resize', handleResize);
        handleResize(); // Initial check
    }
});

document.addEventListener('alpine:init', () => {
    Alpine.data('enrollmentHandler', () => ({
        isLoggedIn: <?= json_encode($isLoggedIn) ?>,
        handleEnrollClick() {
          
            if (!this.isLoggedIn) {
                const currentUrl = encodeURIComponent(window.location.href);
                window.location.href = '<?= $this->Url->build(['controller' => 'Login', 'action' => 'index']) ?>?redirect=' + '<?= $this->request->getRequestTarget() ?>';
            } else {
                 //window.location.href = '<?= $this->Url->build(['controller' => 'Bookings', 'action' => 'enrollment', $course->slug]) ?>';
                // Add your enrollment logic here
            }
        }
    }));
});

/* Testimonial carousel */
function testimonialCarousel() {
    return {
        current: 0,
        interval: null,
        itemsPerView: 1, // default to 1
        startX: 0,
        endX: 0,
        testimonials: [{
                quote: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore",
                name: "Esther Howard",
                role: "CEO, Acme Inc.",
                image: "/img/esther.png"
            },
            {
                quote: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore",
                name: "John Smith",
                role: "Freelancer",
                image: "/img/smith.png"
            },
            {
                quote: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore",
                name: "Joseph Cristopher",
                role: "Marketing Manager",
                image: "/img/cristopher.png"
            },
            {
                quote: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore",
                name: "Test Name",
                role: "Test Role",
                image: "/img/phool-chatti.png"
            },
        ],
        get totalSlides() {
            return Math.ceil(this.testimonials.length / this.itemsPerView);
        },
        updateItemsPerView() {
            this.itemsPerView = window.innerWidth < 640 ? 1 : 2;
        },
        prev() {
            this.current = (this.current === 0) ? this.testimonials.length - 1 : this.current - 1;
        },
        next() {
            if (this.testimonials.length === 0) {
                this.current = 0;
                return;
            }
         
            if (this.current < this.totalSlides - 0) {
                this.current++;
            }

            // this.current = (this.current + 1) % this.totalSlides;
        },
        handleTouchStart(e) {
            this.startX = e.touches[0].clientX;
        },
        handleTouchEnd(e) {
            this.endX = e.changedTouches[0].clientX;
            const deltaX = this.endX - this.startX;
            if (Math.abs(deltaX) > 50) { // minimum swipe threshold
                if (deltaX < 0) {
                    this.next(); // swiped left
                } else {
                    this.prev(); // swiped right
                }
            }
            if (window.innerWidth < 640) {
                this.current = Math.min(Math.max(this.current, 0), this.totalSlides - 1);
            } else {
                this.current = Math.min(Math.max(this.current, 0), this.totalSlides - 0);
            }
        },
        autoplay() {
            this.interval = setInterval(() => {
                this.next();
            }, 5000);
        },
        stopAutoplay() {
            clearInterval(this.interval);
        },
        init() {
            this.updateItemsPerView();
            window.addEventListener('resize', () => {
                this.updateItemsPerView();
                this.current = 0;
            });
            // this.autoplay();
        }
    };
}
document.addEventListener('alpine:init', () => {
    Alpine.data('batchViewer', () => ({
        showModal: false,
        batches: [],
        selectedBatchId: null,
        openBatchModal(batches) {
            this.batches = batches;
            // this.selectedBatchId = batches[0]?.id || null;
            this.showModal = true;
        },
        formatDate(dateStr) {
            const date = new Date(dateStr);
            return date.toLocaleDateString('en-GB', {
                day: '2-digit',
                month: 'short',
                year: 'numeric'
            });
        },
        formatTime(timeString) {
            if (!timeString) return '';
            const [hour, minute] = timeString.split(':');
            return `${hour.padStart(2, '0')}:${minute.padStart(2, '0')}`;
        }
    }));
});

let featuredSliderContainer = document.getElementById("featured-carousel");
let featuredSlider;
let featuredCards;
let featuredElementToShow = 4;
let featuredCardWidth;
// let autoSlideInterval1;

if (featuredSliderContainer) {
    featuredSlider = document.getElementById("featured-slider");
    featuredCards = document.querySelectorAll(".featured-center-carousel");

    if(document.body.clientWidth < 768){
        featuredElementToShow = 1;
    }
    else if(document.body.clientWidth < 992){
        featuredElementToShow = 2;
    } 

    let featuredSliderContainerWidth = featuredSliderContainer.clientWidth;
    featuredCardWidth = featuredSliderContainerWidth / featuredElementToShow;
    featuredSlider.style.width = featuredCards.length * featuredCardWidth + "px";
    featuredSlider.style.transition = "margin";
    featuredSlider.style.transitionDuration = "1s";
    
    for (let i = 0; i < featuredCards.length; i++) {
        featuredCards[i].style.width = featuredCardWidth + "px";
    }

    let currentIndex = 0;

    const maxIndex = featuredCards.length - featuredElementToShow;
    console.log(featuredCards.length, featuredElementToShow);
    // function featuredAutoSlide() {
    //     autoSlideInterval1 = setInterval(() => {
    //         currentIndex = (currentIndex >= maxIndex) ? 0 : currentIndex + 1;
    //         featuredSlider.style.marginLeft = "-" + (featuredCardWidth * currentIndex) + "px";
    //     }, 3000);
    // }

    function featuredAutoSlide() {
        //autoSlideInterval1 = setInterval(() => {
            if (currentIndex >= maxIndex) {
                // Instantly reset without animation
                featuredSlider.style.transition = 'none';
                currentIndex = 0;
                featuredSlider.style.marginLeft = "0px";

                // Force reflow to apply transition again
                void featuredSlider.offsetWidth;

                // Re-enable transition
                featuredSlider.style.transition = 'margin-left 0.5s ease';
            } else {
                currentIndex++;
                featuredSlider.style.transition = 'margin-left 0.5s ease';
                featuredSlider.style.marginLeft = "-" + (featuredCardWidth * currentIndex) + "px";
            }
        //}, 3000);
    }
    featuredAutoSlide();
}

function featuredPrev() {
    if (+featuredSlider.style.marginLeft.slice(0, -2) !== 0) {
        featuredSlider.style.marginLeft = ((+featuredSlider.style.marginLeft.slice(0, -2)) + featuredCardWidth) + 'px';
    }
}

function featuredNext() {
    if (+featuredSlider.style.marginLeft.slice(0, -2) !== -featuredCardWidth * (featuredCards.length - featuredElementToShow)) {
        featuredSlider.style.marginLeft = ((+featuredSlider.style.marginLeft.slice(0, -2)) - featuredCardWidth) + 'px';
    }
}

</script>