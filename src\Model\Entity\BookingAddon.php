<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * BookingAddon Entity
 *
 * @property int $id
 * @property int $booking_id
 * @property string $addon_title
 * @property string $addon_price
 * @property int $quantity
 * @property string|null $currency
 * @property string|null $total_price
 * @property \Cake\I18n\DateTime $created_at
 * @property \Cake\I18n\DateTime $modified_at
 *
 * @property \App\Model\Entity\Booking $booking
 */
class BookingAddon extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'booking_id' => true,
        'addon_title' => true,
        'addon_price' => true,
        'quantity' => true,
        'currency' => true,
        'total_price' => true,
        'created_at' => true,
        'modified_at' => true,
        'booking' => true,
    ];

    /**
     * Calculate total price based on addon price and quantity
     *
     * @return string
     */
    protected function _getTotalPrice(): string
    {
        if (isset($this->_fields['total_price']) && !empty($this->_fields['total_price'])) {
            return $this->_fields['total_price'];
        }

        if (isset($this->addon_price) && isset($this->quantity)) {
            return (string)((float)$this->addon_price * (int)$this->quantity);
        }

        return '0.00';
    }
}