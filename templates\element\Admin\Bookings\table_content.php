<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Booking> $bookings
 * @var array $pagination
 */
?>

<div class="table-responsive">
    <table class="table table-striped table-hover" id="bookings-table" style="width:100%;">
        <thead>
            <tr>
                <th class="no-sort">Actions</th>
                <th>Booking ID</th>
                <th>Customer</th>
                <!-- <th>Course</th>
                <th>Partner</th> -->
                <th class="no-sort">Participants</th>
                <th>Booking Date</th>
                <th>Total Amount</th>
                <th>Status</th>
                <th>Payment</th>
                <th>Created</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($bookings as $booking): ?>
                <tr>
                    <td class="action-buttons">
                        <a href="<?= $this->Url->build(['action' => 'view', $booking->id]) ?>"
                           class="btn btn-sm btn-primary" title="View Details">
                            <i class="fas fa-eye"></i>
                        </a>
                        <div class="btn-group">
                            <!-- <button type="button" class="btn btn-sm btn-secondary dropdown-toggle" 
                                    data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-cog"></i>
                            </button> -->
                            <!-- <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="<?= $this->Url->build(['action' => 'invoice', $booking->id]) ?>">
                                        <i class="fas fa-file-invoice"></i> Invoice
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="post" action="<?= $this->Url->build(['action' => 'updateStatus', $booking->id]) ?>" style="display: inline;">
                                        <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]) ?>
                                        <?= $this->Form->hidden('status', ['value' => 'confirmed']) ?>
                                        <button type="submit" class="dropdown-item text-success">
                                            <i class="fas fa-check"></i> Confirm
                                        </button>
                                    </form>
                                </li>
                                <li>
                                    <form method="post" action="<?= $this->Url->build(['action' => 'updateStatus', $booking->id]) ?>" style="display: inline;">
                                        <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]) ?>
                                        <?= $this->Form->hidden('status', ['value' => 'cancelled']) ?>
                                        <button type="submit" class="dropdown-item text-danger">
                                            <i class="fas fa-times"></i> Cancel
                                        </button>
                                    </form>
                                </li>
                            </ul> -->
                        </div>
                    </td>
                    <td>
                        <strong><?= h($booking->id) ?></strong>
                    </td>
                    <td>
                        <?php if ($booking->customer && $booking->customer->user): ?>
                            <div class="customer-info">
                                <strong><?= h($booking->customer->user->first_name . ' ' . $booking->customer->user->last_name) ?></strong>
                                <br>
                                <small class="text-muted"><?= h($booking->customer->user->email) ?></small>
                            </div>
                        <?php else: ?>
                            <span class="text-muted">N/A</span>
                        <?php endif; ?>
                    </td>
                    <!-- <td>
                        <?php if ($booking->course): ?>
                            <div class="course-info">
                                <strong><?= h($booking->course->title) ?></strong>
                                <?php if ($booking->course->course_type): ?>
                                    <br><small class="text-muted"><?= h($booking->course->course_type->name) ?></small>
                                <?php endif; ?>
                            </div>
                        <?php else: ?>
                            <span class="text-muted">N/A</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($booking->partner): ?>
                            <div class="partner-info">
                                <strong><?= h($booking->partner->business_name) ?></strong>
                            </div>
                        <?php else: ?>
                            <span class="text-muted">N/A</span>
                        <?php endif; ?>
                    </td> -->
                    <td>
                        <span class="badge bg-info">
                            <?= count($booking->booking_items) ?> Participants
                        </span>
                        <?php if (count($booking->booking_items) > 0): ?>
                            <br>
                            <small class="text-muted">
                                <?php 
                                $firstParticipant = $booking->booking_items[0];
                                echo h($firstParticipant->first_name . ' ' . $firstParticipant->last_name);
                                if (count($booking->booking_items) > 1): 
                                    echo ' +' . (count($booking->booking_items) - 1) . ' more';
                                endif;
                                ?>
                            </small>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?= h($booking->booking_date->format('d M Y')) ?>
                    </td>
                    <td>
                        <strong><?= h($booking->billing_currency ?? 'INR') ?> <?= number_format($booking->grand_total ?? 0, 2) ?></strong>
                        <?php if ($booking->sub_total != $booking->grand_total): ?>
                            <br><small class="text-muted">Base: <?= number_format($booking->sub_total ?? 0, 2) ?></small>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php
                        $statusClass = match($booking->booking_status) {
                            'confirmed' => 'status-confirmed',
                            'pending' => 'status-pending',
                            'cancelled' => 'status-cancelled',
                            default => 'status-pending'
                        };
                        ?>
                        <span class="booking-status <?= $statusClass ?>">
                            <?= h(ucfirst($booking->booking_status)) ?>
                        </span>
                    </td>
                    <td>
                        <?php
                        $paymentClass = match($booking->payment_status) {
                            'paid' => 'payment-paid',
                            'pending' => 'payment-pending',
                            'cancelled' => 'payment-cancelled',
                            'refunded' => 'payment-refunded',
                            default => 'payment-pending'
                        };
                        ?>
                        <span class="payment-status <?= $paymentClass ?>">
                            <?= h(ucfirst($booking->payment_status ?? 'pending')) ?>
                        </span>
                    </td>
                    <td>
                        <?= h($booking->created_at->format('d M Y H:i')) ?>
                    </td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>

<!-- Pagination Component -->
<?= $this->element('Admin/Bookings/search_pagination', [
    'pagination' => $pagination ?? [],
    'search' => '',
    'showSearch' => false,
    'showFilters' => false,
    'searchInputId' => 'tableSearchInput',
    'loadDataFunction' => 'loadData'
]) ?>

<style>
.payment-paid { background: #d4edda; color: #155724; padding: 4px 10px; border-radius: 15px; font-size: 0.75rem; font-weight: 500; }
.payment-pending { background: #fff3cd; color: #856404; padding: 4px 10px; border-radius: 15px; font-size: 0.75rem; font-weight: 500; }
.payment-cancelled { background: #f8d7da; color: #721c24; padding: 4px 10px; border-radius: 15px; font-size: 0.75rem; font-weight: 500; }
.payment-refunded { background: #d1ecf1; color: #0c5460; padding: 4px 10px; border-radius: 15px; font-size: 0.75rem; font-weight: 500; }

/* DataTable Custom Styles */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_processing,
.dataTables_wrapper .dataTables_paginate {
    display: none !important;
}

.dataTables_wrapper .dataTables_scroll {
    overflow: visible;
}

table.dataTable thead th {
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

table.dataTable thead th.sorting,
table.dataTable thead th.sorting_asc,
table.dataTable thead th.sorting_desc {
    cursor: pointer;
    position: relative;
}

table.dataTable thead th.sorting:after,
table.dataTable thead th.sorting_asc:after,
table.dataTable thead th.sorting_desc:after {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    font-size: 12px;
    color: #6c757d;
}

table.dataTable thead th.sorting:after {
    content: "\f0dc";
}

table.dataTable thead th.sorting_asc:after {
    content: "\f0de";
    color: #667eea;
}

table.dataTable thead th.sorting_desc:after {
    content: "\f0dd";
    color: #667eea;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTable with sorting only
    if ($.fn.DataTable && $('#bookings-table').length) {
        $('#bookings-table').DataTable({
            "paging": false,
            "searching": false,
            "info": false,
            "ordering": true,
            "autoWidth": false,
            "responsive": true,
            "columnDefs": [
                {
                    "orderable": false,
                    "targets": [0, 3] // Actions and Participants columns
                },
                {
                    "type": "num",
                    "targets": [1, 4] // Booking ID and Total Amount
                },
                {
                    "type": "date",
                    "targets": [5, 8] // Booking Date and Created
                }
            ],
            "order": [[1, "desc"]], // Default sort by Booking ID descending
            "language": {
                "emptyTable": "No bookings found"
            }
        });
    }
});
</script>
