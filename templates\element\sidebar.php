<div class="main-sidebar sidebar-style-2">
    <aside id="sidebar-wrapper">

        <div class="sidebar-user">
            <div class="sidebar-user-picture">
                <img alt="image" src="<?php echo $this->Url->webroot('img/yoga-big.png') ?>">
            </div>

            <div class="sidebar-user-details">
                <?php if (isset($userdata)): ?>
                    <div class="user-name"><?= h($userdata->first_name . ' ' . $userdata->last_name) ?></div>
                    <?php if (isset($userdata->role)): ?>
                        <div class="user-role"><?= h($userdata->role->name) ?></div>
                    <?php endif; ?>
                <?php endif; ?>
                <div class="sidebar-userpic-btn">
                    <a href="<?= $this->Url->build(['controller' => 'Users', 'action' => 'profile']) ?>" data-bs-toggle="tooltip" title="Profile">
                        <i data-feather="user"></i>
                    </a>
                    <a href="<?= $this->Url->build(['controller' => 'Users', 'action' => 'logout']) ?>"
                        data-bs-toggle="tooltip" title="Log Out">
                        <i data-feather="log-out"></i>
                    </a>
                </div>
            </div>
        </div>
        <?php $controller = $this->request->getParam('controller'); ?>
        <ul class="sidebar-menu">
            <li class="menu-header">Main</li>
            <?php
            $controller = $this->request->getParam('controller');
            $action = $this->request->getParam('action');
            $isUsers = ($controller === 'Users');
            $isRoles = ($controller === 'Roles');
            $isYogaCentres = ($controller === 'YogaCentres');
            $isCourses = ($controller === 'Courses');
            $isCourseTypes = ($controller === 'CourseTypes');
            $isPartners = ($controller === 'Partners');
            $isPartnerRequests = ($controller === 'PartnerRequests');
            $isCustomers = ($controller === 'Customers');
            // Combined check for any partner-related pages
            $isPartnersSection = ($isPartners || $isPartnerRequests);
            // Destinations section
            $isDestinations = ($controller === 'Destinations');
            ?>

            <!-- Dashboard Menu Item -->
            <li class="<?= $this->request->getParam('controller') === 'Dashboard' ? 'active' : '' ?>">
                <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Dashboard', 'action' => 'index']) ?>">
                    <i data-feather="home"></i><span>Dashboard</span>
                </a>
            </li>

            <!-- User -->
            <li class="dropdown <?= $isUsers ? 'active menu-open' : '' ?>">
                <a href="#" class="menu-toggle nav-link has-dropdown <?= $isUsers ? 'toggled' : '' ?>">
                    <i data-feather="user-check"></i><span>Users</span>
                </a>
                <ul class="dropdown-menu" style="<?= $isUsers ? 'display: block;' : '' ?>">
                    <li class="<?= ($isUsers && $action === 'index') ? 'active' : '' ?>">
                        <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Users', 'action' => 'index']) ?>">Users</a>
                    </li>
                </ul>
                <!-- <ul class="dropdown-menu" style="<?= $isRoles ? 'display: block;' : '' ?>">
                    <li class="<?= ($isRoles && $action === 'index') ? 'active' : '' ?>">
                        <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Roles', 'action' => 'index']) ?>">Roles</a>
                    </li>
                </ul> -->
            </li>

            <!-- Customers -->
            <li class="<?= ($controller === 'Customers') ? 'active' : '' ?>">
                <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Customers', 'action' => 'index']) ?>">
                    <i data-feather="users"></i><span>Customers</span>
                </a>
            </li>

            <!-- Destinations -->
            <li class="<?= $isDestinations ? 'active' : '' ?>">
                <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Destinations', 'action' => 'index']) ?>">
                    <i data-feather="map-pin"></i><span>Destinations</span>
                </a>
            </li>

            <li class="dropdown <?= $isCourses ? 'active menu-open' : '' ?>">
                <a href="#" class="menu-toggle nav-link has-dropdown" <?= $isCourses ? 'toggled' : '' ?>><i
                        data-feather="layers"></i><span>Partner Offerings</span></a>
                <!-- <ul class="dropdown-menu" style="<?= $isCourses ? 'display: block;' : '' ?>">
                    <li class="<?= ($isCourseTypes) ? 'active' : '' ?>"><a class="nav-link" href="<?= $this->Url->build(['controller' => 'CourseTypes', 'action' => 'index']) ?>">Courses Type</a></li>

                </ul> -->
                <ul class="dropdown-menu" style="<?= $isCourses ? 'display: block;' : '' ?>">
                    <li class="<?= ($isCourses) ? 'active' : '' ?>"><a class="nav-link" href="<?= $this->Url->build(['controller' => 'Courses', 'action' => 'index']) ?>">Partner Offerings</a></li>
                </ul>
            </li>
            <li class="dropdown <?= $isPartnersSection ? 'active menu-open' : '' ?>">
                <a href="#" class="menu-toggle nav-link has-dropdown <?= $isPartnersSection ? 'toggled' : '' ?>">
                    <i data-feather="users"></i>
                    <span>Partners</span>
                </a>
                <ul class="dropdown-menu" style="<?= $isPartnersSection ? 'display: block;' : '' ?>">
                    <li class="<?= ($isPartnerRequests) ? 'active' : '' ?>">
                        <a class="nav-link" href="<?= $this->Url->build(['controller' => 'PartnerRequests', 'action' => 'index']) ?>">
                            Partner Requests
                        </a>
                    </li>
                    <li class="<?= ($isPartners) ? 'active' : '' ?>">
                        <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Partners', 'action' => 'index']) ?>">
                            Partners
                        </a>
                    </li>
                </ul>
            </li>
            <li class="dropdown active menu-open">
                <a href="#" class="menu-toggle nav-link has-dropdown toggled">
                    <i data-feather="users"></i>
                    <span>SEO</span>
                </a>
                <ul class="dropdown-menu">
                    <li >
                        <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Robots', 'action' => 'edit']) ?>">
                        Robots.txt
                        </a>
                    </li>
                   
                </ul>
            </li>
            <!-- <li class="dropdown <?= $isYogaCentres ? 'active menu-open' : '' ?>">
                <a href="#" class="menu-toggle nav-link has-dropdown <?= $isYogaCentres ? 'toggled' : '' ?>">
                    <i data-feather="home"></i><span>Yoga Centres</span>
                </a>
                <ul class="dropdown-menu" style="<?= $isYogaCentres ? 'display: block;' : '' ?>">
                    <li class="<?= ($isYogaCentres && $action === 'index') ? 'active' : '' ?>">
                        <a class="nav-link" href="<?= $this->Url->build(['controller' => 'YogaCentres', 'action' => 'index']) ?>">Yoga Centres</a>
                    </li>
                </ul>
            </li> -->

            <!-- Marketing -->
            <?php
            $isCoupons = ($controller === 'Coupons');
            $isReviews = ($controller === 'Reviews');
            $isMarketingSection = ($isCoupons || $isReviews);
            ?>
            <li class="dropdown <?= $isMarketingSection ? 'active menu-open' : '' ?>">
                <a href="#" class="menu-toggle nav-link has-dropdown <?= $isMarketingSection ? 'toggled' : '' ?>">
                    <i data-feather="tag"></i><span>Marketing</span>
                </a>
                <ul class="dropdown-menu" style="<?= $isMarketingSection ? 'display: block;' : '' ?>">
                    <li class="<?= $isCoupons ? 'active' : '' ?>">
                        <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Coupons', 'action' => 'index']) ?>">
                            Coupons
                        </a>
                    </li>
                    <li class="<?= $isReviews ? 'active' : '' ?>">
                        <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Reviews', 'action' => 'index']) ?>">
                            Reviews
                        </a>
                    </li>
                </ul>
            </li>

            <!-- Master Data -->
            <li class="dropdown">
                <a href="#" class="menu-toggle nav-link has-dropdown">
                    <i data-feather="database"></i><span>Master Data</span>
                </a>
                <ul class="dropdown-menu">
                    <li>
                        <a class="nav-link" href="<?= $this->Url->build(['controller' => 'MasterData', 'action' => 'index']) ?>">List</a>
                    </li>
                </ul>
            </li>

        </ul>
    </aside>
</div>