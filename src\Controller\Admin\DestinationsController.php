<?php
declare(strict_types=1);

namespace App\Controller\Admin;
use Cake\Core\Configure;
use Cake\Routing\Router;
use Laminas\Diactoros\UploadedFile;

class DestinationsController extends AppController
{
    protected $paginationCount;
    protected $Countries;
    protected $States;
    protected $Cities;
    protected $Destinations;
    protected $MasterData;
    protected $Regions;
    protected $Localities;
    public function initialize(): void
    {
        parent::initialize();
       
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->loadComponent('CustomPaginator');
        
        $this->paginationCount = $this->viewBuilder()->getVar('paginationCount');

        $this->Countries = $this->fetchTable('Countries');
        $this->States = $this->fetchTable('States');
        $this->Cities = $this->fetchTable('Cities');
        $this->Destinations = $this->fetchTable('Destinations');
        $this->MasterData = $this->fetchTable('MasterData');
        $this->Regions = $this->fetchTable('Regions');
        $this->Localities = $this->fetchTable('Localities');
    }
    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
    }
    public function index()
    {

        $query = $this->Destinations->find()->contain([
            'Countries',
            'States',
            'Cities',
            'Localities'
        ]);
        $query->where(['Destinations.status !=' => 'D']);
        $query->orderBy(['Destinations.id' => 'DESC']);
        $destinations = $query->toArray();
        $this->set(compact('destinations'));
    }

    public function filterSearch()
    {
        $this->request->allowMethod(['get']);
        $status = $this->request->getQuery('filterStatus');
        $country = $this->request->getQuery('filterCountry');
        $state = $this->request->getQuery('filterState');
        
        if ($this->request->is('ajax')) {
            $query = $this->Destinations->find()
                ->contain([
                    'Countries',
                    'States',
                    'Localities'
                ])
                ->orderBy(['Destinations.id' => 'DESC']);

            if ($status) {
                $query->where(['Destinations.status' => $status]);
            } else {
                $query->where(['Destinations.status !=' => 'D']);
            }

            if ($country) {
                $query->where(['Destinations.country_id' => $country]);
            }

            if ($state) {
                $query->where(['Destinations.state_id' => $state]);
            }

            $statusMap = [
                'A' => ['label' => __('Active'), 'class' => 'badge-outline col-green'],
                'I' => ['label' => __('Inactive'), 'class' => 'badge-outline col-red'],
                'D' => ['label' => __('Deleted'), 'class' => 'badge-outline col-red']
            ];

            $destinationData = [];
            $i = 1;
            foreach ($query as $destination) {
                $status = $statusMap[$destination->status] ?? ['label' => __('Unknown'), 'class' => 'badge-outline col-red'];
                $statusToggleUrl = Router::url(['controller' => 'Destinations', 'action' => 'approve', $destination->id], true);
                $approveClass = ' btn-sm approve approve ms-2 ';
                $approveTitle = $destination->status === 'A' ? 'Mark as Inactive' : 'Mark as Active';

                $destinationData[] = [
                    'actions' => '<a href="' . Router::url(['controller' => 'Destinations', 'action' => 'view', $destination->id], true) . '" class="btn-view" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a> ' .
                                ($destination->status !== 'D' ? 
                                '<a href="javascript:void(0);" class="delete-button" data-id="' . $destination->id . '" data-toggle="tooltip" title="Delete" data-bs-toggle="modal" data-bs-target="#exampleModalCenter"> <i class="far fa-trash-alt"></i></a> ' 
                                : '') .
                                '<a href="' . Router::url(['controller' => 'Destinations', 'action' => 'edit', $destination->id], true) . '" class="btn-edit" data-toggle="tooltip" title="Edit"><i class="fas fa-edit"></i></a>',
                    'id' => $destination->id,
                    'name' => !empty($destination->name) ? h($destination->name) : 'None',
                    'country' => !empty($destination->country->name) ? h($destination->country->name) : 'None',
                    'state' => !empty($destination->state->name) ? h($destination->state->name) : 'None',
                    'type' => !empty($destination->type) ? h(ucfirst($destination->type)) : 'City',
                    'status' => '<div class="badge-outline ' . h($status['class']) . '">' . h($status['label']) . '</div>',
                    'created_at' => !empty($destination->created_at) ? $destination->created_at->format('Y-m-d H:i:s') : 'None',
                ];
                $i++;
            }

            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['data' => $destinationData]));
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode(['data' => []]));
    }

    /**
     * DataTables server-side processing endpoint
     */
    private function getDataTablesData()
    {
        $this->autoRender = false;
        
        try {
            // Debug logging
            error_log('DataTables processing started');
            
            // Get DataTables parameters
            $draw = (int)$this->request->getQuery('draw');
            $start = (int)$this->request->getQuery('start', 0);
            $length = (int)$this->request->getQuery('length', 25);
            $searchValue = trim($this->request->getQuery('search')['value'] ?? '');
            $orderColumnIndex = (int)($this->request->getQuery('order')[0]['column'] ?? 1);
            $orderDirection = $this->request->getQuery('order')[0]['dir'] ?? 'desc';
            
            error_log("DataTables params - draw: $draw, start: $start, length: $length");
            
            // Custom filters
            $statusFilter = $this->request->getQuery('statusFilter', '');
            $countryFilter = $this->request->getQuery('countryFilter', '');
            $stateFilter = $this->request->getQuery('stateFilter', '');
            
            // Column mapping for ordering
            $columns = [
                0 => null, // Actions column (not sortable)
                1 => 'Destinations.id',
                2 => 'Destinations.name',
                3 => 'Countries.name',
                4 => 'States.name',
                5 => 'Destinations.type',
                6 => 'Destinations.status',
                7 => 'Destinations.created_at'
            ];
            
            // Build base query
            $query = $this->Destinations->find()
                ->contain(['Countries', 'States', 'Localities'])
                ->where(['Destinations.status !=' => 'D']); // Exclude deleted by default unless specifically requested
            
            // Apply global search filter
            if (!empty($searchValue)) {
                $query->where([
                    'OR' => [
                        'Destinations.name LIKE' => '%' . $searchValue . '%',
                        'Destinations.slug LIKE' => '%' . $searchValue . '%',
                        'Countries.name LIKE' => '%' . $searchValue . '%',
                        'States.name LIKE' => '%' . $searchValue . '%',
                        'Destinations.type LIKE' => '%' . $searchValue . '%'
                    ]
                ]);
            }
            
            // Apply custom filters
            if (!empty($statusFilter)) {
                $query->where(['Destinations.status' => $statusFilter]);
            }
            
            if (!empty($countryFilter)) {
                $query->where(['Destinations.country_id' => $countryFilter]);
            }
            
            if (!empty($stateFilter)) {
                $query->where(['Destinations.state_id' => $stateFilter]);
            }
            
            // Count total records without filters (for recordsTotal)
            $totalQuery = $this->Destinations->find()->where(['status !=' => 'D']);
            $totalRecords = $totalQuery->count();
            
            // Count filtered records (for recordsFiltered)
            $filteredRecords = $query->count();
            
            // Apply ordering
            if (isset($columns[$orderColumnIndex]) && $columns[$orderColumnIndex]) {
                $query->order([$columns[$orderColumnIndex] => $orderDirection]);
            } else {
                $query->order(['Destinations.id' => 'DESC']); // Default order
            }
            
            // Apply pagination
            $query->limit($length)->offset($start);
            
            // Get the data
            $destinations = $query->all();
            
            // Format data for DataTables
            $data = [];
            foreach ($destinations as $destination) {
                $data[] = $this->formatDestinationRow($destination);
            }
            
            // Return JSON response
            $response = [
                'draw' => $draw,
                'recordsTotal' => $totalRecords,
                'recordsFiltered' => $filteredRecords,
                'data' => $data
            ];
            
            error_log('DataTables response: ' . json_encode([
                'draw' => $draw,
                'recordsTotal' => $totalRecords,
                'recordsFiltered' => $filteredRecords,
                'dataCount' => count($data)
            ]));
            
            $this->response = $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
            return $this->response;
            
        } catch (\Exception $e) {
            // Log the detailed error
            error_log('DataTables error: ' . $e->getMessage());
            error_log('DataTables error trace: ' . $e->getTraceAsString());
            
            // Return error response for DataTables
            $errorResponse = [
                'draw' => (int)$this->request->getQuery('draw', 0),
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                'data' => [],
                'error' => $e->getMessage()
            ];
            
            $this->response = $this->response->withType('application/json')
                ->withStringBody(json_encode($errorResponse));
            return $this->response;
        }
    }

    /**
     * Format destination row for DataTables
     */
    private function formatDestinationRow($destination)
    {
        // Status mapping
        $statusMap = [
            'A' => ['label' => 'Active', 'class' => 'success'],
            'I' => ['label' => 'Inactive', 'class' => 'warning'],
            'D' => ['label' => 'Deleted', 'class' => 'danger']
        ];
        $statusInfo = $statusMap[$destination->status] ?? ['label' => 'Unknown', 'class' => 'secondary'];
        
        // Format actions column using Router::url() instead of $this->Url->build()
        $baseUrl = $this->request->getAttribute('webroot');
        $actions = '<div class="btn-group" role="group">';
        $actions .= '<a href="' . $baseUrl . 'admin/destinations/view/' . $destination->id . '" class="btn btn-sm btn-outline-info" data-bs-toggle="tooltip" title="View"><i class="far fa-eye"></i></a>';
        $actions .= '<a href="' . $baseUrl . 'admin/destinations/edit/' . $destination->id . '" class="btn btn-sm btn-outline-primary" data-bs-toggle="tooltip" title="Edit"><i class="fas fa-edit"></i></a>';
        $actions .= '<button type="button" class="btn btn-sm btn-outline-danger delete-btn" data-id="' . $destination->id . '" data-bs-toggle="tooltip" title="Delete"><i class="far fa-trash-alt"></i></button>';
        $actions .= '</div>';
        
        // Format name with tooltip
        $name = !empty($destination->name) ? h($destination->name) : 'N/A';
        $nameDisplay = strlen($name) > 40 ? '<span data-bs-toggle="tooltip" title="' . $name . '">' . substr($name, 0, 40) . '...</span>' : $name;
        
        // Safely get country name
        $countryName = 'N/A';
        if (!empty($destination->country_id) && !empty($destination->country)) {
            $countryName = h($destination->country->name);
        }
        
        // Safely get state name
        $stateName = 'N/A';
        if (!empty($destination->state_id) && !empty($destination->state)) {
            $stateName = h($destination->state->name);
        }
        
        // Format status badge
        $statusBadge = '<span class="badge bg-' . $statusInfo['class'] . '">' . $statusInfo['label'] . '</span>';
        
        return [
            $actions,
            $destination->id,
            $nameDisplay,
            $countryName,
            $stateName,
            h(ucfirst($destination->type ?? 'city')),
            $statusBadge
        ];
    }

    /**
     * Get countries for filter dropdown with composite key filtering (AJAX)
     */
    public function getCountries()
    {
        $this->request->allowMethod(['get', 'ajax']);
        $this->autoRender = false;

        $type = $this->request->getQuery('type');
        $editId = $this->request->getQuery('edit_id'); // For edit mode, exclude current record
        
        $countries = [];

        if ($type) {
            // Get all countries first
            $allCountries = $this->Countries->find('list', [
                'keyField' => 'id',
                'valueField' => 'name'
            ])->order(['name' => 'ASC'])->toArray();

            // Apply composite key logic based on type
            if ($type === 'country') {
                // For 'country' type, exclude countries that already have a country-type destination
                $usedCountryQuery = $this->Destinations->find()
                    ->select(['country_id'])
                    ->where([
                        'type' => $type,
                        'status !=' => 'D', // Exclude deleted records
                        'country_id IS NOT' => null
                    ]);

                // If editing, exclude current record
                if ($editId) {
                    $usedCountryQuery->where(['id !=' => $editId]);
                }

                $usedCountryResults = $usedCountryQuery->toArray();
                $usedCountryIds = array_column($usedCountryResults, 'country_id');
                
                // Filter out used countries for country type
                foreach ($allCountries as $countryId => $countryName) {
                    if (!in_array($countryId, $usedCountryIds)) {
                        $countries[$countryId] = $countryName;
                    }
                }
            } else {
                // For other types (state, city, locality), show all countries
                // The filtering will happen at the next level (state/locality)
                $countries = $allCountries;
            }
        } else {
            // If no type specified, return all countries (backward compatibility)
            $countries = $this->Countries->find('list', [
                'keyField' => 'id',
                'valueField' => 'name'
            ])->order(['name' => 'ASC'])->toArray();
        }

        $this->response = $this->response->withType('application/json')
            ->withStringBody(json_encode($countries));
        return $this->response;
    }

    /**
     * Get states by country for filter dropdown with composite key filtering (AJAX)
     */
    public function getStatesByCountryFilter($countryId = null)
    {
        $this->request->allowMethod(['get', 'ajax']);
        $this->autoRender = false;
        
        $type = $this->request->getQuery('type');
        $editId = $this->request->getQuery('edit_id'); // For edit mode, exclude current record
        
        $states = [];
        
        if ($countryId) {
            if ($type) {
                // Get all states for the country first
                $allStates = $this->States->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['country_id' => $countryId])->order(['name' => 'ASC'])->toArray();

                // Apply composite key logic based on type
                if ($type === 'state') {
                    // For 'state' type, exclude states that already have a state-type destination
                    $usedStateQuery = $this->Destinations->find()
                        ->select(['state_id'])
                        ->where([
                            'type' => $type,
                            'country_id' => $countryId,
                            'status !=' => 'D', // Exclude deleted records
                            'state_id IS NOT' => null
                        ]);

                    // If editing, exclude current record
                    if ($editId) {
                        $usedStateQuery->where(['id !=' => $editId]);
                    }

                    // Debug logging for Filter method
                    error_log('getStatesByCountryFilter Debug - Type: ' . $type . ', Country ID: ' . $countryId . ', Edit ID: ' . ($editId ?: 'null'));
                    
                    $usedStateResults = $usedStateQuery->toArray();
                    $usedStateIds = array_column($usedStateResults, 'state_id');
                    
                    error_log('Filter Used States Results: ' . json_encode($usedStateResults));
                    error_log('Filter Used State IDs: ' . json_encode($usedStateIds));
                    
                    // Filter out used states for state type
                    foreach ($allStates as $stateId => $stateName) {
                        if (!in_array($stateId, $usedStateIds)) {
                            $states[$stateId] = $stateName;
                        }
                    }
                    
                    error_log('Filter Final Filtered States: ' . json_encode(array_keys($states)));
                } else {
                    // For other types (region, city, locality), show all states for the country
                    // The filtering will happen at the next level (locality)
                    $states = $allStates;
                }
            } else {
                // If no type specified, return all states for the country (backward compatibility)
                $states = $this->States->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['country_id' => $countryId])->order(['name' => 'ASC'])->toArray();
            }
        }
        
        $this->response = $this->response->withType('application/json')
            ->withStringBody(json_encode($states));
        return $this->response;
    }

   

    public function add()
    {
          
        $features = Configure::read('Constants.Features');
        $destination = $this->Destinations->newEmptyEntity();
        $masterDataLocationTypeList = $this->MasterData->getMasterDataByType('location_type');
      
        // Fetch all countries for the dropdown
        $countries = $this->Countries->find('list', ['keyField' => 'id', 'valueField' => 'name'])->toArray();

        // Default empty arrays for dependent dropdowns
        $regions = [];
        $states = [];
        $localities = [];

        // If form is submitted, get selected values and filter dependent dropdowns
        if ($this->request->is(['post', 'put', 'patch'])) {
            $data = $this->request->getData();

            // Handle features data - convert to JSON format for information field
            if (isset($data['information']) && is_array($data['information'])) {
                // Filter out empty values and encode as JSON
                $informationData = array_filter($data['information'], function($value) {
                    return !empty(trim($value));
                });
                $data['information'] = !empty($informationData) ? json_encode($informationData) : null;
            } else {
                $data['information'] = null;
            }

            // Handle banner image upload
            $bannerPath = WWW_ROOT . 'uploads/';
            if (!file_exists($bannerPath)) {
                mkdir($bannerPath, 0775, true);
            }

            if (!empty($data['banner_image']) && $data['banner_image'] instanceof UploadedFile && $data['banner_image']->getClientFilename()) {
                $bannerName = time() . '_' . preg_replace('/[^a-zA-Z0-9._-]/', '', $data['banner_image']->getClientFilename());
                $data['banner_image']->moveTo($bannerPath . $bannerName);
                $data['banner'] = $bannerName;
            }
            unset($data['banner_image']); // Remove the uploaded file object from data

            // Handle thumb image upload
            if (!empty($data['thumb_image']) && $data['thumb_image'] instanceof UploadedFile && $data['thumb_image']->getClientFilename()) {
                $thumbName = time() . '_thumb_' . preg_replace('/[^a-zA-Z0-9._-]/', '', $data['thumb_image']->getClientFilename());
                $data['thumb_image']->moveTo($bannerPath . $thumbName);
                $data['thumb'] = $thumbName;
            }
            unset($data['thumb_image']); // Remove the uploaded file object from data

            // Filter dependent dropdowns based on selections
            if (!empty($data['country_id'])) {
                $regions = $this->Regions->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['country_id' => $data['country_id']])->toArray();

                $states = $this->States->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['country_id' => $data['country_id']])->toArray();
            }

            if (!empty($data['state_id'])) {
                $localities = $this->Localities->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['city_id' => $data['state_id']])->toArray();
            }

            // Handle is_feature checkbox properly
            // With hiddenField => true, we get the actual value (0 or 1)
            $data['is_feature'] = !empty($data['is_feature']) && $data['is_feature'] == 1 ? 1 : 0;

            $destination = $this->Destinations->patchEntity($destination, $data);
            if ($this->Destinations->save($destination)) {
                $this->Flash->success(__('The destination has been saved.'));
                 return $this->response->withType('application/json')
                ->withStringBody(json_encode([
                    'success' => true,
                    'redirect' => 'admin/destinations',
                ]));
            }
            $this->Flash->error(__('The destination could not be saved. Please, try again.'));
        }

        $this->set(compact('masterDataLocationTypeList','features', 'destination', 'countries', 'regions', 'states', 'localities'));
    }

    public function view($id = null)
    {
          $features = Configure::read('Constants.Features');
        $destination = $this->Destinations
            ->get($id, [
                'contain' => ['States', 'Countries', 'Localities']
            ]);

        // Fetch all countries for the dropdown
        $countries = $this->Countries->find('list', ['keyField' => 'id', 'valueField' => 'name'])->toArray();

        // Get states based on current destination data
        $regions = [];
        $states = [];
        $localities = [];
        
        if (!empty($destination->country_id)) {
            $regions = $this->Regions->find('list', [
                'keyField' => 'id',
                'valueField' => 'name'
            ])->where(['country_id' => $destination->country_id])->toArray();

            $states = $this->States->find('list', [
                'keyField' => 'id',
                'valueField' => 'name'
            ])->where(['country_id' => $destination->country_id])->toArray();
        }

        if (!empty($destination->state_id)) {
            $localities = $this->Localities->find('list', [
                'keyField' => 'id',
                'valueField' => 'name'
            ])->where(['city_id' => $destination->state_id])->toArray();
        }

        // Location Type List (for select)
        $masterDataLocationTypeList = $this->MasterData->getMasterDataByType('location_type');

        if ($this->request->is(['patch', 'post', 'put'])) {
            $data = $this->request->getData();

            // Handle features data - convert to JSON format for information field
            if (isset($data['information']) && is_array($data['information'])) {
                // Filter out empty values and encode as JSON
                $informationData = array_filter($data['information'], function($value) {
                    return !empty(trim($value));
                });
                $data['information'] = !empty($informationData) ? json_encode($informationData) : null;
            } else {
                $data['information'] = null;
            }

            // Handle banner image upload
            $bannerPath = WWW_ROOT . 'uploads/';
            if (!file_exists($bannerPath)) {
                mkdir($bannerPath, 0775, true);
            }

            if (!empty($data['banner_image']) && $data['banner_image'] instanceof UploadedFile && $data['banner_image']->getClientFilename()) {
                $bannerName = time() . '_' . preg_replace('/[^a-zA-Z0-9._-]/', '', $data['banner_image']->getClientFilename());
                $data['banner_image']->moveTo($bannerPath . $bannerName);
                $data['banner'] = $bannerName;
                // Remove old banner file if it exists
                if (!empty($destination->banner) && file_exists($bannerPath . $destination->banner)) {
                    unlink($bannerPath . $destination->banner);
                }
            }
            unset($data['banner_image']); // Remove the uploaded file object from data

            // Handle thumb image upload
            $thumbPath = WWW_ROOT . 'uploads/cities/';
            if (!file_exists($thumbPath)) {
                mkdir($thumbPath, 0775, true);
            }

            if (!empty($data['thumb_image']) && $data['thumb_image'] instanceof UploadedFile && $data['thumb_image']->getClientFilename()) {
                $thumbName = time() . '_thumb_' . preg_replace('/[^a-zA-Z0-9._-]/', '', $data['thumb_image']->getClientFilename());
                $data['thumb_image']->moveTo($thumbPath . $thumbName);
                $data['thumb_image'] = $thumbName; // Store filename in data
                // Remove old thumb file if it exists
                if (!empty($destination->thumb_image) && file_exists($thumbPath . $destination->thumb_image)) {
                    unlink($thumbPath . $destination->thumb_image);
                }
            } else {
                unset($data['thumb_image']); // Remove the uploaded file object from data
            }

            // Handle is_feature checkbox properly
            // With hiddenField => true, we get the actual value (0 or 1)
            $data['is_feature'] = !empty($data['is_feature']) && $data['is_feature'] == 1 ? 1 : 0;

            $destination = $this->Destinations->patchEntity($destination, $data);
            if ($this->Destinations->save($destination)) {
                $this->Flash->success(__('The destination has been updated.'));
                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The destination could not be updated. Please, try again.'));
        }
        
        $this->set(compact('features', 'destination', 'countries', 'regions', 'states', 'localities', 'masterDataLocationTypeList'));
  
    }

    public function edit($id = null)
    {
        $features = Configure::read('Constants.Features');
        $destination = $this->Destinations
            ->get($id, [
                'contain' => ['States', 'Countries', 'Localities']
            ]);

        // Fetch all countries for the dropdown
        $countries = $this->Countries->find('list', ['keyField' => 'id', 'valueField' => 'name'])->toArray();

        // Get states based on current destination data
        $regions = [];
        $states = [];
        $localities = [];
        
        if (!empty($destination->country_id)) {
            $regions = $this->Regions->find('list', [
                'keyField' => 'id',
                'valueField' => 'name'
            ])->where(['country_id' => $destination->country_id])->toArray();

            $states = $this->States->find('list', [
                'keyField' => 'id',
                'valueField' => 'name'
            ])->where(['country_id' => $destination->country_id])->toArray();
        }

        if (!empty($destination->state_id)) {
            $localities = $this->Localities->find('list', [
                'keyField' => 'id',
                'valueField' => 'name'
            ])->where(['city_id' => $destination->state_id])->toArray();
        }

        // Location Type List (for select)
        $masterDataLocationTypeList = $this->MasterData->getMasterDataByType('location_type');

        if ($this->request->is(['patch', 'post', 'put'])) {

            

   $bannerData = $this->request->getData();

            if (!empty($bannerData['banner_image']) && $bannerData['banner_image']->getError() === UPLOAD_ERR_OK) {
                $web_media = $bannerData['banner_image'];
                $webMediaName = trim($web_media->getClientFilename());
              
                $webMediaExt = strtolower(pathinfo($webMediaName, PATHINFO_EXTENSION));

                if (!empty($webMediaName)) {
                    $webMeidaTmpName = $web_media->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = 'uploads'; // Configure::read('Constants.BANNER_WEB_IMAGE');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $webMediaFile = pathinfo($webMediaName, PATHINFO_FILENAME) . '_' . $rand . '.' . $webMediaExt;
                    $uploadResult = $this->Media->upload($webMeidaTmpName, $targetdir, $webMediaFile, $uploadFolder);
                    if ($uploadResult === 'Success') {
                        $bannerData['web_banner'] = $uploadFolder . $webMediaFile;
                    }
                }
            }
// print_r($uploadResult); die;



            $data = $this->request->getData();

            // EDIT FUNCTION SPECIFIC: Generate name based on location hierarchy and type if name is empty
            $name = $data['name'] ?? '';
            if (empty($name)) {
                // Auto-generate name based on selected location and type
                $nameParts = [];
                
                if (!empty($data['locality_id'])) {
                    $locality = $this->Localities->get($data['locality_id']);
                    $nameParts[] = $locality->name;
                }

                if (!empty($data['city_id'])) {
                    $city = $this->Cities->get($data['city_id']);
                    $nameParts[] = $city->name;
                }

                if (!empty($data['state_id'])) {
                    $state = $this->States->get($data['state_id']);
                    $nameParts[] = $state->name;
                }
                
                if (!empty($data['country_id'])) {
                    $country = $this->Countries->get($data['country_id']);
                    $nameParts[] = $country->name;
                }
                
                // Use the most specific location available, or default to type
                $data['name'] = !empty($nameParts) ? $nameParts[0] : ucfirst($data['type'] ?? 'destination');
            }

            // Handle features data - convert to JSON format for information field
            if (isset($data['information']) && is_array($data['information'])) {
                // Filter out empty values and encode as JSON
                $informationData = array_filter($data['information'], function($value) {
                    return !empty(trim($value));
                });
                $data['information'] = !empty($informationData) ? json_encode($informationData) : null;
            } else {
                $data['information'] = null;
            }

            // Handle banner image upload
            if (isset($data['banner_image']) && $data['banner_image']->getError() === UPLOAD_ERR_OK) {
                $banner_image = $data['banner_image'];
                $fileName = trim($banner_image->getClientFilename());

                if (!empty($fileName)) {
                    $imageTmpName = $banner_image->getStream()->getMetadata('uri');

                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Settings.BANNER') ?? 'uploads/banners/';
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                    $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                    if ($uploadResult !== 'Success') {
                        $this->Flash->error(__('Banner image could not be uploaded. Please, try again.'));
                        return $this->redirect(['action' => 'edit', $id]);
                    } else {
                        // Remove old banner file if it exists
                        if (!empty($destination->banner)) {
                            $oldBannerPath = WWW_ROOT . $destination->banner;
                            if (file_exists($oldBannerPath)) {
                                unlink($oldBannerPath);
                            }
                        }
                        $data['banner'] = $folderPath . $imageFile;
                    }
                } else {
                    $this->Flash->error(__('Banner image filename is required.'));
                    return $this->redirect(['action' => 'edit', $id]);
                }
            } else {
                $data['banner'] = $this->request->getData('existing_banner');
            }
            unset($data['banner_image']); // Remove the uploaded file object from data

            // Handle thumb image upload
            $thumbPath = WWW_ROOT . 'uploads/cities/';
            if (!file_exists($thumbPath)) {
                mkdir($thumbPath, 0775, true);
            }

            if (!empty($data['thumb_image']) && $data['thumb_image'] instanceof UploadedFile && $data['thumb_image']->getClientFilename()) {
                $thumbName = time() . '_thumb_' . preg_replace('/[^a-zA-Z0-9._-]/', '', $data['thumb_image']->getClientFilename());
                $data['thumb_image']->moveTo($thumbPath . $thumbName);
                $data['thumb_image'] = $thumbName; // Store filename in data
                // Remove old thumb file if it exists
                if (!empty($destination->thumb_image) && file_exists($thumbPath . $destination->thumb_image)) {
                    unlink($thumbPath . $destination->thumb_image);
                }
            } else {
                unset($data['thumb_image']); // Remove the uploaded file object from data
            }

            // Handle is_feature checkbox properly
            // With hiddenField => true, we get the actual value (0 or 1)
            $data['is_feature'] = !empty($data['is_feature']) && $data['is_feature'] == 1 ? 1 : 0;

            $destination = $this->Destinations->patchEntity($destination, $data);
            if ($this->Destinations->save($destination)) {
                $this->Flash->success(__('The destination has been updated.'));
                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The destination could not be updated. Please, try again.'));
        }
        
        $this->set(compact('features', 'destination', 'countries', 'regions', 'states', 'localities', 'masterDataLocationTypeList'));
    }

    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);

        if ($this->request->is('ajax')) {
            $this->autoRender = false;
         
            try {
                if ($this->Destinations->softDelete($id)) {
                    $response = ['success' => true, 'message' => 'Destination deleted successfully.'];
                } else {
                    $response = ['success' => false, 'message' => 'Could not delete destination.'];
                }
            } catch (\Exception $e) {
                $response = ['success' => false, 'message' => 'Destination not found.'];
            }

            $this->response = $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
            return $this->response;
        }
        
        if ($this->Destinations->softDelete($id)) {
            $this->Flash->success(__('The destination has been deleted.'));
        } else {
            $this->Flash->error(__('The destination could not be deleted. Please, try again.'));
        }
        return $this->redirect(['action' => 'index']);
    }

    // AJAX: Get states by country with composite key filtering
    public function getStatesByCountry($countryId = null)
    {
        $this->request->allowMethod(['get', 'ajax']);
        $this->autoRender = false;
        
        $type = $this->request->getQuery('type');
        $editId = $this->request->getQuery('edit_id'); // For edit mode, exclude current record
        
        $states = [];
        
        if ($countryId) {
            if ($type) {
                // Get all states for the country first
                $allStates = $this->States->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['country_id' => $countryId])->order(['name' => 'ASC'])->toArray();

                // Apply composite key logic based on type
                if ($type === 'state') {
                    // For 'state' type, exclude states that already have a state-type destination
                    $usedStateQuery = $this->Destinations->find()
                        ->select(['state_id'])
                        ->where([
                            'type' => $type,
                            'country_id' => $countryId,
                            'status !=' => 'D', // Exclude deleted records
                            'state_id IS NOT' => null
                        ]);

                    // If editing, exclude current record
                    if ($editId) {
                        $usedStateQuery->where(['id !=' => $editId]);
                    }

                    $usedStateResults = $usedStateQuery->toArray();
                    $usedStateIds = array_column($usedStateResults, 'state_id');
                    
                    // Filter out used states for state type
                    foreach ($allStates as $stateId => $stateName) {
                        if (!in_array($stateId, $usedStateIds)) {
                            $states[$stateId] = $stateName;
                        }
                    }
                } else {
                    // For other types (city, locality), show all states for the country
                    // The filtering will happen at the next level (locality)
                    $states = $allStates;
                }
            } else {
                // If no type specified, return all states for the country (backward compatibility)
                $states = $this->States->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['country_id' => $countryId])->order(['name' => 'ASC'])->toArray();
            }
        }
        
        $this->response = $this->response->withType('application/json')
            ->withStringBody(json_encode($states));
        return $this->response;
    }

    // AJAX: Save or update city (destination)
    public function saveCity()
    {
        $this->request->allowMethod(['post', 'ajax']);
        $this->autoRender = false;

        $data = $this->request->getData();

        // Generate name based on location hierarchy if not provided
        $name = $data['name'] ?? '';
        if (empty($name)) {
            // Auto-generate name based on selected location
            $nameParts = [];
            
            if (!empty($data['locality_id'])) {
                $locality = $this->Localities->get($data['locality_id']);
                $nameParts[] = $locality->name;
            }

            if (!empty($data['city_id'])) {
                $city = $this->Cities->get($data['city_id']);
                $nameParts[] = $city->name;
            }

           if (!empty($data['state_id'])) {
                $state = $this->States->get($data['state_id']);
                $nameParts[] = $state->name;
            }
            
            if (!empty($data['country_id'])) {
                $country = $this->Countries->get($data['country_id']);
                $nameParts[] = $country->name;
            }
            
            // Use the most specific location available, or default to type
            $name = !empty($nameParts) ? $nameParts[0] : ucfirst($data['type'] ?? 'destination');
        }

        // Generate slug if not provided or if it already exists
        $slug = $data['slug'] ?? '';
        if (empty($slug)) {
            $slug = strtolower(trim($name));
            $slug = preg_replace('/\s+/', '-', $slug);
        } else {
            $slug = strtolower(trim($slug));
            $slug = preg_replace('/\s+/', '-', $slug);
        }
       
        // Use model method for slug existence check
        $slugExists = $this->Destinations->slugExists($slug, $data['id'] ?? null);

        if ($slugExists) {
            $slug .= '-' . time();
        }

        // Prepare data for saving
        $arrayData = [
            'name' => $name,
            'type' => $data['type'] ?? 'city',
            'slug' => $slug,
            'city_id' => !empty($data['city_id']) ? (int)$data['city_id'] : null,
            'country_id' => !empty($data['country_id']) ? (int)$data['country_id'] : null,
            'state_id' => !empty($data['state_id']) ? (int)$data['state_id'] : null,
            'region_id' => !empty($data['region_id']) ? (int)$data['region_id'] : null,
            'locality_id' => !empty($data['locality_id']) ? (int)$data['locality_id'] : null,
            'lat' => !empty($data['lat']) ? (float)$data['lat'] : null,
            'lng' => !empty($data['lng']) ? (float)$data['lng'] : null,
            'description' => $data['description'] ?? '',
            'meta_title' => $data['meta_title'] ?? '',
            'meta_description' => $data['meta_description'] ?? '',
            'meta_keywords' => $data['meta_keywords'] ?? '',
            'status' => $data['status'] ?? 'A',
            'is_feature' => !empty($data['is_feature']) && $data['is_feature'] == 1 ? 1 : 0,
            'near_type' => !empty($data['near_type']) ? (int)$data['near_type'] : null,
            'old_url' => $data['old_url'] ?? '',
            'near_beach' => isset($data['near_beach']) ? 1 : 0,
            'near_mountain' => isset($data['near_mountain']) ? 1 : 0,
        ];

        // Handle features/information as JSON string
        $featuresData = [];
        if (isset($data['information']) && is_array($data['information'])) {
            // Filter out empty values and encode as JSON string
            $featuresData = array_filter($data['information'], function($value) {
            return !empty(trim($value));
            });
        } elseif (isset($data['information']) && is_string($data['information'])) {
            // If already a JSON string, decode and re-encode to ensure valid JSON
            $decoded = json_decode($data['information'], true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
            $featuresData = array_filter($decoded, function($value) {
                return !empty(trim($value));
            });
            }
        }
        $arrayData['information'] = !empty($featuresData) ? json_encode($featuresData, JSON_UNESCAPED_UNICODE) : null;
       
        // If editing, expect 'id' in data
        if (!empty($data['id'])) {
            $arrayData['id'] = $data['id'];
        }

        // Validate required fields
        if (empty($arrayData['country_id'])) {
            $response = ['success' => false, 'message' => 'Country is required.'];
            $this->response = $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
            return $this->response;
        }

        // Validate composite key uniqueness based on type-specific rules
        // $uniquenessCheck = $this->validateDestinationUniqueness($arrayData);
        // if (!$uniquenessCheck['isUnique']) {
        //     $response = ['success' => false, 'message' => $uniquenessCheck['message']];
        //     $this->response = $this->response->withType('application/json')
        //         ->withStringBody(json_encode($response));
        //     return $this->response;
        // }

        try {
            // Log the data being saved for debugging
            error_log('Saving destination data: ' . json_encode($arrayData));
            
            $city = $this->Destinations->saveOrUpdate($arrayData);

            if ($city) {
                $this->Flash->success(__('The destination has been saved.'));
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode([
                        'success' => true,
                        'data' => $data['information'] ?? null,
                        'redirect' => '/admin/destinations',
                    ]));
            } else {
                // Log more details about the failure
                error_log('SaveOrUpdate returned false for data: ' . json_encode($arrayData));
                $response = ['success' => false, 'message' => 'Could not save destination. Please check your input.'];
            }
        } catch (\Exception $e) {
            error_log('Exception in saveCity: ' . $e->getMessage());
            error_log('Exception trace: ' . $e->getTraceAsString());
            $response = ['success' => false, 'message' => 'Error saving destination: ' . $e->getMessage()];
        }

        $this->response = $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
        return $this->response;
    }

    // AJAX: Find country and state IDs by names (for city search functionality)
    public function findLocationIds()
    {
        $this->request->allowMethod(['post', 'ajax']);
        $this->autoRender = false;
        
        $data = $this->request->getData();
        $countryName = $data['country'] ?? '';
        $stateName = $data['state'] ?? '';
        
        $response = [];
        
        // Find country
        if ($countryName) {
            $country = $this->Countries->find()
                ->where(['name LIKE' => '%' . $countryName . '%'])
                ->first();
            
            if ($country) {
                $response['country_id'] = $country->id;
                $response['country_name'] = $country->name;
            }
        }
        
        // Find state
        if ($stateName && !empty($response['country_id'])) {
            $state = $this->States->find()
                ->where([
                    'name LIKE' => '%' . $stateName . '%',
                    'country_id' => $response['country_id']
                ])
                ->first();
                
            if ($state) {
                $response['state_id'] = $state->id;
                $response['state_name'] = $state->name;
            }
        }
        
        $this->response = $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
        return $this->response;
    }

    // AJAX: Search city and get location details
    public function searchCityLocation()
    {
        $this->request->allowMethod(['post', 'ajax']);
        $this->autoRender = false;
        
        // Load Cities table only for this method
        $citiesTable = $this->fetchTable('Cities');
        
        $data = $this->request->getData();
        $cityName = $data['city'] ?? '';
        
        $response = [];
        
        if ($cityName && strlen($cityName) > 2) {
            // First try to find in our database
            $city = $citiesTable->find()
                ->contain(['Countries', 'States'])
                ->where(['Cities.name LIKE' => '%' . $cityName . '%'])
                ->first();
            
            if ($city) {
                $response = [
                    'success' => true,
                    'city_id' => $city->id,
                    'city_name' => $city->name,
                    'country_id' => $city->country_id,
                    'country_name' => $city->country->name ?? '',
                    'state_id' => $city->state_id,
                    'state_name' => $city->state->name ?? '',
                    'lat' => $city->lat,
                    'lng' => $city->lng
                ];
            } else {
                // If not found in database, we'll rely on the client-side Nominatim search
                $response = [
                    'success' => false,
                    'message' => 'City not found in database, use map search'
                ];
            }
        } else {
            $response = [
                'success' => false,
                'message' => 'City name too short'
            ];
        }
        
        $this->response = $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
        return $this->response;
    }

    /**
     * Debug method to test DataTables endpoint
     */
    public function debugDataTables()
    {
        $this->autoRender = false;
        
        $response = [
            'message' => 'DataTables endpoint is working',
            'timestamp' => date('Y-m-d H:i:s'),
            'request_method' => $this->request->getMethod(),
            'is_ajax' => $this->request->is('ajax'),
            'query_params' => $this->request->getQueryParams(),
            'draw_param' => $this->request->getQuery('draw')
        ];
        
        $this->response = $this->response->withType('application/json')
            ->withStringBody(json_encode($response, JSON_PRETTY_PRINT));
        return $this->response;
    }

    /**
     * Get regions by country for dropdown (AJAX)
     */
    public function getRegionsByCountry()
    {
        $this->request->allowMethod(['post', 'ajax']);
        $this->autoRender = false;
        
        $countryId = $this->request->getData('country_id');
        $regions = [];
        
        if ($countryId) {
            $regions = $this->Regions->find('list', [
                'keyField' => 'id',
                'valueField' => 'name'
            ])->where(['country_id' => $countryId])->order(['name' => 'ASC'])->toArray();
        }
        
        $response = ['regions' => $regions];
        $this->response = $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
        return $this->response;
    }

    /**
     * Get states by region for dropdown (AJAX)
     */
    public function getStatesByRegion()
    {
        $this->request->allowMethod(['post', 'ajax']);
        $this->autoRender = false;
        
        $regionId = $this->request->getData('region_id');
        $states = [];
        
        if ($regionId) {
            $states = $this->States->find('list', [
                'keyField' => 'id',
                'valueField' => 'name'
            ])->where(['region_id' => $regionId])->order(['name' => 'ASC'])->toArray();
        }
        
        $response = ['states' => $states];
        $this->response = $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
        return $this->response;
    }

    /**
     * Get localities by state for dropdown (AJAX)
     */
    public function getLocalitiesByState($stateId = null)
    {
        $this->request->allowMethod(['get', 'ajax']);
        $this->autoRender = false;
        
        $type = $this->request->getQuery('type');
        $editId = $this->request->getQuery('edit_id'); // For edit mode, exclude current record
        
        $localities = [];
        if ($stateId) {
            if ($type) {
                // Get all localities for the state first
                $allLocalities = $this->Localities->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['city_id' => $stateId])->order(['name' => 'ASC'])->toArray();

                // Apply composite key logic based on type
                if ($type === 'locality') {
                    // For 'locality' type, exclude localities that already have a locality-type destination
                    $usedLocalityQuery = $this->Destinations->find()
                        ->select(['locality_id'])
                        ->where([
                            'type' => $type,
                            'state_id' => $stateId,
                            'status !=' => 'D', // Exclude deleted records
                            'locality_id IS NOT' => null
                        ]);

                    // If editing, exclude current record
                    if ($editId) {
                        $usedLocalityQuery->where(['id !=' => $editId]);
                    }

                    $usedLocalityResults = $usedLocalityQuery->toArray();
                    $usedLocalityIds = array_column($usedLocalityResults, 'locality_id');
                    
                    // Filter out used localities for locality type
                    foreach ($allLocalities as $localityId => $localityName) {
                        if (!in_array($localityId, $usedLocalityIds)) {
                            $localities[$localityId] = $localityName;
                        }
                    }
                } else {
                    // For other types, show all localities for the state
                    $localities = $allLocalities;
                }
            } else {
                // If no type specified, return all localities for the state (backward compatibility)
                $localities = $this->Localities->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['city_id' => $stateId])->order(['name' => 'ASC'])->toArray();
            }
        }
        
        $this->response = $this->response->withType('application/json')
            ->withStringBody(json_encode($localities));
        return $this->response;
    }

    /**
     * Get cities by state for dropdown with composite key filtering (AJAX)
     */
    public function getCitiesByState($stateId = null)
    {
        $this->request->allowMethod(['get', 'ajax']);
        $this->autoRender = false;
        
        $type = $this->request->getQuery('type');
        $editId = $this->request->getQuery('edit_id'); // For edit mode, exclude current record
        
        // Load Cities table
        $citiesTable = $this->fetchTable('Cities');
        
        $cities = [];
        if ($stateId) {
            if ($type) {
                // Get all cities for the state first
                $allCities = $citiesTable->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['state_id' => $stateId])->order(['name' => 'ASC'])->toArray();

                // Apply composite key logic based on type
                if ($type === 'city') {
                    // For 'city' type, exclude cities that already have a city-type destination
                    // Check which specific cities are already used in destinations
                    $usedCityQuery = $this->Destinations->find()
                        ->select(['city_id'])
                        ->where([
                            'type' => $type,
                            'city_id IS NOT' => null,
                            'status !=' => 'D', // Exclude deleted records
                        ]);

                    // If editing, exclude current record
                    if ($editId) {
                        $usedCityQuery->where(['id !=' => $editId]);
                    }

                    $usedCityResults = $usedCityQuery->toArray();
                    $usedCityIds = array_column($usedCityResults, 'city_id');
                    
                    // Filter out cities that already have a city-type destination
                    foreach ($allCities as $cityId => $cityName) {
                        if (!in_array($cityId, $usedCityIds)) {
                            $cities[$cityId] = $cityName;
                        }
                    }
                } else {
                    // For other types, show all cities for the state
                    $cities = $allCities;
                }
            } else {
                // If no type specified, return all cities for the state (backward compatibility)
                $cities = $citiesTable->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['state_id' => $stateId])->order(['name' => 'ASC'])->toArray();
            }
        }
        
        $this->response = $this->response->withType('application/json')
            ->withStringBody(json_encode($cities));
        return $this->response;
    }

    /**
     * Get localities by city for dropdown with composite key filtering (AJAX)
     */
    public function getLocalitiesByCity($cityId = null)
    {
        $this->request->allowMethod(['get', 'ajax']);
        $this->autoRender = false;
        
        $type = $this->request->getQuery('type');
        $editId = $this->request->getQuery('edit_id'); // For edit mode, exclude current record
        
        // Load Localities table
        $localitiesTable = $this->fetchTable('Localities');
        
        $localities = [];
        if ($cityId) {
            if ($type) {
                // Get all localities for the city first
                $allLocalities = $localitiesTable->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['city_id' => $cityId])->order(['name' => 'ASC'])->toArray();

                // Apply composite key logic based on type
                if ($type === 'locality') {
                    // For 'locality' type, exclude localities that already have a locality-type destination
                    $usedLocalityQuery = $this->Destinations->find()
                        ->select(['locality_id'])
                        ->where([
                            'type' => $type,
                            'city_id' => $cityId,
                            'status !=' => 'D', // Exclude deleted records
                        ]);

                    // If editing, exclude current record
                    if ($editId) {
                        $usedLocalityQuery->where(['id !=' => $editId]);
                    }

                    $usedLocalityResults = $usedLocalityQuery->toArray();
                    $usedLocalityIds = array_column($usedLocalityResults, 'locality_id');
                    
                    // Filter out localities that already have a locality-type destination
                    foreach ($allLocalities as $localityId => $localityName) {
                        if (!in_array($localityId, $usedLocalityIds)) {
                            $localities[$localityId] = $localityName;
                        }
                    }
                } else {
                    // For other types, show all localities for the city
                    $localities = $allLocalities;
                }
            } else {
                // If no type specified, return all localities for the city (backward compatibility)
                $localities = $localitiesTable->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['city_id' => $cityId])->order(['name' => 'ASC'])->toArray();
            }
        }
        
        $this->response = $this->response->withType('application/json')
            ->withStringBody(json_encode($localities));
        return $this->response;
    }

    /**
     * Export destinations to CSV
     */
    public function exportDestinations()
    {
        $status = $this->request->getQuery('status');
        $country = $this->request->getQuery('country');
        $state = $this->request->getQuery('state');
        
        $query = $this->Destinations->find()
            ->contain(['Countries', 'States', 'Localities'])
            ->where(['Destinations.status !=' => 'D']); // Exclude deleted by default
        
        // Apply filters
        if (!empty($status)) {
            $query->where(['Destinations.status' => $status]);
        }
        if (!empty($country)) {
            $query->where(['Destinations.country_id' => $country]);
        }
        if (!empty($state)) {
            $query->where(['Destinations.state_id' => $state]);
        }
        
        $destinations = $query->all();
        $filename = "destinations_list_" . date('Y-m-d') . ".csv";
        
        $this->response = $this->response->withDownload($filename);
        $this->response = $this->response->withType('text/csv');
        
        $csvData = fopen('php://output', 'w');
        fputcsv($csvData, ['S.No', 'Name', 'Country', 'State', 'Locality', 'Type', 'Status', 'Created Date']);
        
        $counter = 1;
        foreach ($destinations as $destination) {
            $countryName = !empty($destination->country) ? $destination->country->name : 'N/A';
            $stateName = !empty($destination->state) ? $destination->state->name : 'N/A';
            $localityName = !empty($destination->locality) ? $destination->locality->name : 'N/A';
            
            $statusMap = [
                'A' => 'Active',
                'I' => 'Inactive',
                'D' => 'Deleted'
            ];
            $statusLabel = $statusMap[$destination->status] ?? 'Unknown';
            
            fputcsv($csvData, [
                $counter++,
                $destination->name,
                $countryName,
                $stateName,
                $localityName,
                ucfirst($destination->type ?? 'city'),
                $statusLabel,
                $destination->created_at ? $destination->created_at->format('Y-m-d H:i:s') : 'N/A'
            ]);
        }

        fclose($csvData);
        return $this->response;
    }

    public function approve($id)
    {
        $destination = $this->Destinations->get($id);

        // Toggle status: If Active -> set to Inactive, else set to Active
        $destination->status = ($destination->status === 'A') ? 'I' : 'A';

        if ($this->Destinations->save($destination)) {
            $this->Flash->success(__('Destination status updated successfully.'), [
                'key' => 'destination_success'
            ]);
        } else {
            $this->Flash->error(__('Unable to update destination status. Please try again.'), [
                'key' => 'destination_error'
            ]);
        }

        return $this->redirect(['action' => 'index']);
    }

    // AJAX: Get states by country ID
    public function getStates()
    {
        $this->request->allowMethod(['post', 'ajax']);
        $this->autoRender = false;

        $countryId = $this->request->getData('country_id');
        $states = [];

        if ($countryId) {
            $states = $this->States->find('list', [
                'keyField' => 'id',
                'valueField' => 'name'
            ])->where(['country_id' => $countryId])->toArray();
        }

        $response = ['states' => $states];
        $this->response = $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
        return $this->response;
    }

    // AJAX: Get localities by state ID
    public function getLocalities()
    {
        $this->request->allowMethod(['post', 'ajax']);
        $this->autoRender = false;

        $stateId = $this->request->getData('state_id');
        $localities = [];

        if ($stateId) {
            $localities = $this->Localities->find('list', [
                'keyField' => 'id',
                'valueField' => 'name'
            ])->where(['city_id' => $stateId])->toArray();
        }

        $response = ['localities' => $localities];
        $this->response = $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
        return $this->response;
    }

    /**
     * Get available countries based on type and exclude already used combinations
     * AJAX: Get countries filtered by type (composite key logic)
     */
    public function getAvailableCountries()
    {
        $this->request->allowMethod(['post', 'ajax']);
        $this->autoRender = false;

        $type = $this->request->getData('type');
        $editId = $this->request->getData('edit_id'); // For edit mode, exclude current record
        
        $countries = [];

        if ($type) {
            // Get all countries first
            $allCountries = $this->Countries->find('list', [
                'keyField' => 'id',
                'valueField' => 'name'
            ])->order(['name' => 'ASC'])->toArray();

            // Find already used country IDs for this type based on type-specific rules
            $usedCountryQuery = $this->Destinations->find()
                ->select(['country_id'])
                ->where([
                    'type' => $type,
                    'status !=' => 'D', // Exclude deleted records
                    'country_id IS NOT' => null
                ]);

            // If editing, exclude current record
            if ($editId) {
                $usedCountryQuery->where(['id !=' => $editId]);
            }

            // For 'country' type, exclude countries that already have a country-type destination
            // For other types, we can be more lenient
            if ($type === 'country') {
                $usedCountryResults = $usedCountryQuery->toArray();
                $usedCountryIds = array_column($usedCountryResults, 'country_id');
                
                // Filter out used countries for country type
                foreach ($allCountries as $countryId => $countryName) {
                    if (!in_array($countryId, $usedCountryIds)) {
                        $countries[$countryId] = $countryName;
                    }
                }
            } else {
                // For other types (state, region, city, locality), show all countries
                // The filtering will happen at the next level (state/region/locality)
                $countries = $allCountries;
            }
        }

        $response = ['countries' => $countries];
        $this->response = $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
        return $this->response;
    }

    /**
     * Get available states based on type and country, exclude already used combinations
     * AJAX: Get states filtered by type and country (composite key logic)
     */
    public function getAvailableStates()
    {
        $this->request->allowMethod(['post', 'ajax']);
        $this->autoRender = false;

        $type = $this->request->getData('type');
        $countryId = $this->request->getData('country_id');
        $editId = $this->request->getData('edit_id'); // For edit mode, exclude current record
        
        $states = [];

        if ($type && $countryId) {
            // Get all states for the country first
            $allStates = $this->States->find('list', [
                'keyField' => 'id',
                'valueField' => 'name'
            ])->where(['country_id' => $countryId])->order(['name' => 'ASC'])->toArray();

            if ($type === 'state') {
                // For 'state' type, exclude states that already have a state-type destination
                $usedStateQuery = $this->Destinations->find()
                    ->select(['state_id'])
                    ->where([
                        'type' => $type,
                        'country_id' => $countryId,
                        'status !=' => 'D', // Exclude deleted records
                        'state_id IS NOT' => null
                    ]);

                // If editing, exclude current record
                if ($editId) {
                    $usedStateQuery->where(['id !=' => $editId]);
                }

                $usedStateResults = $usedStateQuery->toArray();
                $usedStateIds = array_column($usedStateResults, 'state_id');

                // Filter out used states
                foreach ($allStates as $stateId => $stateName) {
                    if (!in_array($stateId, $usedStateIds)) {
                        $states[$stateId] = $stateName;
                    }
                }
            } else {
                // For other types (region, city, locality), show all states for the country
                $states = $allStates;
            }
        }

        $response = ['states' => $states];
        $this->response = $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
        return $this->response;
    }

    /**
     * Get available localities based on type, country and state, exclude already used combinations
     * AJAX: Get localities filtered by type, country and state (composite key logic)
     */
    public function getAvailableLocalities()
    {
        $this->request->allowMethod(['post', 'ajax']);
        $this->autoRender = false;

        $type = $this->request->getData('type');
        $countryId = $this->request->getData('country_id');
        $stateId = $this->request->getData('state_id');
        $editId = $this->request->getData('edit_id'); // For edit mode, exclude current record
        
        $localities = [];

        if ($type && $stateId) {
            // Get all localities for the state first
            $allLocalities = $this->Localities->find('list', [
                'keyField' => 'id',
                'valueField' => 'name'
            ])->where(['city_id' => $stateId])->order(['name' => 'ASC'])->toArray();

            if (in_array($type, ['city', 'locality'])) {
                // For 'city' or 'locality' types, exclude localities that already have destinations of this type
                $usedLocalityQuery = $this->Destinations->find()
                    ->select(['locality_id'])
                    ->where([
                        'type' => $type,
                        'country_id' => $countryId,
                        'state_id' => $stateId,
                        'status !=' => 'D', // Exclude deleted records
                        'locality_id IS NOT' => null
                    ]);

                // If editing, exclude current record
                if ($editId) {
                    $usedLocalityQuery->where(['id !=' => $editId]);
                }

                $usedLocalityResults = $usedLocalityQuery->toArray();
                $usedLocalityIds = array_column($usedLocalityResults, 'locality_id');

                // Filter out used localities
                foreach ($allLocalities as $localityId => $localityName) {
                    if (!in_array($localityId, $usedLocalityIds)) {
                        $localities[$localityId] = $localityName;
                    }
                }
            } else {
                // For other types, show all localities for the state
                $localities = $allLocalities;
            }
        }

        $response = ['localities' => $localities];
        $this->response = $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
        return $this->response;
    }

    /**
     * Get available regions based on type and country, exclude already used combinations
     * AJAX: Get regions filtered by type and country (composite key logic)
     */
    public function getAvailableRegions()
    {
        $this->request->allowMethod(['post', 'ajax']);
        $this->autoRender = false;

        $type = $this->request->getData('type');
        $countryId = $this->request->getData('country_id');
        $editId = $this->request->getData('edit_id'); // For edit mode, exclude current record
        
        $regions = [];

        if ($type && $countryId) {
            // Get all regions for the country first
            $allRegions = $this->Regions->find('list', [
                'keyField' => 'id',
                'valueField' => 'name'
            ])->where(['country_id' => $countryId])->order(['name' => 'ASC'])->toArray();

            if ($type === 'region') {
                // For 'region' type, exclude regions that already have a region-type destination
                $usedRegionQuery = $this->Destinations->find()
                    ->select(['region_id'])
                    ->where([
                        'type' => $type,
                        'country_id' => $countryId,
                        'status !=' => 'D', // Exclude deleted records
                        'region_id IS NOT' => null
                    ]);

                // If editing, exclude current record
                if ($editId) {
                    $usedRegionQuery->where(['id !=' => $editId]);
                }

                $usedRegionResults = $usedRegionQuery->toArray();
                $usedRegionIds = array_column($usedRegionResults, 'region_id');

                // Filter out used regions
                foreach ($allRegions as $regionId => $regionName) {
                    if (!in_array($regionId, $usedRegionIds)) {
                        $regions[$regionId] = $regionName;
                    }
                }
            } else {
                // For other types, show all regions for the country
                $regions = $allRegions;
            }
        }

        $response = ['regions' => $regions];
        $this->response = $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
        return $this->response;
    }

    /**
     * Check if a destination with the given type and location combination already exists
     * AJAX: Validate composite key uniqueness
     */
    public function checkCompositeUniqueness()
    {
        $this->request->allowMethod(['post', 'ajax']);
        $this->autoRender = false;

        $data = [
            'type' => $this->request->getData('type'),
            'country_id' => $this->request->getData('country_id'),
            'state_id' => $this->request->getData('state_id'),
            'region_id' => $this->request->getData('region_id'),
            'locality_id' => $this->request->getData('locality_id'),
            'id' => $this->request->getData('edit_id') // For edit mode, exclude current record
        ];

        $validation = $this->validateDestinationUniqueness($data);

        $response = [
            'isUnique' => $validation['isUnique'],
            'message' => $validation['message']
        ];

        $this->response = $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
        return $this->response;
    }

    /**
     * Validate destination uniqueness based on type-specific rules
     */
    private function validateDestinationUniqueness($data)
    {
        $type = $data['type'];
        $editId = $data['id'] ?? null;

        $query = $this->Destinations->find()
            ->where([
                'type' => $type,
                'status !=' => 'D' // Exclude deleted records
            ]);

        // If editing, exclude current record
        if ($editId) {
            $query->where(['id !=' => $editId]);
        }

        // Apply type-specific uniqueness rules
        switch ($type) {
            case 'country':
                // For country type: only country_id should be unique
                $query->where(['country_id' => $data['country_id']]);
                break;

            case 'state':
                // For state type: country_id + state_id should be unique
                $query->where([
                    'country_id' => $data['country_id'],
                    'state_id' => $data['state_id']
                ]);
                break;

            case 'region':
                // For region type: country_id + region_id should be unique
                $query->where([
                    'country_id' => $data['country_id']
                ]);
                if (!empty($data['region_id'])) {
                    $query->where(['region_id' => $data['region_id']]);
                }
                break;

            case 'city':
                // For city type: country_id + state_id + locality_id should be unique
                $conditions = ['country_id' => $data['country_id']];
                if (!empty($data['state_id'])) {
                    $conditions['state_id'] = $data['state_id'];
                }
                if (!empty($data['locality_id'])) {
                    $conditions['locality_id'] = $data['locality_id'];
                }
                $query->where($conditions);
                break;

            case 'locality':
                // For locality type: full location hierarchy should be unique
                $conditions = ['country_id' => $data['country_id']];
                if (!empty($data['state_id'])) {
                    $conditions['state_id'] = $data['state_id'];
                }
                if (!empty($data['locality_id'])) {
                    $conditions['locality_id'] = $data['locality_id'];
                }
                $query->where($conditions);
                break;

            default:
                // Default: check country_id only
                $query->where(['country_id' => $data['country_id']]);
                break;
        }

        $existingDestination = $query->first();

        if ($existingDestination) {
            return [
                'isUnique' => false,
                'message' => "A destination with type '{$type}' already exists for this location combination."
            ];
        }

        return ['isUnique' => true, 'message' => ''];
    }
}