<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;
use Cake\I18n\FrozenTime;

/**
 * Review Entity
 *
 * @property int $id
 * @property int $customer_id
 * @property int|null $booking_id
 * @property int|null $booking_item_id
 * @property string $review_type
 * @property int $reference_id
 * @property int $rating
 * @property string|null $comment
 * @property string|null $status
 * @property \Cake\I18n\FrozenTime|null $created_at
 * @property \Cake\I18n\FrozenTime|null $updated_at
 */
class Review extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'customer_id' => true,
        'booking_id' => true,
        'booking_item_id' => true,
        'review_type' => true,
        'reference_id' => true,
        'rating' => true,
        'comment' => true,
        'status' => true,
        'created_at' => true,
        'updated_at' => true,
    ];

    /**
     * Virtual fields
     *
     * @var array<string>
     */
    protected array $_virtual = [
        'status_badge_class',
        'rating_stars',
        'review_type_label',
        'is_approved'
    ];

    /**
     * Get status badge class for UI
     *
     * @return string
     */
    protected function _getStatusBadgeClass(): string
    {
        switch ($this->status) {
            case 'Approved':
                return 'bg-success';
            case 'Rejected':
                return 'bg-danger';
            case 'Pending':
            default:
                return 'bg-warning';
        }
    }

    /**
     * Get rating as stars
     *
     * @return string
     */
    protected function _getRatingStars(): string
    {
        $stars = '';
        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $this->rating) {
                $stars .= '<i class="fas fa-star text-warning"></i>';
            } else {
                $stars .= '<i class="far fa-star text-muted"></i>';
            }
        }
        return $stars;
    }

    /**
     * Get review type label
     *
     * @return string
     */
    protected function _getReviewTypeLabel(): string
    {
        switch ($this->review_type) {
            case 'course':
                return 'Course Review';
            case 'teacher':
                return 'Teacher Review';
            case 'center':
                return 'Center Review';
            default:
                return ucfirst($this->review_type);
        }
    }

    /**
     * Check if review is approved
     *
     * @return bool
     */
    protected function _getIsApproved(): bool
    {
        return $this->status === 'Approved';
    }

    /**
     * Get short comment for listing
     *
     * @return string
     */
    public function getShortComment(int $length = 100): string
    {
        if (empty($this->comment)) {
            return 'No comment provided';
        }
        
        if (strlen($this->comment) <= $length) {
            return $this->comment;
        }
        
        return substr($this->comment, 0, $length) . '...';
    }
}
