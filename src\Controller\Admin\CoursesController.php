<?php

declare(strict_types=1);

namespace App\Controller\Admin;

use Authentication\PasswordHasher\DefaultPasswordHasher;
use Cake\Core\Configure;
use Cake\ORM\TableRegistry;
use Cake\Routing\Router;
use Cake\Utility\Security;
use Cake\I18n\FrozenTime;
use Cake\Log\Log;
use Cake\I18n\DateTime;

/**
 * Courses Controller
 *
 * @property \App\Model\Table\CoursesTable $Courses
 */
class CoursesController extends AppController
{
    protected $Currencies;
    protected $Partners;
    protected $SpecialNeeds;
    protected $Countries;
    protected $States;
    protected $Cities;
    protected $CourseBatches;
    protected $MasterData;
    protected $Modalities;
    protected $CourseYogaStyles;
    protected $CourseSpecialNeeds;
    protected $Teachers;
    protected $CourseTeachers;
    protected $CourseBatchPricing;
    protected $CourseModalities;
    
    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->loadComponent('Common');
        $this->loadComponent('ValidationHelper');
        $this->Countries = $this->fetchTable('Countries');
        $this->States = $this->fetchTable('States');
        $this->Cities = $this->fetchTable('Cities');
        $this->Currencies = $this->fetchTable('Currencies');
        $this->Partners = $this->fetchTable('Partners');
        $this->SpecialNeeds = $this->fetchTable('SpecialNeeds');
        $this->CourseBatches = $this->fetchTable('CourseBatches');
        $this->CourseBatchPricing = $this->fetchTable('CourseBatchPricing');
        $this->MasterData = $this->fetchTable('MasterData');
        $this->Modalities = $this->fetchTable('Modalities');
        $this->CourseYogaStyles = $this->fetchTable('CourseYogaStyles');
        $this->CourseSpecialNeeds = $this->fetchTable('CourseSpecialNeeds');
        $this->Teachers = $this->fetchTable('Teachers');
        $this->CourseTeachers = $this->fetchTable('CourseTeachers');
        $this->CourseModalities = $this->fetchTable('CourseModalities');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
    }

    public function index()
    {
        if ($this->request->is('ajax')) {
            $this->viewBuilder()->enableAutoLayout(false);
            $this->viewBuilder()->setClassName('Json');
            $this->response = $this->response->withType('application/json');

            $draw = (int)$this->request->getData('draw');
            $start = (int)$this->request->getData('start');
            $length = (int)$this->request->getData('length');
            $search = $this->request->getData('search.value');
            $status = $this->request->getData('status');
          
            $query = $this->Courses->find()->contain([
                'CourseTypes'
            ]);

            if (!empty($status)) {
                $query->where(['Courses.status' => $status]);
            } else {
                $query->where(['Courses.status !=' => 'D']);
            }
           
            if (!empty($search)) {
                $query->where([
                    'Courses.name LIKE' => '%' . $search . '%'
                ]);
            }

            $recordsTotal = $this->Courses->find()->where(['Courses.status !=' => 'D'])->count();
            $recordsFiltered = $query->count();

            $courses = $query
                ->limit($length)
                ->offset($start)
                ->order(['Courses.id' => 'DESC'])
                ->all();

            $data = [];
          
            foreach ($courses as $course) {
                $data[] = [
                    'id' => $course->id,
                    'name' => $course->name,
                    'course_type' => $course->course_type->name ?? '',
                    'address' => $course->address ?? '',  
                    'status' => $course->status
                ];
            }

            $responseData = [
                'draw' => $draw,
                'recordsTotal' => $recordsTotal,
                'recordsFiltered' => $recordsFiltered,
                'data' => $data
             ];

            return $this->response
                ->withType('application/json')
                ->withStringBody(json_encode($responseData));
        }

        // Optional: for non-AJAX page load
        $courses = [];
        $this->set(compact('courses'));
    }

    public function view($id = null)
    {
        $course = $this->Courses->get($id, contain: [
            'CourseTypes',
            'CourseGalleries',
            'Partners',
            'YogaStyleMasterData',
            'SpecialNeedMasterData',
            'Countries',
            'States',
            'Cities',
            'Teachers.Partners',
            'CourseBatches',
            'CourseAddons.CourseAddonPricing.Currencies',
            'CourseAddons.MasterData',
            'CourseBasePrices.Currencies',
            'CourseModalities.Modalities',
            // 'CourseTeachers.Teachers.Partners'
        ]);
    
        // dd($course);
        // Log::debug(print_r($course, true));
     
        $media = $this->Media;
        $specialNeeds = [];
        if(!empty($course->special_need_master_data)){
            foreach($course->special_need_master_data as $special){
                $specialNeeds[] = $special->title;
            }
        }
        
        $modeNames = [];
        if($course->course_modalities){
               foreach($course->course_modalities as $mode){
                if($mode->modality){
                    $modeNames[] = $mode->modality->name;
                }
            }
        }

        $levelNames = [];
        if($course->level){
            $levels = explode(',', $course->level);
            $levelNames = $this->MasterData->getNamesList($levels);
        }

        $techNames = [];
        if($course->techniques){
            $techniques = explode(',', $course->techniques);
            $techNames = $this->MasterData->getNamesList($techniques);
        }

        $this->set(compact('course', 'media', 'specialNeeds', 'modeNames', 'levelNames', 'techNames'));
    }

    protected function getTeachersList(){
        $teachersList = [];
        $partnerTeachers = [];

        // $existingPartnerIds = $this->Teachers->find()
        // ->select('partner_id')
        // ->where(['partner_id IS NOT' => null])
        // ->all()
        // ->extract('partner_id')
        // ->toArray();
        
        $partnerTypeTeacher = $this->fetchTable('PartnerTypes')->getByName('Teachers');
        
        if($partnerTypeTeacher){

            $partnerTeachers = $this->fetchTable('Partners')->find()
                // ->where(['partner_type_id' => $partnerTypeTeacher->id, 'id NOT IN' => $existingPartnerIds])
                ->where(['partner_type_id' => $partnerTypeTeacher->id])
                ->orderAsc('name')
                ->all();
            // $partnerTeachers  =  $this->fetchTable('Partners')->find()
            // ->select(['id', 'name'])
            // ->where(
            //     ['partner_type_id' => $partnerTypeTeacher->id]
            // )->all();

            if(!empty($partnerTeachers)){
               foreach ($partnerTeachers as $p) {
                    $key = 'p_' . $p->id;
                    $label = $p->name;
                    if (!empty($p->email)) {
                        $label .= " ({$p->email})";
                    }
                    $teachersList[$key] = $label . " [Partner]";
                }
            }
        }
       
        $guestTeachers  = $this->Teachers->getGuestTeacher();

        if(!empty($guestTeachers)){
           foreach ($guestTeachers as $t) {
                $key = 't_' . $t->id;
                $label = $t->name;
                if (!empty($t->email)) {
                    $label .= " ({$t->email})";
                }
                $teachersList[$key] = $label . " [Guest]";
            }

        }

        return $teachersList;
    }

    public function add()
    {
        $course = $this->Courses->newEmptyEntity();
        $addonTypes = $this->MasterData->findByType('addon');
     
        $modalities = $this->Modalities->selectInputOptions();

        $yogaStyles   = $this->MasterData->findByType('yoga_style')->toArray();
        $specialNeeds = $this->MasterData->findByType('special_need')->toArray();
        $level        = $this->MasterData->findByType('level')->toArray();
        $techniques   = $this->MasterData->findByType('technique')->toArray();
        $yogaStyles['n/a']  = 'N/A';
        $specialNeeds['n/a']= 'N/A';   
        $techniques['n/a']  = 'N/A';
      
        // get teachers list //
        $teachersList = $this->getTeachersList();
       
        $coursetypes = $this->Courses->CourseTypes->find('list')->where(['CourseTypes.status' => 'A'])->all();
        $partners = $this->Partners->find('list')->where(['Partners.status' => 'A'])->order(['Partners.name' => 'ASC'])->all();

        try {
            if ($this->request->is(['ajax'])) {
                $data = $this->request->getData();
               
                if(!empty($data['course_base_prices'])){
                    foreach ($data['course_base_prices'] as $index => $bp) {
                        if(!$bp['name']){
                            unset($data['course_base_prices'][$index]);
                            continue;
                        }
                    }
                }
            
                $data['slug'] = str_replace(' ', '-', $data['name']);
            
                if (!empty($data['banner_image']) && $data['banner_image'] instanceof \Laminas\Diactoros\UploadedFile) {
                    if ($data['banner_image']->getError() === UPLOAD_ERR_OK) {
                        $course_Image = $data['banner_image'];
                        $fileName = preg_replace('/[^A-Za-z0-9_\-\.]/', '_', trim($data['banner_image']->getClientFilename()));


                        if (!empty($fileName)) {
                            $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                            $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                            $filePath = Configure::read('Settings.Course');
                            $folderPath = $uploadFolder . $filePath;
                            $ext = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                            $newFileName = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                            // Upload using UploadedFileInterface
                            $uploadResult = $this->Media->uploadMedia($course_Image, $newFileName, $folderPath);

                            if ($uploadResult !== 'Success') {
                                $response = [
                                    'success' => false,
                                    'message' => 'Banner Image could not be uploaded. Please, try again.'
                                ];
                                return $this->response
                                    ->withType('application/json')
                                    ->withStringBody(json_encode($response));
                                
                            } else {
                                $data['banner_image'] = $filePath . $newFileName;
                            }
                        }
                    } else {
                        // If image exists but has error, remove it to avoid saving object to DB
                        unset($data['banner_image']);
                    }
                }

                if (!empty($data['level']) && is_array($data['level'])) {
                    $data['level'] = implode(',', $data['level']);
                }

                if (!empty($data['techniques']) && is_array($data['techniques'])) {
                    $data['techniques'] = implode(',', $data['techniques']);
                }

                if (!empty($data['action']) && $data['action'] === 'validate') {
                    $course = $this->Courses->patchEntity($course, $data, [
                        'validate' => 'full',
                        'associated' => ['CourseBasePrices']
                    ]);

                } else {
                    $course = $this->Courses->patchEntity($course, $data, [
                    'associated' => ['CourseBasePrices']
                    ]);
                }
                
                // Start transaction
                $conn = $this->Courses->getConnection();
                $conn->begin();

                if (!$this->Courses->save($course)) {
                    return $this->response->withType('application/json')->withStringBody(json_encode([
                        'success' => false,
                        'errors' => $course->getErrors(),
                        'message' => 'Course validation failed.'
                    ]));
                }

                $courseId = $course->id;
                
                // update slug with course ID//
                $course->slug = $courseId.'-'.$course->slug;
                $this->Courses->save($course);

                $YogaStyleIds = !empty($data['yoga_style_id']) ? $data['yoga_style_id'] : [];
                if (!empty($YogaStyleIds)) {
                    foreach ($YogaStyleIds as $YogaStyleId) {
                        $courseYogaStyle = $this->CourseYogaStyles->newEmptyEntity();
                        $courseYogaStyle->course_id = $courseId;
                        $courseYogaStyle->yoga_style_id = $YogaStyleId;
                        $this->CourseYogaStyles->save($courseYogaStyle);
                    }
                }

                $SpecialNeedsIds = !empty($data['special_need_id']) ? $data['special_need_id'] : [];
                if (!empty($SpecialNeedsIds)) {
                    foreach ($SpecialNeedsIds as $SpecialNeedId) {
                        $courseSpecialNeed = $this->CourseSpecialNeeds->newEmptyEntity();
                        $courseSpecialNeed->course_id = $courseId;
                        $courseSpecialNeed->special_need_id = $SpecialNeedId;
                        $this->CourseSpecialNeeds->save($courseSpecialNeed);
                    }
                }

                $modalities = !empty($data['modality']) ? $data['modality'] : [];
                if (!empty($modalities)) {
                    foreach ($modalities as $modalityId) {
                        $courseModality = $this->CourseModalities->newEmptyEntity();
                        $courseModality->course_id = $courseId;
                        $courseModality->modality_id = $modalityId;
                        $this->CourseModalities->save($courseSpecialNeed);
                    }
                }
                
                if(!empty($data['course_batches_json'])){
                    $batchErrors = $this->saveCourseBatches($courseId, $data);
                    if (!empty($batchErrors)) {
                        return $this->response->withType('application/json')->withStringBody(json_encode([
                            'success' => false,
                            'type' => 'batches',
                            'message' => 'Some batch records failed to save. Please check the input.',
                            'errors' => $batchErrors
                        ]));
                    }
                }

                if(!empty($data['course_addons_json'])){
                    $addonErrors = $this->saveCourseAddons($courseId, $data);
                    if (!empty($addonErrors)) {
                        return $this->response->withType('application/json')->withStringBody(json_encode([
                            'success' => false,
                            'type' => 'addons',
                            'message' => 'Some addon records failed to save. Please check the input.',
                            'errors' => $addonErrors
                        ]));
                    }
                }
                
                if(!empty($data['teachers_ids']) || !empty($data['teachers_new'])){
                    $this->CourseTeachers->deleteAll(['course_id' => $courseId]);
                    $teacherErrors = $this->saveCourseTeachers($courseId, $data['teachers_ids'] ?? [],$data['teachers_new'] ?? []);
                    if (!empty($teacherErrors)) {
                        return $this->response->withType('application/json')->withStringBody(json_encode([
                            'success' => false,
                            'type' => 'teachers',
                            'message' => 'Some teacher records could not be saved.',
                            'errors' => $teacherErrors
                        ]));
                    }
                } 
                
                // Handle new file uploads (both images and videos)
                $uploadedImages = !empty($this->request->getData('media')) ? $this->handleFileUploads('media') : [];
                $this->saveMedia($uploadedImages, $data, $courseId);

                // Commit transaction if all succeeded
                $conn->commit();
                
                $response = [
                    'success' => true,
                    'message' => 'The course has been saved.'
                ];
                return $this->response
                    ->withType('application/json')
                    ->withStringBody(json_encode($response));
            }

        } catch (\Exception $e) {
            $conn->rollback();
            //dd($e->getMessage());
            $response = [
                'success' => false,
                'message' => 'The course could not be saved. Please, try again.'
            ];
            return $this->response
                ->withType('application/json')
                ->withStringBody(json_encode($response));
        }


        $languages = Configure::read('Constants.Language');
        $currencies = $this->Currencies->getList();
   
        // Get the first key (currency ID)
        $firstCurrency = array_key_first($currencies);
        $countries = $this->Countries->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->where(['status' => 'A'])->toArray();
        $states = [];

        if (!empty($course->country_id)) {
            $statesAll = $this->States->getListByCountry($course->country_id);
            $states = [];

            if(!empty($statesAll)){
                foreach ($statesAll as $state) {
                    $states[$state->id] = $state->name;
                }
            }
           
        }

        $cities = [];
        if (!empty($course->state_id)) {
            $citiesAll = $this->Cities->getListByState($course->state_id);
            $cities = [];

            if(!empty($citiesAll)){
                foreach ($citiesAll as $city) {
                    $cities[$city->id] = $city->name;
                }
            }
        }

        $meta_robot_options = Configure::read('Constants.META_ROBOT_OPTIONS');
      
        $this->set(compact('course', 'coursetypes', 'specialNeeds', 'countries', 'states', 'cities', 'partners', 'currencies', 'firstCurrency', 'languages', 'yogaStyles', 'level', 'addonTypes', 'modalities', 'teachersList', 'meta_robot_options', 'techniques'));
    }

    public function saveMedia($uploadedImages, $data, $courseId){
        
        if (!empty($uploadedImages)) {
            foreach ($uploadedImages as $mediaData) {
                if($mediaData){
                    $CourseImage = $this->Courses->CourseGalleries->newEmptyEntity();
                    $CourseImage->course_id = $courseId;
                    $CourseImage->media = $mediaData['path'];
                    $CourseImage->media_type = $mediaData['type']; // 'image' or 'video'
                    $CourseImage->status = 'A';
                    $this->Courses->CourseGalleries->save($CourseImage);
                }
            }
        }

        if(!empty($data['video_url'])){
            foreach($data['video_url'] as $url){
                if($url){
                    $urlExists = $this->Courses->CourseGalleries->find()->where(['media' => $url, 'media_type' => 'url'])->count();
                    if($urlExists){
                        continue;
                    }
                    $CourseImage            = $this->Courses->CourseGalleries->newEmptyEntity();
                    $CourseImage->course_id = $courseId;
                    $CourseImage->media     = $url;
                    $CourseImage->media_type= 'url'; // 'image' or 'video'
                    $CourseImage->status    = 'A';
                    $this->Courses->CourseGalleries->save($CourseImage);
                }
              
            }
        }
    }

    public function saveCourseBatches($courseId, $data){
        // Handle course_batches_json
        $batchesJson = $data['course_batches_json'] ?? '[]';
        $batchData = json_decode($batchesJson, true);

        $CourseBatches = $this->CourseBatches;
        $CourseBatchPricing = $this->CourseBatchPricing;
        $errors = [];

        foreach ($batchData as $index => $batch) {
            
            $batchEntity = $CourseBatches->newEntity([
                'course_id'     => $courseId,
                'name'          => $batch['batch_name'] ?? '',
                'start_date'    => $batch['start_date'] ?? null,
                'end_date'      => $batch['end_date'] ?? null,
                'duration_details'=> $batch['duration_details'] ?? '',
                'start_time'    => $batch['start_time'] ?? null,
                'end_time'      => $batch['end_time'] ?? null,
                'capacity'      => $batch['capacity'] ?? null,
                'status'        => $batch['status'] ?? 'active',
            ]);
            
            if ($batchEntity->getErrors()) {
                $errors["batch_{$index}"] = $batchEntity->getErrors();
                continue; // Skip saving if batch has validation errors
            }

            if ($CourseBatches->save($batchEntity)) {
                $batchId = $batchEntity->id;

                // Save pricing (multiple currencies)
                $prices = $batch['prices'] ?? [];
                foreach ($prices as $price) {
                    if (!empty($price['currency']) && !empty($price['amount'])) {
                        $priceEntity = $CourseBatchPricing->newEntity([
                            'batch_id' => $batchId,
                            'currency_id'     => 1,
                            'price'           => $price['amount'],
                            'hourly_rate'     => $price['hourly_rate'],
                            'advance_value'   => $price['advance_value'],
                            'has_installments' => $price['has_installments'],
                        ]);
        
                        $CourseBatchPricing->save($priceEntity);
                    }
                }
            }
        }
        return $errors;
    }

    public function saveCourseAddons($courseId, $data){
        // Save Addons
        $addonJson = $data['course_addons_json'] ?? '[]';
        $addons = json_decode($addonJson, true);

        $CourseAddons = $this->fetchTable('CourseAddons');
        $AddonPricing = $this->fetchTable('CourseAddonPricing');
        $errors = [];
        
        foreach ($addons as $index => $addon) {
            // Skip if no name
            if (empty($addon['master_data_id']) && empty($addon['custom_name'])) {
                continue;
            }

            $addonEntity = $CourseAddons->newEntity([
                'course_id'      => $courseId,
                'type'           => $addon['type'] ?? '',
                'master_data_id' => !empty($addon['master_data_id']) ? (int)$addon['master_data_id']: null,
                'custom_name'    => $addon['custom_name'] ?? '',
                'custom_category'=> $addon['custom_category'] ?? '',
                'total_slots'   => !empty($addon['total_slots']) ? (int)$addon['total_slots'] : 0,
                'sort_order'    => $addon['sort_order'] ?? 0,
            ]);
            
            if ($addonEntity->getErrors()) {
                $errors["addon_{$index}"] = $addonEntity->getErrors();
                continue; // Skip saving if batch has validation errors
            }

            if ($CourseAddons->save($addonEntity)) {
                $addonId = $addonEntity->id;

                $prices = $addon['prices'] ?? [];
                $pricingEntities = [];
                foreach ($prices as $price) {
                    if (!empty($price['currency']) && !empty($price['amount'])) {
                        $pricingEntities[] = $AddonPricing->newEntity([
                            'addon_id' => $addonId,
                            'currency_id' => $price['currency'],
                            'price'       => $price['amount']
                        ]);
                        // $AddonPricing->save($priceEntity);
                    }
                }
                $AddonPricing->saveMany($pricingEntities);
            }
        }
        return $errors;
    }

    public function saveCourseTeachers($courseId, $teachers, $newTeachers)
    {
        $courseTeachersData = [];
        $errors = [];

        // Existing teacher references
        if (!empty($teachers)) {
            foreach ($teachers as $id) {
                if (str_starts_with($id, 't_')) {
                    $teacherId = (int)str_replace('t_', '', $id);
                    $courseTeachersData[] = [
                        'course_id' => $courseId,
                        'teacher_id' => $teacherId
                    ];

                } elseif (str_starts_with($id, 'p_')) {
                    $p_id = (int)str_replace('p_', '', $id);
                    $teacherExists = $this->Teachers->findByPartnerId($p_id);

                    if (!$teacherExists) {
                        $teacher = $this->Teachers->newEntity(['partner_id' => $p_id]);

                        if ($this->Teachers->save($teacher)) {
                            $courseTeachersData[] = [
                                'course_id' => $courseId,
                                'teacher_id' => $teacher->id
                            ];
                        } else {
                            $errors[] = ['partner_id_' . $p_id => $teacher->getErrors()];
                        }
                    } else {
                        $courseTeachersData[] = [
                            'course_id' => $courseId,
                            'teacher_id' => $teacherExists->id
                        ];
                    }
                }
            }
        }

        // New teachers
        if (!empty($newTeachers)) {
            foreach ($newTeachers as $index => $data) {
                $emailExists = $this->Teachers->findByEmail($data['email']);
                $phoneExists = $this->Teachers->findByPhone($data['phone']);
               
                if ($emailExists) {
                    $errors["new_teacher_{$index}"] = ['duplicate' => 'Email already exists.'];
                    continue;
                }

                if ($phoneExists) {
                    $errors["new_teacher_{$index}"] = ['duplicate' => 'Phone already exists.'];
                    continue;
                }

                $teacher = $this->Teachers->newEmptyEntity();
                $teacher = $this->Teachers->patchEntity($teacher, $data);

                // Handle image file
                if (isset($data['image_file']) && $data['image_file'] instanceof \Laminas\Diactoros\UploadedFile) {
                    if ($data['image_file']->getError() === UPLOAD_ERR_OK) {
                        $image = $data['image_file'];
                        $fileName = preg_replace('/[^A-Za-z0-9_\-\.]/', '_', trim($image->getClientFilename()));
                        if (!empty($fileName)) {
                            $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                            $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                            $filePath = Configure::read('Settings.Course');
                            $folderPath = $uploadFolder . 'teachers/' . $filePath;
                            $ext = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                            $newFileName = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                            $uploadResult = $this->Media->uploadMedia($image, $newFileName, $folderPath);

                            if ($uploadResult === 'Success') {
                                $teacher->image = $filePath . $newFileName;
                            } else {
                                $errors["new_teacher_{$index}"]['image_file'] = 'Image upload failed';
                            }
                        }
                    }
                }

                if ($teacher->hasErrors()) {
                    $errors["new_teacher_{$index}"] = $teacher->getErrors();
                    continue;
                }

                if ($this->Teachers->save($teacher)) {
                    $courseTeachersData[] = [
                        'course_id' => $courseId,
                        'teacher_id' => $teacher->id
                    ];
                } else {
                    $errors["new_teacher_{$index}"] = ['save' => 'Failed to save teacher'];
                }
            }
        }

        // Save course_teacher mappings
        if (!empty($courseTeachersData)) {
            $courseTeacherEntities = $this->CourseTeachers->newEntities($courseTeachersData);
            if (!$this->CourseTeachers->saveMany($courseTeacherEntities)) {
                $errors[] = ['course_teachers' => 'Failed to save course teacher mappings'];
            }
        }

        // Final return: just errors, let controller decide how to respond
        return $errors;
    }

    
    public function masterDataByType($type){
        $this->request->allowMethod(['get']);
        $this->viewBuilder()->disableAutoLayout();
        $this->autoRender = false;

        $addons = $this->MasterData->findByType($type);
   
        $results = [];
        foreach ($addons as $id => $title) {
            $results[] = [
                'id' => $id,
                'name' => $title
            ];
        }

        return $this->response
            ->withType('application/json')
            ->withStringBody(json_encode($results));
    }

    public function edit($id = null)
    {
        $course = $this->Courses->get($id, contain: [
            'CourseTypes',
            'CourseGalleries',
            'Partners',
            'CourseYogaStyles',
            'CourseSpecialNeeds',
            'Countries',
            'States',
            'Cities',
            'Teachers',
            'CourseBatches',
            'CourseAddons.CourseAddonPricing.Currencies',
            'CourseAddons.MasterData',
            'CourseBasePrices.Currencies',
            // 'CourseTeachers.Teachers',
            'CourseModalities'
        ]);
    //  dd($course);
        // Populate arrays for multi-selects
        $addonTypes = $this->MasterData->findByType('addon');
        
        $modalities = $this->Modalities->selectInputOptions();

        $yogaStyles   = $this->MasterData->findByType('yoga_style')->toArray();
        $specialNeeds = $this->MasterData->findByType('special_need')->toArray();
        $levelOptions = $this->MasterData->findByType('level')->toArray();
        $techniques   = $this->MasterData->findByType('technique')->toArray();
        $yogaStyles['n/a'] = 'N/A';
        $specialNeeds['n/a'] = 'N/A';   
        $techniques['n/a'] = 'N/A';

        $partnerTypeTeacher = $this->fetchTable('PartnerTypes')->getByName('Teachers');
        
        $languages = Configure::read('Constants.Language');
       
        $teachersList = $this->getTeachersList();
        
        // $selectedTeacherIds = collection($course->teachers)->extract('id')->toArray();
        // $teachersJson = json_encode($course->teachers);

        $selectedTeacherIds = [];
        $teachersJson = [];

        $selectedTeacherIds = [];

        $courseTeachers = $this->CourseTeachers->find()
            ->where(['course_id' => $course->id])
            ->contain(['Teachers'])
            ->all();

        foreach ($courseTeachers as $ct) {
            $teacher = $ct->teacher;
            if ($teacher->partner_id) {
                $selectedTeacherIds[] = 'p_' . $teacher->partner_id;
            } else {
                $selectedTeacherIds[] = 't_' . $teacher->id;
            }
        }
 
        if ($this->request->is(['ajax'])) {
            $data = $this->request->getData();
   
            if(!empty($data['course_base_prices'])){
                foreach ($data['course_base_prices'] as $index => $bp) {
                    if(!$bp['name']){
                        unset($data['course_base_prices'][$index]);
                        continue;
                    }
                }
            }
            // upload banner image //
            $oldImage = $course->banner_image;
            $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');

            if (isset($data['banner_image']) && $data['banner_image'] instanceof \Laminas\Diactoros\UploadedFile) {
                $fileName = preg_replace('/[^A-Za-z0-9_\-\.]/', '_', trim($data['banner_image']->getClientFilename()));

                if (!empty($fileName)) {
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));

                    $filePath = Configure::read('Settings.Course');
                    $folderPath = $uploadFolder . $filePath;
                    $ext = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                    $newFileName = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                    $uploadResult = $this->Media->uploadMedia($data['banner_image'], $newFileName, $folderPath);

                    if ($uploadResult !== 'Success') {
                         $response = [
                            'success' => false,
                            'message' => 'Banner Image could not be uploaded. Please, try again.'
                        ];
                        return $this->response
                            ->withType('application/json')
                            ->withStringBody(json_encode($response));
                    } else {
                        if ($oldImage) {
                            $this->Media->deleteMedia($uploadFolder . $oldImage);
                        }
                        // Set the new image path
                        $data['banner_image'] = $filePath . $newFileName;
                    }
                } else {
                    $data['banner_image'] = $oldImage;
                }
            }
            $courseId = $course->id;
            $slugName = '';

            if (!empty($data['slug_name'])) {
                $slugName = $this->slugify($data['slug_name']);
            } elseif (!empty($data['slug'])) {
                $providedSlug = trim($data['slug']);
                $slugName = $this->slugify(preg_replace('/^\d+-/', '', $providedSlug));
            } else {
                $slugName = $this->slugify($data['name']);
            }
            $newSlugWithId = $courseId . '-' . $slugName;
            if (!preg_match('/^' . $courseId . '-/', $newSlugWithId)) {
                throw new \Exception('Invalid slug format. Course ID mismatch detected.');
            }
            $existingCourse = $this->Courses->find()
                ->where(['slug' => $newSlugWithId, 'id !=' => $courseId])
                ->first();

            if ($existingCourse) {
                throw new \Exception('Slug already exists for another course.');
            }

            // Handle old_url for SEO purposes 
            $oldSlug = $course->slug;

            if ($oldSlug && $newSlugWithId !== $oldSlug) {
                $oldUrls = [];
                if (!empty($course->old_url)) {
                    $oldUrls = array_map('trim', explode(',', $course->old_url));
                }

                // Add the old slug if it's not already in the list
                if (!in_array($oldSlug, $oldUrls)) {
                    $oldUrls[] = $oldSlug;
                }

                // Keep only the last 5 old URLs to prevent the field from growing too large
                $oldUrls = array_slice($oldUrls, -5);
                $data['old_url'] = implode(', ', $oldUrls);
            }
            $data['slug'] = $newSlugWithId;

            if (!empty($data['techniques']) && is_array($data['techniques']) && !(in_array('n/a', $data['techniques']))) {
                $data['techniques'] = implode(',', $data['techniques']);
            }
            
            if (!empty($data['level']) && is_array($data['level'])) {
                $data['level'] = implode(',', $data['level']);
            }
           
            $deletedMediaIds = !empty($data['deletedImages']) ? json_decode($data['deletedImages'], true) : [];
            unset($data['deletedImages']);
          
            // to update course base prices //
            $course = $this->Courses->patchEntity($course, $data, [
                'associated' => ['CourseBasePrices']
            ]);
 
            if ($this->Courses->save($course)) {
                $courseId = $course->id;

                if (!empty($data['yoga_style_id'])) {
                    $this->Courses->CourseYogaStyles->deleteAll(['course_id' => $courseId]);
                    foreach ($data['yoga_style_id'] as $YogaStyleId) {
                        if($YogaStyleId == 'n/a'){
                            continue; // Skip N/A style
                        }
                        $courseYogaStyle = $this->Courses->CourseYogaStyles->newEmptyEntity();
                        $courseYogaStyle->course_id = $courseId;
                        $courseYogaStyle->yoga_style_id = $YogaStyleId;
                        $this->Courses->CourseYogaStyles->save($courseYogaStyle);
                    }
                }

                if (!empty($data['special_need_id'])) {
                    $this->Courses->CourseSpecialNeeds->deleteAll(['course_id' => $courseId]);
                    foreach ($data['special_need_id'] as $specialNeedId) {
                        if($specialNeedId == 'n/a'){
                            continue; // Skip N/A style
                        }
                        $courseSpecialNeed = $this->Courses->CourseSpecialNeeds->newEmptyEntity();
                        $courseSpecialNeed->course_id = $courseId;
                        $courseSpecialNeed->special_need_id = $specialNeedId;
                        $this->Courses->CourseSpecialNeeds->save($courseSpecialNeed);
                    }
                }

                $modalities = !empty($data['modality']) ? $data['modality'] : [];
            
                if (!empty($modalities)) {
                    $existingModalityIds = $this->CourseModalities->getExistingIds($courseId);
                  
                    $this->CourseModalities->deleteAll([
                        'course_id' => $courseId,
                        'modality_id NOT IN' => $modalities
                    ]);

                    foreach ($modalities as $modalityId) {
                        if (!in_array($modalityId, $existingModalityIds)) {
                            $courseModality = $this->CourseModalities->newEmptyEntity();
                            $courseModality->course_id = $courseId;
                            $courseModality->modality_id = (int) $modalityId;
                            $this->CourseModalities->save($courseModality);
                        }
                    }
                }

                if(!empty($data['course_batches_json'])){
                    $this->CourseBatches->deleteAll(['course_id' => $courseId]);
                    $batchErrors = $this->saveCourseBatches($courseId, $data);
                    if (!empty($batchErrors)) {
                        return $this->response->withType('application/json')->withStringBody(json_encode([
                            'success' => false,
                            'type' => 'batches',
                            'message' => 'Some batch records failed to save. Please check the input.',
                            'errors' => $batchErrors
                        ]));
                    }
                }

                if(!empty($data['course_addons_json'])){
                    $this->fetchTable('CourseAddons')->deleteAll(['course_id' => $courseId]);
                    $addonErrors= $this->saveCourseAddons($courseId, $data);

                    if (!empty($addonErrors)) {
                        return $this->response->withType('application/json')->withStringBody(json_encode([
                            'success' => false,
                            'type' => 'addons',
                            'message' => 'Some addon records failed to save. Please check the input.',
                            'errors' => $addonErrors
                        ]));
                    }
                }
               
                if(!empty($data['teachers_ids']) || !empty($data['teachers_new'])){
                    $this->CourseTeachers->deleteAll(['course_id' => $courseId]);
                    $teacherErrors = $this->saveCourseTeachers($courseId, $data['teachers_ids'] ?? [],$data['teachers_new'] ?? []);

                    if (!empty($teacherErrors)) {
                        return $this->response->withType('application/json')->withStringBody(json_encode([
                            'success' => false,
                            'type' => 'teachers',
                            'message' => 'Some teacher records could not be saved.',
                            'errors' => $teacherErrors
                        ]));
                    }
                }

                // Delete selected media from DB and folder
                if (!empty($deletedMediaIds)) {
                    foreach ($deletedMediaIds as $mediaId) {
                        $mediaEntity = $this->Courses->CourseGalleries->get($mediaId);
                        if (!empty($mediaEntity)) {
                            $this->Media->deleteMedia($uploadFolder . $mediaEntity->media);
                            $this->Courses->CourseGalleries->delete($mediaEntity);
                        }
                    }
                }

                // Handle uploaded files
                $uploadedImages = !empty($this->request->getData('media')) ? $this->handleFileUploads('media') : [];
                $this->saveMedia($uploadedImages, $data, $courseId);

                 $response = [
                    'success' => true,
                    'message' => 'The course has been updated.'
                ];
                return $this->response
                    ->withType('application/json')
                    ->withStringBody(json_encode($response));

            } else {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'success' => false,
                    'errors' => $course->getErrors(),
                    'message' => 'Course validation failed.'
                ]));
                return $this->response
                    ->withType('application/json')
                    ->withStringBody(json_encode($response));
            }
        }
        $coursetypes = $this->Courses->CourseTypes->find('list')->where(['CourseTypes.status' => 'A'])->all();
        $partners = $this->Partners->find('list')->where(['Partners.status' => 'A'])->order(['Partners.name' => 'ASC'])->all();

        $selectedYogaStyles = [];
        if (!empty($course->course_yoga_styles)) {
            foreach ($course->course_yoga_styles as $style) {
                $selectedYogaStyles[] = $style->yoga_style_id;
            }
        }
     
        if(empty($selectedYogaStyles)){
            $selectedYogaStyles[] = 'n/a'; // Add N/A if no styles selected
        }

        $selectedSpecialNeeds = [];
        if (!empty($course->course_special_needs)) {
            foreach ($course->course_special_needs as $sn) {
                $selectedSpecialNeeds[] = $sn->special_need_id;
            }
        }
        if(empty($selectedSpecialNeeds)){
            $selectedSpecialNeeds[] = 'n/a'; // Add N/A if no styles selected
        }

        $selectedTechniques = [];
        if (!empty($course->techniques)) {
            $stechniques = explode(',', $course->techniques);
            foreach ($stechniques as $val) {
                $selectedTechniques[] = $val;
            }
        }

        if(empty($selectedTechniques)){
            $selectedTechniques[] = 'n/a'; // Add N/A if no styles selected
        }

        $selectedLevels = [];
        if (!empty($course->level)) {
            $levels = explode(',', $course->level);
            foreach ($levels as $val) {
                $selectedLevels[] = $val;
            }
        }
     
        $selectedModalities = [];
    
        if (!empty($course->course_modalities)) {
            foreach($course->course_modalities as $modality){
                $selectedModalities[] = $modality->modality_id;
            }
        }
 
        $mediaImages = [];
        $videoLinks = [];
      
        if (!empty($course->course_galleries)) {
            $webroot = $this->request->getAttribute('webroot');

            foreach ($course->course_galleries as $media) {
                if($media->media_type == 'url'){
                    $videoLinks[] = [
                        'id' => $media->id,
                        'displayname' => basename($media->media),
                        'path' => $media->media,
                        'url' => '',
                    ];
                } else {
                    $pathUrl = $this->Media->displayImage($media->media);
                    $imageUrl = $webroot . $pathUrl;
                    $mediaImages[] = [
                        'id' => $media->id,
                        'displayname' => basename($media->media),
                        'path' => $media->media,
                        'url' => $imageUrl,
                    ];
                }
             
            }
        }

        $currencies = $this->Currencies->find('list')
            ->where(['Currencies.status' => 'A'])
            ->toArray();

        $firstCurrency = array_key_first($currencies);
        $media = $this->Media;

        $countries = $this->Countries->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->where(['status' => 'A'])->toArray();

        $states = [];
        if (!empty($course->country_id)) {
            $statesAll = $this->States->getListByCountry($course->country_id);
            $states = [];
            if(!empty($statesAll)){
                foreach ($statesAll as $state) {
                    $states[$state->id] = $state->name;
                }
            }
        }

        $cities = [];
        if (!empty($course->state_id)) {
            $citiesAll = $this->Cities->getListByState($course->state_id);
            $cities = [];
            if(!empty($citiesAll)){
                foreach ($citiesAll as $city) {
                    $cities[$city->id] = $city->name;
                }
            }
        }

        // get saved batch details //
        $batchData = [];
   
        foreach ($course->course_batches as $batch) {
            $batchData[] = [
                'batch_name'   => $batch->name,
                'start_date'   => $batch->start_date ? $batch->start_date->format('Y-m-d') : '',
                'end_date'     => $batch->end_date ? $batch->end_date->format('Y-m-d') : '',
                'duration_details'=> $batch->duration_details,
                'start_time'   => $batch->start_time,
                'end_time'     => $batch->end_time,
                'status'       => $batch->status,
                'capacity'     => $batch->capacity,
               // 'prices'       => []
            ];

            // Include pricing if available
            // if (!empty($batch->course_batch_prices)) {
            //     foreach ($batch->course_batch_prices as $price) {
            //         $batchData[count($batchData) - 1]['prices'][] = [
            //             'currency' => $price->currency,
            //             'amount'   => $price->amount,
            //         ];
            //     }
            // } else {
            //     $batchData[count($batchData) - 1]['prices'][] = ['currency' => '', 'amount' => ''];
            // }
        }

        $batchJson = json_encode($batchData, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT);

        // get saved base price details //
        $basePrices = [];
        foreach ($course->course_base_prices as $bp) {
            $basePrices[] = [
                'name'       => $bp->name,
                'currency_id'=> $bp->currency_id,
                'price'      => $bp->price,
                'total_count'=> $bp->total_count,
                'sort_order' => $bp->sort_order,
                'status'     => $bp->status
            ];
        }
        $basePricesJson = json_encode($basePrices, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT);

        // get course addons //
        $addonsData = [];
        foreach ($course->course_addons as $addon) {
            $addonData = [
                'course_id' => $addon->course_id,
                'master_data_id'=> $addon->master_data_id,
                'custom_name'   => $addon->custom_name,
                'custom_category'=> $addon->custom_category,
                'total_slots'    => $addon->total_slots,
                'sort_order'     => $addon->sort_order,
                'status'         => $addon->status,
                'prices'         => []
            ];
          
            $masterData = $this->MasterData->findById($addon->master_data_id);
            $addonData['type'] = !empty($masterData) ? $masterData->type : '';
           
            foreach ($addon->course_addon_pricing as $price) {
                $addonData['prices'][] = [
                    'currency' => $price->currency_id,
                    'amount' => $price->price,
                    'status' => $price->status,
                ];
            }

            // if no prices, add empty one
            if (empty($addonData['prices'])) {
                $addonData['prices'][] = ['currency' => '', 'amount' => '', 'status' => ''];
            }

            $addonsData[] = $addonData;
        }

        $addonsJson = json_encode($addonsData, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT);
        $meta_robot_options = Configure::read('Constants.META_ROBOT_OPTIONS');

        $this->set(compact('course', 'selectedYogaStyles', 'selectedSpecialNeeds', 'specialNeeds', 'countries', 'states', 'cities', 'yogaStyles', 'coursetypes', 'partners', 'currencies', 'firstCurrency', 'techniques', 'languages', 'media', 'mediaImages', 'levelOptions', 'modalities', 'selectedLevels', 'teachersList', 'selectedTeacherIds', 'teachersJson', 'batchJson', 'basePricesJson', 'addonTypes', 'addonsJson', 'meta_robot_options', 'selectedModalities', 'videoLinks', 'selectedTechniques'));
    }
    
    private function handleFileUploads($fileInputName = '')
    {
        $files = $this->request->getData($fileInputName);
        $uploadedFiles = [];
        if (!empty($files) && is_array($files)) {
            foreach ($files as $file) {
                if ($file->getError() === UPLOAD_ERR_OK) {
                    $fileName = preg_replace('/[^A-Za-z0-9_\-\.]/', '_', trim($file->getClientFilename()));
                    if (!empty($fileName)) {
                        $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                        $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                        $filePath = Configure::read('Settings.Course');
                        $folderPath = $uploadFolder . $filePath;
                        $ext = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                        $newFileName = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                        $uploadResult = $this->Media->uploadMedia($file, $newFileName, $folderPath);

                        if ($uploadResult !== 'Success') {
                            $this->Flash->error(__($fileInputName . ' ' . $fileName . ' could not be uploaded.'));
                        } else {
                            $mediaType = in_array($ext, ['mp4', 'mov', 'avi', 'mkv']) ? 'video' : 'image';
                            $uploadedFiles[] = [
                                'path' => $filePath . $newFileName,
                                'type' => $mediaType
                            ];
                        }
                    }
                }
            }
        }
        return $uploadedFiles;
    }
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $response = ['success' => false, 'message' => __('The course could not be deleted. Please, try again.')];
        try {
            $record = $this->Courses->get($id);
            $record->status = 'D'; // Mark as deleted
            if ($this->Courses->save($record)) {
                $response = ['success' => true, 'message' => __('The course has been marked as deleted.')];
            }
        } catch (\Exception $e) {
            $response['message'] = $e->getMessage();
        }

        if ($this->request->is('ajax')) {
            return $this->response
                ->withType('application/json')
                ->withStringBody(json_encode($response));
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message'], ['key' => 'success']);
            } else {
                $this->Flash->error($response['message'], ['key' => 'error']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    // this function is not using 1-08-2025 GP//
    public function filterSearch()
    {
        $this->request->allowMethod(['get']);
        $status = $this->request->getQuery('filterStatus');
        if ($this->request->is('ajax')) {
            $query = $this->Courses->find(contain: [
                'CourseTypes',
            ])->orderBy(['Courses.id' => 'DESC']);

            if ($status) {
                $query->where(['Courses.status' => $status]);
            } else {
                $query->where(['Courses.status !=' => 'D']);
            }
            $statusMap = [
                'A' => ['label' => __('Active'), 'class' => 'badge-outline col-green'],
                'I' => ['label' => __('Inactive'), 'class' => 'badge-outline col-red'],
                'D' => ['label' => __('Deleted'), 'class' => 'badge-outline col-red']
            ];

            $courses = [];
            $i = 1;
            foreach ($query as $course) {
                $status = $statusMap[$course->status] ?? ['label' => __('Unknown'), 'class' => 'badge-outline col-red'];
                $statusToggleUrl = Router::url(['controller' => 'Courses', 'action' => 'approve', $course->id], true);
                $approveClass = ' btn-sm approve approve ms-2';
                $approveTitle = $course->status === 'A' ? 'Mark as Inactive' : 'Mark as Active';
                $courses[] = [
                    'actions' => '<a href="' . Router::url(['controller' => 'Courses', 'action' => 'view', $course->id], true) . '" class="btn-view" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a> ' .
                        ($course->status !== 'D' ?
                            '<a href="' . Router::url(['controller' => 'Courses', 'action' => 'edit', $course->id], true) . '" class="btn-edit" data-toggle="tooltip" title="Edit"><i class="fas fa-edit m-r-10"></i></a>' .
                            ' <a href="javascript:void(0);" class="delete-button" data-id="' . $course->id . '" data-toggle="tooltip" title="Delete"
                                data-bs-toggle="modal" data-bs-target="#exampleModalCenter"> <i class="far fa-trash-alt"></i>
                            </a>'
                            : '') .
                        '<a href="' . $statusToggleUrl . '" class="' . $approveClass . '" data-toggle="tooltip" title="' . $approveTitle . '"><i class="fas fa-check"></i></a>',
                    'id' => $course->id,
                    'name' => !empty($course->name) ? h(strlen($course->name) > 40 ? substr($course->name, 0, 40) . '...' : $course->name) : 'None',
                    'course_type' => !empty($course->course_type->name) ? h($course->course_type->name) : 'None',
                    'price' => !empty(h($course->price)) ? h($course->currency->name) . ' ' . h($course->price) : 'None',
                    'venue_address' => !empty($course->venue_address) ? h(strlen($course->venue_address) > 40 ? substr($course->venue_address, 0, 40) . '...' : $course->venue_address) : 'None',
                    'status' => '<div class="badge-outline ' . h($status['class']) . '">' . h($status['label']) . '</div>'
                    
                ];
                $i++;
            }
            $this->set([
                'courses' => $courses,
                '_serialize' => ['courses'],
            ]);

            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['data' => $courses]));
        }
        return null;
    }
    public function exportCourses()
    {
        $query = $this->Courses->find()->contain([
            'CourseTypes',
            'Partners'
        ]);
       
        $query->where(['Courses.status !=' => 'D'])
            ->order(['Courses.id' => 'DESC']);
        $courses = $query->all();

        $filename = "courses_list_" . date('Y-m-d') . ".csv";
        $this->response = $this->response->withDownload($filename);
        $this->response = $this->response->withType('text/csv');
        $csvData = fopen('php://output', 'w');
        fputcsv($csvData, ['Id', 'Name', 'Slug', 'Language', 'Address', 'Status']);
   
        foreach ($courses as $course) {

            // Check if course type and partner are set
            $type = !empty($course->course_type) ? $course->course_type->name : '';
            $offeredBy = !empty($course->partner) ? $course->partner->name : '';
           
            fputcsv($csvData, [
                $course->id,
                $course->name,
                $course->slug,
                $course->language,
                $course->address,
                $course->status
            ]);
        }

        fclose($csvData);
        return $this->response;
    }
    public function approve($id)
    {
        $course = $this->Courses->get($id);
        // Toggle status: If Active -> set to Inactive, else set to Active
        $course->status = ($course->status === 'A') ? 'I' : 'A';

        if ($this->Courses->save($course)) {

            $this->Flash->success(__('Course status updated successfully.'), [
                'key' => 'success'
            ]);
        } else {
            $this->Flash->error(__('Unable to update course status. Please try again.'), [
                'key' => 'error'
            ]);
        }
        return $this->redirect(['action' => 'index']);
    }
    public function checkDuplicateCourseName()
    {
        $this->request->allowMethod(['post']);
        $name = $this->request->getData('name');
        $currentId = $this->request->getData('id');
        $isDuplicate = false;
        $query = $this->Courses->find()
            ->where(['name' => $name])
            ->where(['status !=' => 'D']);

        if (!empty($currentId)) {
            $query->where(['id !=' => $currentId]);
        }
        // print_r($query->count());
        if ($query->count() > 0) {
            $isDuplicate = true;
        }
        $exists = $query->count();
        return $this->response->withType('application/json')
            ->withStringBody(json_encode(['isDuplicate' => $isDuplicate]));
    }
    public function checkPhpConfig()
    {
        $this->autoRender = false;

        // Define expected values
        $expectedConfig = [
            'post_max_size' => '500M',
            'upload_max_filesize' => '500M',
            'max_execution_time' => '300',
            'memory_limit' => '256M'
        ];

        // Get current values
        $currentConfig = [
            'post_max_size' => ini_get('post_max_size'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'max_execution_time' => ini_get('max_execution_time'),
            'memory_limit' => ini_get('memory_limit')
        ];

        // Function to convert PHP size strings to bytes
        $toBytes = function ($val) {
            $val = trim($val);
            $last = strtolower($val[strlen($val) - 1]);
            $val = (int)$val;
            switch ($last) {
                case 'g':
                    $val *= 1024;
                case 'm':
                    $val *= 1024;
                case 'k':
                    $val *= 1024;
            }
            return $val;
        };

        // Compare and prepare results
        $results = [];
        $allCorrect = true;

        foreach ($expectedConfig as $key => $expectedValue) {
            $currentValue = $currentConfig[$key];
            $isCorrect = false;

            // For memory-related settings, compare in bytes
            if (in_array($key, ['post_max_size', 'upload_max_filesize', 'memory_limit'])) {
                $isCorrect = $toBytes($currentValue) >= $toBytes($expectedValue);
            } else {
                // For numeric settings, compare as integers
                $isCorrect = (int)$currentValue >= (int)$expectedValue;
            }

            $results[$key] = [
                'current' => $currentValue,
                'expected' => $expectedValue,
                'status' => $isCorrect ? 'OK' : 'Warning',
            ];

            if (!$isCorrect) {
                $allCorrect = false;
            }
        }

        // Add PHP version info
        $results['php_version'] = [
            'current' => PHP_VERSION,
            'status' => 'Info'
        ];

        // Format output as HTML
        $output = '<div style="font-family: Arial, sans-serif; padding: 20px;">';
        $output .= '<h2>PHP Configuration Check</h2>';

        foreach ($results as $setting => $info) {
            $color = $info['status'] === 'OK' ? '#4CAF50' : ($info['status'] === 'Warning' ? '#FF9800' : '#2196F3');

            $output .= '<div style="margin-bottom: 10px; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">';
            $output .= "<strong>{$setting}:</strong><br>";

            if (isset($info['expected'])) {
                $output .= "Current Value: <span style='color: {$color}'>{$info['current']}</span><br>";
                $output .= "Expected Value: {$info['expected']}<br>";
            } else {
                $output .= "Value: <span style='color: {$color}'>{$info['current']}</span><br>";
            }

            $output .= "Status: <span style='color: {$color}'>{$info['status']}</span>";
            $output .= '</div>';
        }

        if (!$allCorrect) {
            $output .= '<div style="margin-top: 20px; padding: 10px; background-color: #FFF3E0; border-left: 4px solid #FF9800; border-radius: 4px;">';
            $output .= '<strong>⚠️ Warning:</strong> Some configuration values are below recommended settings.<br>';
            $output .= 'To update these values, modify your php.ini file or contact your server administrator.';
            $output .= '</div>';
        }

        $output .= '</div>';

        // Send response
        return $this->response
            ->withType('html')
            ->withStringBody($output);
    }
    // Contry based states
    public function getStates()
    {
        $this->request->allowMethod(['ajax']);
        $countryId = $this->request->getQuery('country_id');

        $states = $this->States->find()
        ->select(['id', 'name'])
        ->where(['country_id' => $countryId])
        ->orderAsc('name')
        ->all()
        ->toArray();

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode(['states' => $states]));
        return $this->response;
    }

    // State based cities
    public function getCities()
    {
        $this->request->allowMethod(['ajax']);
        $stateId = $this->request->getQuery('state_id');

        $cities = $this->Cities->find()
            ->select(['id', 'name'])
            ->where(['state_id' => $stateId])
            ->orderAsc('name')
            ->all()
            ->toArray();

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode(['cities' => $cities]));
        return $this->response;
    }

    public function getLocalities()
    {
        $this->request->allowMethod(['ajax']);
        $cityId = $this->request->getQuery('city_id');
        $localities = $this->fetchTable('Localities')->getListByCity($cityId);

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode(['localities' => $localities]));
        return $this->response;
    }
    
    public function checkTeacherEmail()
    {
        $this->request->allowMethod(['get']);
        $email = $this->request->getQuery('email');
        
        $exists = false;

        if (!empty($email)) {
            $exists = $this->Teachers->exists(['email' => $email]);
        }
        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode(['exists' => $exists]));
        return $this->response;
    }

    public function deleteVideolink($id=null){
        $this->request->allowMethod(['post', 'delete']);

        $video = $this->Courses->CourseGalleries->get($id);
     
        if ($this->Courses->CourseGalleries->delete($video)) {
            $response = ['success' => true, 'message' => 'Video Link deleted'];
        } else {
            $response = ['success' => false, 'message' => 'Delete failed'];
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode($response));

    }

    /**
     * Convert string to URL-friendly slug
     */
    private function slugify($string)
    {
        return strtolower(str_replace([' ', '_'], '-', trim($string)));
    }
}
