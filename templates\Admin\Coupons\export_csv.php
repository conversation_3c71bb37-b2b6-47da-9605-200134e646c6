<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Coupon> $coupons
 */

// CSV Headers
$headers = [
    'ID',
    'Code',
    'Title',
    'Description',
    'Discount Type',
    'Discount Value',
    'Currency Code',
    'Min Cart Value',
    'Max Discount Value',
    'Usage Limit',
    'Per User Limit',
    'Start Date',
    'End Date',
    'Status',
    'Is Active',
    'Created At',
    'Updated At'
];

echo implode(',', array_map(function($header) {
    return '"' . str_replace('"', '""', $header) . '"';
}, $headers)) . "\n";

// CSV Data
foreach ($coupons as $coupon) {
    $row = [
        $coupon->id,
        $coupon->code,
        $coupon->title ?: '',
        $coupon->description ?: '',
        $coupon->discount_type,
        $coupon->discount_value ?: '',
        $coupon->currency_code ?: '',
        $coupon->min_cart_value ?: '',
        $coupon->max_discount_value ?: '',
        $coupon->usage_limit ?: '',
        $coupon->per_user_limit ?: '',
        $coupon->start_date ? $coupon->start_date->format('Y-m-d H:i:s') : '',
        $coupon->end_date ? $coupon->end_date->format('Y-m-d H:i:s') : '',
        $coupon->status,
        $coupon->is_active ? 'Yes' : 'No',
        $coupon->created_at->format('Y-m-d H:i:s'),
        $coupon->updated_at->format('Y-m-d H:i:s')
    ];
    
    echo implode(',', array_map(function($field) {
        return '"' . str_replace('"', '""', (string)$field) . '"';
    }, $row)) . "\n";
}
?>
