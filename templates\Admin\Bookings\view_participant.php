<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\BookingItem $bookingItem
 * @var array $participantAddons
 */
?>

<style>
    .compact-booking-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px 25px;
        border-radius: 12px;
        margin-bottom: 25px;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
    }

    .booking-number {
        font-size: 2rem;
        font-weight: bold;
        color: white;
    }

    .header-info h6 {
        color: white;
        font-weight: 600;
        margin-bottom: 2px;
    }

    .header-info small {
        color: rgba(255, 255, 255, 0.8);
        font-size: 12px;
    }

    .status-badge-header {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 12px;
        letter-spacing: 0.5px;
        border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .status-badge-header.status-active {
        background: rgba(40, 167, 69, 0.9);
        color: white;
        border-color: rgba(40, 167, 69, 0.5);
    }

    .status-badge-header.status-inactive {
        background: rgba(220, 53, 69, 0.9);
        color: white;
        border-color: rgba(220, 53, 69, 0.5);
    }

    .info-card {
        background: white;
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 3px 15px rgba(0,0,0,0.08);
        border: 1px solid #e9ecef;
    }

    .info-card h5 {
        color: #2c3e50;
        font-weight: 700;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #f8f9fa;
    }

    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f8f9fa;
    }

    .info-row:last-child {
        border-bottom: none;
    }

    .info-label {
        font-weight: 600;
        color: #6c757d;
        flex: 0 0 40%;
    }

    .info-value {
        flex: 1;
        text-align: right;
        font-weight: 500;
    }

    .addon-card {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        border-left: 4px solid #28a745;
    }

    .price-highlight {
        font-size: 1.5rem;
        font-weight: bold;
        color: #28a745;
    }

    .status-badge {
        padding: 6px 12px;
        border-radius: 15px;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.75rem;
    }

    .status-active { background: #d4edda; color: #155724; }
    .status-inactive { background: #f8d7da; color: #721c24; }
</style>

<div class="main-content">
    <section class="section">
        <div class="section-body1">
            <div class="container-fluid">
                <!-- Participant Header -->
                <div class="compact-booking-header">
                    <!-- <div class="row align-items-center">
                        <div class="col-md-2">
                            <div class="booking-number">
                                <i class="fas fa-user"></i>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="header-info">
                                <h6><?= h($bookingItem->first_name . ' ' . $bookingItem->last_name) ?></h6>
                                <small><?= h($bookingItem->email) ?></small>
                                <br>
                                <small><i class="fas fa-phone me-1"></i><?= h($bookingItem->phone) ?></small>
                            </div>
                        </div>
                        <div class="col-md-2 text-center">
                            <div class="header-info">
                                <h6>Age</h6>
                                <small><?= h($bookingItem->age) ?> years</small>
                            </div>
                        </div>
                        <div class="col-md-2 text-end">
                            <div class="header-info">
                                <h6><?= h($bookingItem->currency) ?> <?= number_format($bookingItem->grand_total ?? 0, 2) ?></h6>
                                <small>Total Amount</small>
                                <br>
                                <span class="status-badge-header status-<?= ($bookingItem->status ?? 'active') ?>">
                                    <?= h(ucfirst($bookingItem->status ?? 'active')) ?>
                                </span>
                            </div>
                        </div>
                    </div> -->

                    <!-- Navigation -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb mb-0" style="background: transparent;">
                                    <li class="breadcrumb-item">
                                        <a href="<?= $this->Url->build(['action' => 'index']) ?>" style="color: rgba(255,255,255,0.8);">
                                            <i class="fas fa-list me-1"></i>All Bookings
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item">
                                        <a href="<?= $this->Url->build(['action' => 'view', $bookingItem->booking->id]) ?>" style="color: rgba(255,255,255,0.8);">
                                            <i class="fas fa-eye me-1"></i>Booking #<?= h($bookingItem->booking->id) ?>
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item active" style="color: white;">
                                        <i class="fas fa-user me-1"></i>Participant Details
                                    </li>
                                </ol>
                            </nav>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Participant Information -->
                    <div class="col-lg-6">
                        <div class="info-card">
                            <h5><i class="fas fa-user me-2"></i>Personal Information</h5>
                            <!-- <div class="info-row">
                                <span class="info-label">Title:</span>
                                <span class="info-value"><?= h($bookingItem->title) ?></span>
                            </div> -->
                            <div class="info-row">
                                <span class="info-label">Full Name:</span>
                                <span class="info-value"><strong><?= h($bookingItem->title .' '.$bookingItem->first_name . ' ' . $bookingItem->last_name) ?></strong></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Email:</span>
                                <span class="info-value"><?= h($bookingItem->email) ?></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Phone:</span>
                                <span class="info-value"><?= h($bookingItem->phone) ?></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Age:</span>
                                <span class="info-value"><?= h($bookingItem->age) ?> years</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Food Preference:</span>
                                <span class="info-value">
                                    <span class="badge badge-info"><?= h($bookingItem->food) ?></span>
                                </span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Residency:</span>
                                <span class="info-value"><?= h($bookingItem->residency) ?></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Status:</span>
                                <span class="info-value">
                                    <span class="status-badge <?= ($bookingItem->status ?? 'active') == 'active' ? 'status-active' : 'status-inactive' ?>">
                                        <?= h(ucfirst($bookingItem->status ?? 'active')) ?>
                                    </span>
                                </span>
                            </div>
                        </div>

                        <!-- Pricing Information -->
                       

                    <!-- Addons -->
                    <?php if (!empty($participantAddons)): ?>
                    <div class="info-card">
                        <h5><i class="fas fa-plus-circle me-2"></i>Add-ons & Services</h5>
                        <?php foreach ($participantAddons as $addon): ?>
                        <div class="addon-card">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h6 class="mb-1"><?= h($addon->addon_name) ?></h6>
                                    <small class="text-muted">Quantity: <?= h($addon->quantity) ?></small>
                                </div>
                                <div class="col-md-4 text-end">
                                    <strong><?= h($bookingItem->currency) ?> <?= number_format($addon->total_price, 2) ?></strong>
                                    <br>
                                    <!-- <small class="text-muted">@ <?= number_format($addon->addon_price, 2) ?> each</small> -->
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Booking Information -->
                <div class="col-lg-6">
                     <div class="info-card">
                            <h5><i class="fas fa-money-bill-wave me-2"></i>Pricing Details</h5>
                            <div class="info-row">
                                <span class="info-label">Base Price:</span>
                                <span class="info-value"><?= h($bookingItem->base_price_name) ?></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Amount:</span>
                                <span class="info-value"><strong><?= h($bookingItem->base_price_currency) ?> <?= number_format($bookingItem->base_price_amount, 2) ?></strong></span>
                            </div>
                            <!-- <div class="info-row">
                                <span class="info-label">Currency:</span>
                                <span class="info-value"><?= h($bookingItem->currency) ?></span>
                            </div> -->
                            <!-- <?php if ($bookingItem->exchange_rate): ?>
                            <div class="info-row">
                                <span class="info-label">Exchange Rate:</span>
                                <span class="info-value"><?= h($bookingItem->exchange_rate) ?></span>
                            </div>
                            <?php endif; ?> -->
                            <!-- <div class="info-row">
                                <span class="info-label">Sub Total:</span>
                                <span class="info-value"><?= h($bookingItem->currency) ?> <?= number_format($bookingItem->sub_total ?? 0, 2) ?></span>
                            </div> -->
                             <div class="info-row">
                                <span class="info-label">Addons Amount:</span>
                                <span class="info-value"><?= h($bookingItem->currency) ?> <?= number_format($bookingItem->sub_total ?? 0, 2) ?></span>
                            </div>
                            <?php if ($bookingItem->discount_value): ?>
                            <div class="info-row">
                                <span class="info-label">Discount:</span>
                                <span class="info-value text-danger">-<?= h($bookingItem->currency) ?> <?= number_format($bookingItem->discount_value, 2) ?></span>
                            </div>
                            <?php endif; ?>
                            <?php if ($bookingItem->tax_amount): ?>
                            <div class="info-row">
                                <span class="info-label">Tax (<?= h($bookingItem->tax_rate) ?>%):</span>
                                <span class="info-value"><?= h($bookingItem->currency) ?> <?= number_format($bookingItem->tax_amount, 2) ?></span>
                            </div>
                            <?php endif; ?>
                            <div class="info-row" style="border-top: 2px solid #28a745; padding-top: 15px; margin-top: 10px;">
                                <span class="info-label"><strong>Grand Total:</strong></span>
                                <span class="info-value"><strong class="text-success" style="font-size: 1.2rem;"><?= h($bookingItem->base_price_currency) ?>
                                <!-- <?= number_format($bookingItem->grand_total ?? 0, 2) ?> -->
                                 <?= number_format($bookingItem->base_price_amount + $bookingItem->tax_amount - $bookingItem->discount_value ?? 0, 2) ?> 
                                </strong></span>
                            </div>
                        </div>
                    <!-- <div class="info-card">
                        <h5><i class="fas fa-calendar-alt me-2"></i>Booking Information</h5>
                        <div class="info-row">
                            <span class="info-label">Booking ID:</span>
                            <span class="info-value"><strong>#<?= h($bookingItem->booking->id) ?></strong></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Course:</span>
                            <span class="info-value"><?= h($bookingItem->booking->course->title ?? 'N/A') ?></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Partner:</span>
                            <span class="info-value"><?= h($bookingItem->booking->partner->business_name ?? 'N/A') ?></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Booking Date:</span>
                            <span class="info-value"><?= h($bookingItem->booking->booking_date->format('d M Y')) ?></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Customer:</span>
                            <span class="info-value">
                                <?php if ($bookingItem->booking->customer && $bookingItem->booking->customer->user): ?>
                                    <strong><?= h($bookingItem->booking->customer->user->first_name . ' ' . $bookingItem->booking->customer->user->last_name) ?></strong>
                                    <br>
                                    <small class="text-muted"><?= h($bookingItem->booking->customer->user->email) ?></small>
                                <?php else: ?>
                                    N/A
                                <?php endif; ?>
                            </span>
                        </div>

                        <div class="mt-4">
                            <a href="<?= $this->Url->build(['action' => 'view', $bookingItem->booking->id]) ?>"
                               class="btn btn-primary w-100">
                                <i class="fas fa-eye me-2"></i>View Full Booking
                            </a>
                        </div>
                    </div> -->

                    <!-- Timestamps -->
                    <!-- <div class="info-card">
                        <h5><i class="fas fa-clock me-2"></i>Timestamps</h5>
                        <div class="info-row">
                            <span class="info-label">Created:</span>
                            <span class="info-value"><?= h($bookingItem->created_at->format('d M Y H:i')) ?></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Modified:</span>
                            <span class="info-value"><?= h($bookingItem->modified_at->format('d M Y H:i')) ?></span>
                        </div>
                    </div> -->
                </div>
            </div>
        </div>
    </div>
</section>
</div>
