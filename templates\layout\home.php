<?php
/**
 * CakePHP(tm) : Rapid Development Framework (https://cakephp.org)
 * Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * @copyright     Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 * @link          https://cakephp.org CakePHP(tm) Project
 * @since         0.10.0
 * @license       https://opensource.org/licenses/mit-license.php MIT License
 * @var \App\View\AppView $this
 */

$cakeDescription = 'Homevilla-Yoga';
?>
<!DOCTYPE html>
<html>
<head>
    <?= $this->Html->charset() ?>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <meta name="keyword" content="">
    <meta name="description" content="">
    <meta name="csrf-token" content="<?= $this->request->getAttribute('csrfToken') ?>">
    <title>
        <?= $this->fetch('title') ?>
    </title>
    <link rel="icon" href="<?= $this->Url->build('/img/yoga-logo.png') ?>">
    <?= $this->fetch('meta') ?>
    
    <script src="https://unpkg.com/@tailwindcss/browser@4"></script>
        <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.14.8/dist/cdn.min.js"></script>
        <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.14.8/dist/cdn.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        <script src="<?= $this->Url->webroot('js/data.js') ?>"></script>
        <link rel="stylesheet" href="<?= $this->Url->webroot('css/responsive.css') ?>">
        <link rel="stylesheet" href="<?= $this->Url->webroot('css/app.min.css') ?>">
        <link rel="stylesheet" href="<?= $this->Url->webroot('css/home.css') ?>">
        <link rel="stylesheet" href="<?= $this->Url->webroot('css/course.css') ?>">
        <link rel="icon" href="<?= $this->Url->webroot('img/yoga-logo.png') ?>">
        <link href='https://fonts.googleapis.com/css?family=Open Sans' rel='stylesheet'>
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Afacad:ital,wght@0,400..700;1,400..700&family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&family=DM+Serif+Text:ital@0;1&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Outfit:wght@100..900&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/lipis/flag-icons@7.2.3/css/flag-icons.min.css"/>
        <?= $this->fetch('meta') ?>
        <?= $this->fetch('css') ?>
        <?= $this->fetch('script') ?>
        <script>
            var baseUrl = '<?= \Cake\Routing\Router::url("/", true); ?>';
            var lang = '<?= $this->request->getParam('lang') ?>';
        </script>
</head>
<body>
<div class="header-top md:px-10 lg:px-25 xl:px-40 2xl:px-60">
    <header>
        <?= $this->element('frontend/navbar'); ?>
        <div class="sm:block lg:flex lg:items-center lg:flext-start lg:justify-center 2xl:justify-start header-div">
            <div class="header-content">
                <img src="<?= $this->Url->webroot('img/leaf.png') ?>" alt="Leaf Image" class="yoga-leaf desktop-view" />
                <img src="<?= $this->Url->webroot('img/mobile-leaf.png') ?>" alt="Leaf Image" class="yoga-leaf mobile-view" />
                <h1>Find your Yoga in India</h1>
                <p>Yoga Courses, Classes, Centers, Teachers anywhere in India</p>
            </div>
            <div class="header-img">
                <img src="<?= $this->Url->webroot('img/yoga.png') ?>" class="img" alt="yoga image">
            </div>
        </div>
    </header>
</div>
    <?= $this->fetch('content') ?>
    <?= $this->element('frontend/footer'); ?>
</body>
</html>
