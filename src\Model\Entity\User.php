<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * User Entity
 *
 * @property int $id
 * @property string $first_name
 * @property string $last_name
 * @property string $full_name
 * @property string $country_code
 * @property string $mobile
 * @property string|null $profile_pic
 * @property string $email
 * @property string $password
 * @property string $user_type
 * @property int|null $role_id
 * @property string|null $social_provider
 * @property string|null $socialID
 * @property string|null $otp_code
 * @property \Cake\I18n\DateTime|null $otp_expires_at
 * @property string $status
 * @property string|null $remember_token
 * @property string|null $reset_token
 * @property \Cake\I18n\DateTime|null $reset_token_expiry
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Cake\I18n\DateTime|null $last_login_at
 * @property \Cake\I18n\DateTime $created_at
 * @property \Cake\I18n\DateTime $modified_at
 * @property \Cake\I18n\DateTime|null $deleted_at
 */
class User extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'first_name' => true,
        'last_name' => true,
        'full_name' => true,
        'country_code' => true,
        'mobile' => true,
        'profile_pic' => true,
        'gender'=>true,
        'dob'=>true,
        'email' => true,
        'address' => true,
        'password' => true,
        'user_type' => true,
        'partner_id' => true,
        'role_id' => true,
        'social_provider' => true,
        'socialID' => true,
        'otp_code' => true,
        'otp_expires_at' => true,
        'status' => true,
        'remember_token' => true,
        'reset_token' => true,
        'reset_token_expiry' => true,
        'created_by' => true,
        'updated_by' => true,
        'last_login_at' => true,
        'created_at' => true,
        'modified_at' => true,
        'deleted_at' => true,
    ];

    /**
     * Fields that are excluded from JSON versions of the entity.
     *
     * @var list<string>
     */
    protected array $_hidden = [
        'password',
    ];
}
