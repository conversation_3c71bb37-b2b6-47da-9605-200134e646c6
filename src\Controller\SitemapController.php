<?php
namespace App\Controller;

use Cake\Routing\Router;
use Cake\Http\Response;
use Cake\I18n\DateTime;

class SitemapController extends AppController
{
    protected $CoursesTable;
    protected $PartnersTable;
    protected $CourseTypesTable;
    protected $SpecialNeedsTable;
    protected $PartnerTypesTable;
    protected $RegionsTable;
    protected $YogaStylesTable;
    protected $MasterDataTable;
    protected $CourseYogaStylesTable;
    protected $CountriesTable;
    protected $StatesTable;
    protected $CitiesTable;
    protected $LocalitiesTable;
    protected $ModalitiesTable;
    protected $CourseModalitiesTable;

    public function initialize(): void
    {
        parent::initialize();
        $this->CoursesTable     = $this->fetchTable('Courses');
        $this->PartnersTable    = $this->fetchTable('Partners');
        $this->CourseTypesTable = $this->fetchTable('CourseTypes');
        $this->SpecialNeedsTable= $this->fetchTable('SpecialNeeds');
        $this->PartnerTypesTable= $this->fetchTable('PartnerTypes');
        $this->RegionsTable     = $this->fetchTable('Regions');
        $this->YogaStylesTable = $this->fetchTable('YogaStyles');
        $this->MasterDataTable = $this->fetchTable('MasterData');
        $this->CourseYogaStylesTable = $this->fetchTable('CourseYogaStyles');
        $this->CountriesTable    = $this->fetchTable('Countries');
        $this->StatesTable       = $this->fetchTable('States');
        $this->CitiesTable       = $this->fetchTable('Cities');
        $this->LocalitiesTable   = $this->fetchTable('Localities');
        $this->ModalitiesTable   = $this->fetchTable('Modalities');
        $this->CourseModalitiesTable = $this->fetchTable('CourseModalities');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
        // Allow access to index action without login
        $this->Authentication->addUnauthenticatedActions(['index','sitemapIndex','listPartners','detailCourses','detailPartners','listCourses']);
    }

    

   public function sitemapIndex()
    {
        
        try {
            $this->response = $this->response->withType('xml');
            $this->viewBuilder()->disableAutoLayout();

            $baseUrl = $this->request->scheme() . '://' . $this->request->host();

            $sitemaps = [
                [
                    'loc' => $baseUrl . '/sitemap/detail-courses.xml',
                    'lastmod' => date('Y-m-d\TH:i:s\Z')
                ],
                [
                    'loc' => $baseUrl . '/sitemap/list-courses.xml',
                    'lastmod' => date('Y-m-d\TH:i:s\Z')
                ],
                [
                    'loc' => $baseUrl . '/sitemap/detail-partners.xml',
                    'lastmod' => date('Y-m-d\TH:i:s\Z')
                ],
                [
                    'loc' => $baseUrl . '/sitemap/list-partners.xml',
                    'lastmod' => date('Y-m-d\TH:i:s\Z')
                ]
            ];

            $this->set(compact('sitemaps'));
            $this->render('sitemap_index');
        } catch (\Exception $e) {
            // Return a simple XML response if there's an error
            $this->response = $this->response->withType('xml');
            $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
            $xml .= '<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
            $xml .= '<!-- Error: ' . $e->getMessage() . ' -->' . "\n";
            $xml .= '</sitemapindex>';
            $this->response = $this->response->withStringBody($xml);
            return $this->response;
        }
    }

    /**
     * Legacy index method - redirects to sitemap index
     */
    public function index()
    {
        return $this->redirect('/sitemap-index.xml');
    }


    /**
     * Generate sitemap for course detail pages
     * URL: /sitemap/detail-courses.xml
     */
    public function detailCourses()
    {
        try {
            $this->response = $this->response->withType('xml');
            $this->viewBuilder()->disableAutoLayout();

            $lang = 'en'; // Default language
            $urls = [];

            // Get all active courses with location data and modalities
            $courses = $this->CoursesTable->find()
                ->contain(['Countries', 'States', 'Cities', 'Partners', 'Modalities','Localities'])
                ->where(['Courses.status' => 'A'])
                ->limit(100) // Limit for testing
                ->all();
          

            foreach ($courses as $course) {
                $courseModalities = $this->getCourseModalityInfo($course);
                $slug = $this->slugify($course->slug);

                // Generate URLs based on modality rules
                if ($courseModalities['isOnlyOnlineVOD']) {
                    // Online-VOD only: /en/yoga-courses/video/12-[yoga-center-name-slug]
                    // $centerSlug = !empty($course->partner) ? $this->slugify($course->partner->slug) : 'center';
                    $url = Router::url("/{$lang}/yoga-courses/video/{$slug}", true);

                    $urls[] = [
                        'loc' => $url,
                        'lastmod' => $course->modified_at ? $course->modified_at->format('Y-m-d') : date('Y-m-d'),
                        'changefreq' => 'weekly',
                        'priority' => '0.9'
                    ];
                } elseif ($courseModalities['isOnlyOnlineLive']) {
                    // Online-Live only: /en/yoga-courses/online/12-[yoga-center-name-slug]
                    $centerSlug = !empty($course->partner) ? $this->slugify($course->partner->slug) : 'center';
                    $url = Router::url("/{$lang}/yoga-courses/online/{$slug}", true);

                    $urls[] = [
                        'loc' => $url,
                        'lastmod' => $course->modified_at ? $course->modified_at->format('Y-m-d') : date('Y-m-d'),
                        'changefreq' => 'weekly',
                        'priority' => '0.9'
                    ];
                } else {
                    // On-site or hybrid courses: use location-based URLs
                    if (!empty($course->country) && !empty($course->state) && !empty($course->city)) {
                        $country = $this->slugify($course->country->name);
                        $region = $this->getRegionByState($course->state_id);
                        $state = $this->slugify($course->state->name);
                        $city = $this->slugify($course->city->name);
                        $locality = !empty($course->locality) ? '/' . $this->slugify($course->locality->name) : '';
                        $url = Router::url("/{$lang}/yoga-courses/{$country}/{$region}/{$state}/{$city}{$locality}/{$slug}", true);
                        // if (!empty($course->locality) && ($course->locality_id != 0)) {  
                        //     $locality = $this->slugify($course->locality->name);
                        //     // URL with locality
                        //     $url = Router::url("/{$lang}/yoga-courses/{$country}/{$region}/{$state}/{$city}/{$locality}/{$slug}", true);
                        // } else {
                        //     // URL without locality
                        //     $url = Router::url("/{$lang}/yoga-courses/{$country}/{$region}/{$state}/{$city}/{$slug}", true);
                        // }
                        // Detail page URL pattern: /en/yoga-courses/india/south-india/kerala/kovalam/3-[yoga-course-title-slug]
                        // $url = Router::url("/{$lang}/yoga-courses/{$country}/{$region}/{$state}/{$city}/{$locality}/{$slug}", true);

                        $urls[] = [
                            'loc' => $url,
                            'lastmod' => $course->modified_at ? $course->modified_at->format('Y-m-d') : date('Y-m-d'),
                            'changefreq' => 'weekly',
                            'priority' => '0.9'
                        ];
                    }
                }
            }

            $this->set(compact('urls'));
            $this->render('sitemap');
        } catch (\Exception $e) {
            // Return a simple XML response if there's an error
            $this->response = $this->response->withType('xml');
            $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
            $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
            $xml .= '<!-- Error: ' . $e->getMessage() . ' -->' . "\n";
            $xml .= '</urlset>';
            $this->response = $this->response->withStringBody($xml);
            return $this->response;
        }
    }

    /**
     * Generate sitemap for course list pages
     * URL: /sitemaps/sitemap_en_list_courses.xml
     */
    public function listCourses()
    {
        $this->response = $this->response->withType('xml');
        $this->viewBuilder()->disableAutoLayout();

        $lang = 'en';
        $urls = [];

        // Generate modality-based list pages
        $this->generateModalityBasedUrls($urls, $lang);

        // Generate location-based list pages that have at least 1 course
        $this->generateLocationBasedUrls($urls, 'yoga-courses', $lang, 'courses');

        // Generate course type based URLs
        $this->generateCourseTypeUrls($urls, $lang);

        // Generate yoga style based URLs
        $this->generateYogaStyleUrls($urls, $lang);

        $this->set(compact('urls'));
        $this->render('sitemap');
    }

    /**
     * Generate sitemap for partner detail pages
     * URL: /sitemaps/sitemap_en_detail_partners.xml
     */
    public function detailPartners()
    {
        
        $this->response = $this->response->withType('xml');
        $this->viewBuilder()->disableAutoLayout();

        $lang = 'en';
        $urls = [];

        // Get all active partners with location data
        $partners = $this->PartnersTable->find()
            ->contain(['Countries', 'States', 'Cities'])
            ->where(['Partners.status' => 'A'])
            ->all();

        foreach ($partners as $partner) {
            if (!empty($partner->country) && !empty($partner->state) && !empty($partner->city)) {
                $country = $this->slugify($partner->country->name);
                $region = $this->getRegionByState($partner->state_id);
                $state = $this->slugify($partner->state->name);
                $city = $this->slugify($partner->city->name);
                //$slug = $partner->id . '-' . $this->slugify($partner->slug);
                $slug =  $this->slugify($partner->slug);

                // Detail page URL pattern: /en/yoga-centers/india/south-india/kerala/kovalam/partner-slug
                $url = Router::url("/{$lang}/yoga-centers/{$country}/{$region}/{$state}/{$city}/{$slug}", true);

                $urls[] = [
                    'loc' => $url,
                    'lastmod' => $partner->modified_at->format('Y-m-d'),
                    'changefreq' => 'weekly',
                    'priority' => '0.8'
                ];
            }
        }

        $this->set(compact('urls'));
        $this->render('sitemap');
    }

    /**
     * Generate sitemap for partner list pages
     * URL: /sitemaps/sitemap_en_list_partners.xml
     */
    public function listPartners()
    {
        $this->response = $this->response->withType('xml');
        $this->viewBuilder()->disableAutoLayout();

        $lang = 'en';
        $urls = [];

        // Generate location-based list pages that have at least 1 partner
        $this->generateLocationBasedUrls($urls, 'yoga-centers', $lang, 'partners');

        // Generate partner type based URLs
        $this->generatePartnerTypeUrls($urls, $lang);

        $this->set(compact('urls'));
        $this->render('sitemap');
    }

    /**
     * Helper method to generate location-based URLs
     */
    private function generateLocationBasedUrls(&$urls, $type, $lang, $entityType)
    {
        // Get all countries with active entities
        $countries = $this->CountriesTable->find()
            ->contain(['States.Cities.Localities'])
            ->all();

        foreach ($countries as $country) {
            $countrySlug = $this->slugify($country->name);

            // Country level: /en/yoga-courses/india
            if ($this->hasEntitiesInLocation($entityType, ['country_id' => $country->id])) {
                $urls[] = [
                    'loc' => Router::url("/{$lang}/{$type}/{$countrySlug}", true),
                    'lastmod' => date('Y-m-d'),
                    'changefreq' => 'weekly',
                    'priority' => '0.7'
                ];
            }

            // Region level: /en/yoga-courses/india/north-india
            $regions = $this->RegionsTable->find()
                ->where(['country_id' => $country->id])
                ->all();

            foreach ($regions as $region) {
                $regionSlug = $this->slugify($region->name);

                if ($this->hasEntitiesInRegion($entityType, $region->id)) {
                    $urls[] = [
                        'loc' => Router::url("/{$lang}/{$type}/{$countrySlug}/{$regionSlug}", true),
                        'lastmod' => date('Y-m-d'),
                        'changefreq' => 'weekly',
                        'priority' => '0.7'
                    ];
                }

                // State level: /en/yoga-courses/india/south-india/kerala
                $states = $this->StatesTable->find()
                    ->where(['region_id' => $region->id])
                    ->all();

                foreach ($states as $state) {
                    $stateSlug = $this->slugify($state->name);

                    if ($this->hasEntitiesInLocation($entityType, ['state_id' => $state->id])) {
                        $urls[] = [
                            'loc' => Router::url("/{$lang}/{$type}/{$countrySlug}/{$regionSlug}/{$stateSlug}", true),
                            'lastmod' => date('Y-m-d'),
                            'changefreq' => 'weekly',
                            'priority' => '0.7'
                        ];
                    }

                    // City level: /en/yoga-courses/india/south-india/kerala/kovalam
                    $cities = $this->CitiesTable->find()
                        ->contain(['Localities'])
                        ->where(['state_id' => $state->id])
                        ->all();

                    foreach ($cities as $city) {
                        $citySlug = $this->slugify($city->name);

                        if ($this->hasEntitiesInLocation($entityType, ['city_id' => $city->id])) {
                            $urls[] = [
                                'loc' => Router::url("/{$lang}/{$type}/{$countrySlug}/{$regionSlug}/{$stateSlug}/{$citySlug}", true),
                                'lastmod' => date('Y-m-d'),
                                'changefreq' => 'weekly',
                                'priority' => '0.7'
                            ];
                        }

                        // Locality level: /en/yoga-courses/india/south-india/karnataka/bangalore/koramangala
                        foreach ($city->localities as $locality) {
                            $localitySlug = $this->slugify($locality->name);

                            // For localities, we check if there are entities in the city (since localities don't have direct foreign keys)
                            if ($this->hasEntitiesInLocation($entityType, ['city_id' => $city->id])) {
                                $urls[] = [
                                    'loc' => Router::url("/{$lang}/{$type}/{$countrySlug}/{$regionSlug}/{$stateSlug}/{$citySlug}/{$localitySlug}", true),
                                    'lastmod' => date('Y-m-d'),
                                    'changefreq' => 'weekly',
                                    'priority' => '0.6'
                                ];
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Generate course type based URLs
     */
    private function generateCourseTypeUrls(&$urls, $lang)
    {
        $courseTypes = $this->CourseTypesTable->find()->all();

        foreach ($courseTypes as $courseType) {
            $typeSlug = $courseType->slug;

            // Get all countries that have courses with this type
            $countries = $this->CountriesTable->find()
                ->matching('Courses', function ($q) use ($courseType) {
                    return $q->where(['Courses.course_type_id' => $courseType->id, 'Courses.status' => 'A']);
                })
                ->all();

            foreach ($countries as $country) {
                $countrySlug = $this->slugify($country->name);

                // Country + type: /en/yoga-courses/country+course-type
                // $urls[] = [
                //     'loc' => Router::url("/{$lang}/yoga-courses/{$countrySlug}+{$typeSlug}", true),
                //     'lastmod' => $courseType->modified_at->format('Y-m-d'),
                //     'changefreq' => 'weekly',
                //     'priority' => '0.8'
                // ];

                // Region + type combinations for this country
                $regions = $this->RegionsTable->find()
                    ->where(['country_id' => $country->id])
                    ->all();

                
            foreach ($regions as $region)
             {
                $regionSlug = $this->slugify($region->name);
                $regionLoc = Router::url("/{$lang}/yoga-courses/{$countrySlug}/{$regionSlug}+{$typeSlug}", true);

                // if (!isset($uniqueUrls[$regionLoc])) {
                //     $urls[] = [
                //         'loc' => $regionLoc,
                //         'lastmod' => $courseType->modified_at->format('Y-m-d'),
                //         'changefreq' => 'weekly',
                //         'priority' => '0.8'
                //     ];
                //     $uniqueUrls[$regionLoc] = true;
                // }

                // States in this region
                $states = $this->StatesTable->find()
                    ->where(['region_id' => $region->id])
                    ->all();

                foreach ($states as $state) {
                    $stateSlug = $this->slugify($state->name);

                    // Check if courses exist with this type in this state
                    $hasStateCourses = $this->CoursesTable->find()
                        ->where([
                            'state_id' => $state->id,
                            'course_type_id' => $courseType->id,
                            'Courses.status' => 'A'
                        ])
                        ->count() > 0;

                    if ($hasStateCourses) {
                        $stateLoc = Router::url("/{$lang}/yoga-courses/{$countrySlug}/{$regionSlug}/{$stateSlug}+{$typeSlug}", true);

                        if (!isset($uniqueUrls[$stateLoc])) {
                            $urls[] = [
                                'loc' => $stateLoc,
                                'lastmod' => $courseType->modified_at->format('Y-m-d'),
                                'changefreq' => 'weekly',
                                'priority' => '0.8'
                            ];
                            $uniqueUrls[$stateLoc] = true;
                        }
                    }
                }
            }
            }
        }
    }

    /**
     * Generate yoga style based URLs
     */

    private function generateYogaStyleUrls(&$urls, $lang)
    {
        // Get yoga styles from MasterData table
        $yogaStyles = $this->MasterDataTable->find()
            ->where(['type' => 'yoga_style', 'status' => 'A'])
            ->all();

        $uniqueUrls = []; // Store unique loc values

        foreach ($yogaStyles as $yogaStyle) {
            $styleSlug = $yogaStyle->slug;

            // Get all countries that have courses with this yoga style
            // Using course_yoga_styles junction table
            $countries = $this->CountriesTable->find()
                ->distinct(['Countries.id'])
                ->innerJoinWith('Courses', function ($q) {
                    return $q->where(['Courses.status' => 'A']);
                })
                ->innerJoinWith('Courses.CourseYogaStyles', function ($q) use ($yogaStyle) {
                    return $q->where(['CourseYogaStyles.yoga_style_id' => $yogaStyle->id]);
                })
                ->all();

            foreach ($countries as $country) {
                $countrySlug = $this->slugify($country->name);

                // $loc = Router::url("/{$lang}/yoga-courses/{$countrySlug}+{$styleSlug}", true);

                // // Add only if not already added
                // if (!isset($uniqueUrls[$loc])) {
                //     $lastmod = $yogaStyle->modified_at ? $yogaStyle->modified_at->format('Y-m-d') :
                //               ($yogaStyle->created_at ? $yogaStyle->created_at->format('Y-m-d') : date('Y-m-d'));

                //     $urls[] = [
                //         'loc' => $loc,
                //         'lastmod' => $lastmod,
                //         'changefreq' => 'monthly',
                //         'priority' => '0.7'
                //     ];
                //     $uniqueUrls[$loc] = true;
                // }

                $regions = $this->RegionsTable->find()
                    ->where(['country_id' => $country->id])
                    ->all();

                foreach ($regions as $region) {
                    $regionSlug = $this->slugify($region->name);

                    $hasRegionEntities = $this->CoursesTable->find()
                        ->matching('States', function ($q) use ($region) {
                            return $q->where(['States.region_id' => $region->id]);
                        })
                        ->matching('CourseYogaStyles', function ($q) use ($yogaStyle) {
                            return $q->where(['CourseYogaStyles.yoga_style_id' => $yogaStyle->id]);
                        })
                        ->where(['Courses.status' => 'A'])
                        ->count() > 0;

                    if ($hasRegionEntities) {
                        $loc = Router::url("/{$lang}/yoga-courses/{$countrySlug}/{$regionSlug}+{$styleSlug}", true);

                        if (!isset($uniqueUrls[$loc])) {
                            $lastmod = $yogaStyle->modified_at ? $yogaStyle->modified_at->format('Y-m-d') :
                                      ($yogaStyle->created_at ? $yogaStyle->created_at->format('Y-m-d') : date('Y-m-d'));

                            $urls[] = [
                                'loc' => $loc,
                                'lastmod' => $lastmod,
                                'changefreq' => 'monthly',
                                'priority' => '0.7'
                            ];
                            $uniqueUrls[$loc] = true;
                        }
                    }
                }
            }
        }
    }
    
    /**
     * Generate partner type based URLs
     */
    
    private function generatePartnerTypeUrls(&$urls, $lang)
    {
        $partnerTypes = $this->PartnerTypesTable->find()->all();
        $uniqueUrls = [];

        foreach ($partnerTypes as $partnerType) {
            $typeSlug = $partnerType->slug;

            // Get all countries that have partners with this type
            $countries = $this->CountriesTable->find()
                ->distinct(['Countries.id'])
                ->matching('Partners', function ($q) use ($partnerType) {
                    return $q->where([
                        'Partners.partner_type_id' => $partnerType->id,
                        'Partners.status' => 'A'
                    ]);
                })
                ->all();

            foreach ($countries as $country) {
                $countrySlug = $this->slugify($country->name);
                $countryUrl = Router::url("/{$lang}/yoga-centers/{$countrySlug}+{$typeSlug}", true);

                if (!isset($uniqueUrls[$countryUrl])) {
                    // $urls[] = [
                    //     'loc' => $countryUrl,
                    //     'lastmod' => $partnerType->modified_at->format('Y-m-d'),
                    //     'changefreq' => 'weekly',
                    //     'priority' => '0.8'
                    // ];
                    $uniqueUrls[$countryUrl] = true;
                }

                // Region + type combinations for this country
                $regions = $this->RegionsTable->find()
                    ->where(['country_id' => $country->id])
                    ->all();

                foreach ($regions as $region) {
                    $regionSlug = $this->slugify($region->name);

                    $hasRegionEntities = $this->PartnersTable->find()
                        ->matching('States', function ($q) use ($region) {
                            return $q->where(['States.region_id' => $region->id]);
                        })
                        ->where([
                            'partner_type_id' => $partnerType->id,
                            'Partners.status' => 'A'
                        ])
                        ->count() > 0;

                    if ($hasRegionEntities) {
                        $regionUrl = Router::url("/{$lang}/yoga-centers/{$countrySlug}/{$regionSlug}+{$typeSlug}", true);

                        if (!isset($uniqueUrls[$regionUrl])) {
                            // $urls[] = [
                            //     'loc' => $regionUrl,
                            //     'lastmod' => $partnerType->modified_at->format('Y-m-d'),
                            //     'changefreq' => 'weekly',
                            //     'priority' => '0.8'
                            // ];
                            $uniqueUrls[$regionUrl] = true;
                        }

                        // State + type combinations under region
                        $states = $this->StatesTable->find()
                            ->where(['region_id' => $region->id])
                            ->all();

                        foreach ($states as $state) {
                            $stateSlug = $this->slugify($state->name);

                            $hasStateEntities = $this->PartnersTable->find()
                                ->where([
                                    'state_id' => $state->id,
                                    'partner_type_id' => $partnerType->id,
                                    'Partners.status' => 'A'
                                ])
                                ->count() > 0;

                            if ($hasStateEntities) {
                                $stateUrl = Router::url("/{$lang}/yoga-centers/{$countrySlug}/{$regionSlug}/{$stateSlug}+{$typeSlug}", true);

                                if (!isset($uniqueUrls[$stateUrl])) {
                                    $urls[] = [
                                        'loc' => $stateUrl,
                                        'lastmod' => $partnerType->modified_at->format('Y-m-d'),
                                        'changefreq' => 'weekly',
                                        'priority' => '0.8'
                                    ];
                                    $uniqueUrls[$stateUrl] = true;
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    /**
     * Check if there are entities in a specific location
     */
    private function hasEntitiesInLocation($entityType, $conditions)
    {
        $table = $this->{ucfirst($entityType) . 'Table'};
        return $table->find()
            ->where(array_merge($conditions, [ucfirst($entityType) . '.status' => 'A']))
            ->count() > 0;
    }

    /**
     * Check if there are entities in a specific region
     */
    private function hasEntitiesInRegion($entityType, $regionId)
    {
        $table = $this->{ucfirst($entityType) . 'Table'};
        return $table->find()
            ->matching('States', function ($q) use ($regionId) {
                return $q->where(['States.region_id' => $regionId]);
            })
            ->where([ucfirst($entityType) . '.status' => 'A'])
            ->count() > 0;
    }

    /**
     * Get region by state ID
     */
    private function getRegionByState($stateId)
    {
       
        
        try {
            $state = $this->StatesTable->find()
                ->contain(['Regions'])
                ->where(['States.id' => $stateId])
                ->first();

            if ($state && !empty($state->region) && !empty($state->region->name)) {
                return $this->slugify($state->region->name);
            }
        } catch (\Exception $e) {
            error_log('Error getting region for state ' . $stateId . ': ' . $e->getMessage());
        }

       //  return 'unknown-region'; // fallback for states without regions
    }


    
    /**
     * Generate modality-based URLs for course lists
     * This includes courses that have Online-VOD or Online-Live modalities,
     * regardless of whether they also have on-site availability
     */
    private function generateModalityBasedUrls(&$urls, $lang)
    {
        // Check if there are courses with Online-VOD modality (including hybrid courses)
        $hasOnlineVOD = $this->CoursesTable->find()
            ->matching('Modalities', function ($q) {
                return $q->where(['Modalities.name' => 'Online VOD']);
            })
            ->where(['Courses.status' => 'A'])
            ->count() > 0;

        if ($hasOnlineVOD) {
            $urls[] = [
                'loc' => Router::url("/{$lang}/yoga-courses/video", true),
                'lastmod' => date('Y-m-d'),
                'changefreq' => 'weekly',
                'priority' => '0.8'
            ];
        }

        // Check if there are courses with Online-Live modality (including hybrid courses)
        $hasOnlineLive = $this->CoursesTable->find()
            ->matching('Modalities', function ($q) {
                return $q->where(['Modalities.name' => 'Online Live']);
            })
            ->where(['Courses.status' => 'A'])
            ->count() > 0;

        if ($hasOnlineLive) {
            $urls[] = [
                'loc' => Router::url("/{$lang}/yoga-courses/online", true),
                'lastmod' => date('Y-m-d'),
                'changefreq' => 'weekly',
                'priority' => '0.8'
            ];
        }
    }

    /**
     * Get course modality information
     */
    private function getCourseModalityInfo($course)
    {
        $modalityNames = [];
        if (!empty($course->modalities)) {
            $modalityNames = array_column($course->modalities, 'name');
        }

        $hasOnlineVOD = in_array('Online VOD', $modalityNames);
        $hasOnlineLive = in_array('Online Live', $modalityNames);
        $hasOnSite = in_array('On Site', $modalityNames);
        $hasHybrid = in_array('Hybrid', $modalityNames);

        return [
            'hasOnlineVOD' => $hasOnlineVOD,
            'hasOnlineLive' => $hasOnlineLive,
            'hasOnSite' => $hasOnSite,
            'hasHybrid' => $hasHybrid,
            'isOnlyOnlineVOD' => $hasOnlineVOD && !$hasOnlineLive && !$hasOnSite && !$hasHybrid,
            'isOnlyOnlineLive' => $hasOnlineLive && !$hasOnlineVOD && !$hasOnSite && !$hasHybrid,
            'modalityNames' => $modalityNames
        ];
    }

    /**
     * Convert string to URL-friendly slug
     */
    private function slugify($string)
    {
        return strtolower(str_replace([' ', '_'], '-', trim($string)));
    }
}
