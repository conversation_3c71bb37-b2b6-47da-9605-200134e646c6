<?php
/**
 * @var \App\View\AppView $this
 * @var string $title
 */

// $this->extend('/layout/profile_layout');
$this->assign('title', $title);
$this->assign('meta_desc', 'Your saved yoga courses and favorite programs');
$this->assign('keywords', 'bookimarks, favorites, saved courses, yoga');
$this->assign('activeMenuItem', 'wishlist');
?>
<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');


    .group-booking .page-title {
        color: #293148;
        font-size: 24px;
        font-weight: 700;
        text-align: center;
        font-family: "Open Sans", sans-serif;
    }

    .group-booking .cards .art {
        font-family: "Poppins", sans-serif;
        font-size: 10px;
        font-weight: 600;
    }

    .group-booking .cards .card-title {
        font-weight: 700;
        color: #293148;
        font-size: 14px;
        text-decoration: underline;
    }

    .group-booking .cards {
        border-top: 1px solid #D87A61 !important;
        border: 0px;
    }

    .group-booking .cards .date-content .date {
        font-size: 12px;
        font-style: italic;
        font-family: "Open Sans", sans-serif;
        font-weight: 600;
        color: #000000;
    }

    .group-booking .cards .upcoming {
        color: #BF0A30;
        font-size: 10px;
        font-style: italic;
        font-weight: 700;
        font-family: "Open Sans", sans-serif;
    }

    .group-booking .cards .card-button .enroll {
        border-bottom-left-radius: 10px !important;
        border-radius: 0px;
        font-size: 12px;
        font-weight: 600;
        font-family: "Open Sans", sans-serif;
    }

    .group-booking .cards .card-button .remove {
        border-bottom-right-radius: 10px !important;
        border-radius: 0px;
        font-size: 12px;
        font-weight: 600;
        font-family: "Open Sans", sans-serif;
    }

    @media only screen and (max-width: 700px) {}

    @media only screen and (max-width: 390px) {}
</style>
 <section class="group-booking grp-booking-body-content">

        <div class="mx-auto p-4">
            <h1 class="page-title text-2xl font-bold text-center mb-4">My Bookmarks</h1>

            <!-- Card 1 -->
            <div class=" cards border rounded-lg  mb-2 py-4">
                <div class="flex px-4">
                    <img src="https://media-cdn.tripadvisor.com/media/photo-s/0e/77/5a/1a/firecamp.jpg"
                        alt="Course Image" class="w-20 h-20 rounded-lg object-cover mr-4" />
                    <div>
                        <p class="text-sm text-[#D87A61] font-semibold art">@The Art of Living</p>
                        <h3 class="font-bold card-title text-lg text-[#293148]">200-Hour Yoga Teacher Training in India
                        </h3>
                    </div>
                </div>
                <div class="flex items-center text-sm text-gray-600 mt-1 date-content px-4 py-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M5.33398 1.33301V3.33301" stroke="#C45F44" stroke-miterlimit="10"
                            stroke-linecap="round" stroke-linejoin="round" />
                        <path d="M10.666 1.33301V3.33301" stroke="#C45F44" stroke-miterlimit="10" stroke-linecap="round"
                            stroke-linejoin="round" />
                        <path opacity="0.4" d="M2.33398 6.05957H13.6673" stroke="#C45F44" stroke-miterlimit="10"
                            stroke-linecap="round" stroke-linejoin="round" />
                        <path
                            d="M14.6673 12.6667C14.6673 13.1667 14.5273 13.64 14.2807 14.04C13.8207 14.8133 12.974 15.3333 12.0007 15.3333C11.3273 15.3333 10.714 15.0867 10.2473 14.6667C10.0407 14.4933 9.86065 14.28 9.72065 14.04C9.47399 13.64 9.33398 13.1667 9.33398 12.6667C9.33398 11.1933 10.5273 10 12.0007 10C12.8007 10 13.514 10.3533 14.0007 10.9067C14.414 11.38 14.6673 11.9933 14.6673 12.6667Z"
                            stroke="#C45F44" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                        <path d="M10.9609 12.667L11.6209 13.327L13.0409 12.0137" stroke="#C45F44" stroke-linecap="round"
                            stroke-linejoin="round" />
                        <path
                            d="M14 5.66634V10.9063C13.5133 10.353 12.8 9.99967 12 9.99967C10.5267 9.99967 9.33333 11.193 9.33333 12.6663C9.33333 13.1663 9.47333 13.6397 9.72 14.0397C9.86 14.2797 10.04 14.493 10.2467 14.6663H5.33333C3 14.6663 2 13.333 2 11.333V5.66634C2 3.66634 3 2.33301 5.33333 2.33301H10.6667C13 2.33301 14 3.66634 14 5.66634Z"
                            stroke="#C45F44" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                        <path opacity="0.4" d="M7.99764 9.13314H8.00363" stroke="#C45F44" stroke-linecap="round"
                            stroke-linejoin="round" />
                        <path opacity="0.4" d="M5.52889 9.13314H5.53488" stroke="#C45F44" stroke-linecap="round"
                            stroke-linejoin="round" />
                        <path opacity="0.4" d="M5.5293 11.1338H5.53528" stroke="#C45F44" stroke-linecap="round"
                            stroke-linejoin="round" />
                    </svg>
                    <span class="date pl-2">06 Mar 2025 - 06 Apr 2025</span>
                    <span class="ml-4 flex items-center date">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path
                                d="M8.00065 14.6663C11.6825 14.6663 14.6673 11.6816 14.6673 7.99967C14.6673 4.31778 11.6825 1.33301 8.00065 1.33301C4.31875 1.33301 1.33398 4.31778 1.33398 7.99967C1.33398 11.6816 4.31875 14.6663 8.00065 14.6663Z"
                                stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                            <g opacity="0.4">
                                <path d="M5.33372 2H6.00039C4.70039 5.89333 4.70039 10.1067 6.00039 14H5.33372"
                                    stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M10 2C11.3 5.89333 11.3 10.1067 10 14" stroke="#C45F44" stroke-linecap="round"
                                    stroke-linejoin="round" />
                                <path d="M2 10.6667V10C5.89333 11.3 10.1067 11.3 14 10V10.6667" stroke="#C45F44"
                                    stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M2 6.00039C5.89333 4.70039 10.1067 4.70039 14 6.00039" stroke="#C45F44"
                                    stroke-linecap="round" stroke-linejoin="round" />
                            </g>
                        </svg>
                        <span class="pl-2">English</span>
                    </span>
                </div>
                <p class="text-sm text-red-500  upcoming px-4">Upcoming Dates: 1 Aug | 2 Aug | <a href="#"
                        class="underline">View All</a></p>
                <div class="grid grid-cols-2 mt-4 gap-0 card-button">
                    <button class="bg-[#D87A61] text-[#000000] py-2 rounded enroll">View</button>
                    <button class="bg-[#ECCEC4] text-[#000000] py-2 rounded remove">Remove</button>
                </div>
            </div>

            <!-- Card 2 -->
            <!-- <div class=" cards border rounded-lg  mb-2 py-4">
                <div class="flex px-4">
                    <img src="https://media-cdn.tripadvisor.com/media/photo-s/0e/77/5a/1a/firecamp.jpg"
                        alt="Course Image" class="w-20 h-20 rounded-lg object-cover mr-4" />
                    <div>
                        <p class="text-sm text-[#D87A61] font-semibold art">@The Art of Living</p>
                        <h3 class="font-bold card-title text-lg text-[#293148]">200-Hour Yoga Teacher Training in India
                        </h3>
                    </div>
                </div>
                <div class="flex items-center text-sm text-gray-600 mt-1 date-content px-4 py-2 justify-between">
                    <div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M5.33398 1.33301V3.33301" stroke="#C45F44" stroke-miterlimit="10"
                            stroke-linecap="round" stroke-linejoin="round" />
                        <path d="M10.666 1.33301V3.33301" stroke="#C45F44" stroke-miterlimit="10" stroke-linecap="round"
                            stroke-linejoin="round" />
                        <path opacity="0.4" d="M2.33398 6.05957H13.6673" stroke="#C45F44" stroke-miterlimit="10"
                            stroke-linecap="round" stroke-linejoin="round" />
                        <path
                            d="M14.6673 12.6667C14.6673 13.1667 14.5273 13.64 14.2807 14.04C13.8207 14.8133 12.974 15.3333 12.0007 15.3333C11.3273 15.3333 10.714 15.0867 10.2473 14.6667C10.0407 14.4933 9.86065 14.28 9.72065 14.04C9.47399 13.64 9.33398 13.1667 9.33398 12.6667C9.33398 11.1933 10.5273 10 12.0007 10C12.8007 10 13.514 10.3533 14.0007 10.9067C14.414 11.38 14.6673 11.9933 14.6673 12.6667Z"
                            stroke="#C45F44" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                        <path d="M10.9609 12.667L11.6209 13.327L13.0409 12.0137" stroke="#C45F44" stroke-linecap="round"
                            stroke-linejoin="round" />
                        <path
                            d="M14 5.66634V10.9063C13.5133 10.353 12.8 9.99967 12 9.99967C10.5267 9.99967 9.33333 11.193 9.33333 12.6663C9.33333 13.1663 9.47333 13.6397 9.72 14.0397C9.86 14.2797 10.04 14.493 10.2467 14.6663H5.33333C3 14.6663 2 13.333 2 11.333V5.66634C2 3.66634 3 2.33301 5.33333 2.33301H10.6667C13 2.33301 14 3.66634 14 5.66634Z"
                            stroke="#C45F44" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                        <path opacity="0.4" d="M7.99764 9.13314H8.00363" stroke="#C45F44" stroke-linecap="round"
                            stroke-linejoin="round" />
                        <path opacity="0.4" d="M5.52889 9.13314H5.53488" stroke="#C45F44" stroke-linecap="round"
                            stroke-linejoin="round" />
                        <path opacity="0.4" d="M5.5293 11.1338H5.53528" stroke="#C45F44" stroke-linecap="round"
                            stroke-linejoin="round" />
                    </svg>
                    <span class="date pl-2">06 Mar 2025 - 06 Apr 2025</span></div>
                    <span class="ml-4 flex items-center date">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path
                                d="M8.00065 14.6663C11.6825 14.6663 14.6673 11.6816 14.6673 7.99967C14.6673 4.31778 11.6825 1.33301 8.00065 1.33301C4.31875 1.33301 1.33398 4.31778 1.33398 7.99967C1.33398 11.6816 4.31875 14.6663 8.00065 14.6663Z"
                                stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                            <g opacity="0.4">
                                <path d="M5.33372 2H6.00039C4.70039 5.89333 4.70039 10.1067 6.00039 14H5.33372"
                                    stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M10 2C11.3 5.89333 11.3 10.1067 10 14" stroke="#C45F44" stroke-linecap="round"
                                    stroke-linejoin="round" />
                                <path d="M2 10.6667V10C5.89333 11.3 10.1067 11.3 14 10V10.6667" stroke="#C45F44"
                                    stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M2 6.00039C5.89333 4.70039 10.1067 4.70039 14 6.00039" stroke="#C45F44"
                                    stroke-linecap="round" stroke-linejoin="round" />
                            </g>
                        </svg>
                        <span class="pl-2">English</span>
                    </span>
                </div>
                <p class="text-sm text-red-500  upcoming px-4">Upcoming Dates: 1 Aug | 2 Aug | <a href="#"
                        class="underline">View All</a></p>
                <div class="grid grid-cols-2 mt-4 gap-0 card-button">
                    <button class="bg-[#D87A61] text-[#000000] py-2 rounded enroll">Enroll Now</button>
                    <button class="bg-[#ECCEC4] text-[#000000] py-2 rounded remove">Remove</button>
                </div>
            </div> -->

            <!-- Card 3 -->
            <!-- <div class=" cards border rounded-lg  mb-2 py-4">
                <div class="flex px-4">
                    <img src="https://media-cdn.tripadvisor.com/media/photo-s/0e/77/5a/1a/firecamp.jpg"
                        alt="Course Image" class="w-20 h-20 rounded-lg object-cover mr-4" />
                    <div>
                        <p class="text-sm text-[#D87A61] font-semibold art">@The Art of Living</p>
                        <h3 class="font-bold card-title text-lg text-[#293148]">200-Hour Yoga Teacher Training in India
                        </h3>
                    </div>
                </div>
                <div class="flex items-center text-sm text-gray-600 mt-1 date-content px-4 py-2 justify-between">
                    <div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path
                            d="M14.6673 7.99967C14.6673 11.6797 11.6807 14.6663 8.00065 14.6663C4.32065 14.6663 1.33398 11.6797 1.33398 7.99967C1.33398 4.31967 4.32065 1.33301 8.00065 1.33301C11.6807 1.33301 14.6673 4.31967 14.6673 7.99967Z"
                            stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                        <path opacity="0.4"
                            d="M10.4739 10.1202L8.40724 8.88684C8.04724 8.6735 7.75391 8.16017 7.75391 7.74017V5.00684"
                            stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    <span class="date pl-2">30 Mins Per Slot</span></div>
                    
                    <span class="ml-4 flex items-center date">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path
                                d="M8.00065 14.6663C11.6825 14.6663 14.6673 11.6816 14.6673 7.99967C14.6673 4.31778 11.6825 1.33301 8.00065 1.33301C4.31875 1.33301 1.33398 4.31778 1.33398 7.99967C1.33398 11.6816 4.31875 14.6663 8.00065 14.6663Z"
                                stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                            <g opacity="0.4">
                                <path d="M5.33372 2H6.00039C4.70039 5.89333 4.70039 10.1067 6.00039 14H5.33372"
                                    stroke="#C45F44" stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M10 2C11.3 5.89333 11.3 10.1067 10 14" stroke="#C45F44" stroke-linecap="round"
                                    stroke-linejoin="round" />
                                <path d="M2 10.6667V10C5.89333 11.3 10.1067 11.3 14 10V10.6667" stroke="#C45F44"
                                    stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M2 6.00039C5.89333 4.70039 10.1067 4.70039 14 6.00039" stroke="#C45F44"
                                    stroke-linecap="round" stroke-linejoin="round" />
                            </g>
                        </svg>
                        <span class="pl-2">English</span>
                    </span>
                </div>
                <p class="text-sm text-red-500  upcoming px-4">Upcoming Dates: 1 Aug | 2 Aug | <a href="#"
                        class="underline">View All</a></p>
                <div class="grid grid-cols-2 mt-4 gap-0 card-button">
                    <button class="bg-[#D87A61] text-[#000000] py-2 rounded enroll">Enroll Now</button>
                    <button class="bg-[#ECCEC4] text-[#000000] py-2 rounded remove">Remove</button>
                </div>
            </div> -->
        </div>

</section>