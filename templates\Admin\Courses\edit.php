<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Offer $offer
 */
?>
<?php $this->append('style'); ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
</head>
<?php $this->end(); ?>

<div class="main-content bg-container">
    <div class="row">
        <div class="col-12 col-xl-12">
            <div class="card card-primary">
                <div class="d-flex justify-content-between align-items-center">
                    <ul class="breadcrumb breadcrumb-style m-0">
                        <li class="breadcrumb-item">Partner Offerings</li>
                        <li class="breadcrumb-item">
                            <a href="<?= $this->Url->build(['controller' => 'Courses', 'action' => 'index']) ?>">Partner Offerings</a>
                        </li>
                        <li class="breadcrumb-item">Edit Partner Offering</li>
                    </ul>

                    <a href="javascript:void(0);" class="d-flex align-items-center category-back-button  breadcrumb m-0" id="back-button-mo" onclick="history.back();">
                        <span class="rotate me-2">⤣</span>
                        <small class="fw-bold" data-translate="back"><?= __("BACK") ?></small>
                    </a>
                </div>

                <div class="card-header">
                    <h4>Edit Partner Offering</h4>
                </div>
               
                <div class="card-body">
                     <?= $this->Flash->render() ?>
                    <ul class="nav nav-tabs" id="courseTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="basic-tab" data-bs-toggle="tab" href="#basic" role="tab">Basic Information</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="details-tab" data-bs-toggle="tab" href="#details" role="tab">Details </a>
                        </li>
                         <li class="nav-item">
                            <a class="nav-link" id="teachers-tab" data-bs-toggle="tab" href="#teachers" role="tab">Teachers </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link" id="batches-tab" data-bs-toggle="tab" href="#batches" role="tab">Schedule & Bacthes</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="pricing-tab" data-bs-toggle="tab" href="#pricing" role="tab">Pricing</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="media-tab" data-bs-toggle="tab" href="#media" role="tab"> Media</a>
                        </li>
                        <!-- <li class="nav-item">
                            <a class="nav-link" id="review-tab" data-bs-toggle="tab" href="#review" role="tab">Review & Rating</a>
                        </li> -->
                        <li class="nav-item">
                        <a class="nav-link" id="seo-tab" data-bs-toggle="tab" href="#seo" role="tab">SEO</a>
                        </li>
                    </ul>
                   <?php echo $this->Form->create($course, ['id' => 'courseForm', 'novalidate' => true, 'type' => 'file']); ?>
                   <input type="hidden" id="record_id" value="<?= $course->id; ?>">
                    <div class="tab-content p-3 border border-top-0" id="courseTabContent">
                        <!-- Basic Info -->
                        <div class="tab-pane fade show active" id="basic" role="tabpanel">
                            <div class="form-group row">
                                <label for="name" class="col-sm-2 col-form-label">Id<span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <?= h($course->id) ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="name" class="col-sm-2 col-form-label">Name<span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('name', [
                                        'type' => 'text',
                                        'class' => 'form-control',
                                        'id' => 'course-name',
                                        'placeholder' => __('Name'),
                                        'label' => false,
                                        'required' => false
                                    ]); ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="slug" class="col-sm-2 col-form-label">Slug<span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <div class="input-group">
                                        <span class="input-group-text bg-secondary text-white fw-bold" id="course-id-prefix" style="border-right: 0;"><?= $course->id ?>-</span>
                                        <?php echo $this->Form->control('slug_name', [
                                            'type' => 'text',
                                            'class' => 'form-control',
                                            'id' => 'course-slug-name',
                                            'placeholder' => __('yoga-teacher-training'),
                                            'label' => false,
                                            'required' => false,
                                            'value' => preg_replace('/^\d+-/', '', $course->slug ?? ''),
                                            'style' => 'border-left: 0;width:414px;'
                                        ]); ?>
                                    </div>
                                    <!-- Hidden field to store the complete slug -->
                                    <?php echo $this->Form->control('slug', [
                                        'type' => 'hidden',
                                        'id' => 'course-slug-hidden'
                                    ]); ?>
                                    <!-- <small class="form-text text-muted">
                                        Course ID (<strong><?= $course->id ?></strong>) is automatically prefixed and cannot be changed.
                                    </small> -->
                                </div>
                            </div>
                        
                            <div class="form-group row">
                                <label for="course_type_id" class="col-sm-2 col-form-label">Type<span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('course_type_id', [
                                        'type' => 'select',
                                        'options' => $coursetypes,
                                        'empty' => __('Select Course Type'),
                                        'class' => 'form-control form-select',
                                        'label' => false,
                                        'id' => 'course_type_id'
                                    ]); ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="offered_by" class="col-sm-2 col-form-label">Offered By<span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('partner_id', [
                                        'type' => 'select',
                                        'options' => $partners,
                                        'empty' => __('Select Center'),
                                        'class' => 'form-control form-select',
                                        'label' => false,
                                        'id' => 'partner_id'
                                    ]); ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="short_description" class="col-sm-2 col-form-label">Short Description<span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('short_description', [
                                        'type' => 'textarea',
                                        'rows' => '2',
                                        'class' => 'form-control',
                                        'id' => 'short_description',
                                        'placeholder' => __('Short Description'),
                                        'label' => false,
                                    ]); ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="language" class="col-sm-2 col-form-label">Language<span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('language', [
                                        'type' => 'select',
                                        'options' => $languages,
                                        'empty' => __('Select Language'),
                                        'class' => 'form-control form-select mode-checkbox',
                                        'label' => false,
                                        'id' => 'language',
                                    ]); ?>
                                </div>
                            </div>
                           <div class="form-group row">
                                <label for="yoga_style_id" class="col-sm-2 col-form-label">Yoga Styles<span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('yoga_style_id', [
                                        'style'=>'height: 100px',
                                        'type' => 'select',
                                        'id' => 'yoga_style_id',
                                        'options' => $yogaStyles,
                                        'class' => 'select2-multi',
                                        // 'empty' => __('Select Yoga Style'),
                                        'value' => $selectedYogaStyles,
                                        'label' => false,
                                        'multiple' => true
                                    ]) ?>
                                </div>
                            </div>
                           <div class="form-group row">
                                <label for="special_need_id" class="col-sm-2 col-form-label">Special Needs<span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('special_need_id', [
                                        'style'=>'height: 100px',
                                        'type' => 'select',
                                        'id' => 'special_need_id',
                                        'options' => $specialNeeds,
                                        'class' => 'select2-multi',
                                        // 'empty' => __('Select Special Need'),
                                        'value' => $selectedSpecialNeeds,
                                        'label' => false,
                                        'multiple' => true
                                    ]) ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="techniques" class="col-sm-2 col-form-label">Techniques<span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('techniques', [
                                        'style'=>'height: 100px',
                                        'type' => 'select',
                                        'id' => 'techniques',
                                        'options' => $techniques,
                                        'value' => $selectedTechniques,
                                        'class' => 'select2-multi',
                                        // 'empty' => __('Select Techniques'),
                                        'label' => false,
                                        'multiple' => true
                                    ]) ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="level" class="col-sm-2 col-form-label">Level<span class="required-sign">*</span></label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('level', [
                                        'style'=>'height: 100px',
                                        'type' => 'select',
                                        'options' => $levelOptions,
                                        // 'empty' => __('Select Level'),
                                        'value' => $selectedLevels,
                                        'class' => 'select2-multi',
                                        'id' => 'level',
                                        'label' => false,
                                        'multiple' => true
                                    ]); ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="modality" class="col-sm-2 col-form-label">Modality<span class="required-sign">*</span></label>
                                 <div class="col-sm-6 main-field">
                                        <?= $this->Form->control('modality', [
                                        'type' => 'select',
                                        'options'=> $modalities,
                                        'value' => $selectedModalities,
                                        'empty' => __('Select Modality'),
                                        'class' => 'select2-multi',
                                        'label' => false,
                                        'multiple' => true
                                    ]); ?>
                                </div>
                            </div>
                             <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Country</label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('country_id', [
                                        'type' => 'select',
                                        'options' => $countries,  // Fetch from database
                                        'empty' => 'Select Country',
                                        'class' => 'form-control form-select',
                                        'label' => false,
                                        'id' => 'country_id'
                                    ]) ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">State</label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('state_id', [
                                        'type' => 'select',
                                        'options' => $states,
                                        'empty' => 'Select State',
                                        'class' => 'form-control form-select',
                                        'label' => false,
                                        'id' => 'state_id'
                                    ]) ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">City</label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('city_id', [
                                        'type' => 'select',
                                        'options' => $cities,
                                        'empty' => 'Select City',
                                        'class' => 'form-control form-select',
                                        'label' => false,
                                        'id' => 'city_id'
                                    ]) ?>
                                </div>
                            </div>
                            <!-- <div class="mb-3 col-md-6">
                                <label class="form-label">Rgion</label>
                                <?= $this->Form->control('region_id', [
                                    'type' => 'select',
                                    'options' => [],
                                    'empty' => 'Select Region',
                                    'class' => 'form-control form-select',
                                    'label' => false,
                                    'id' => 'region_id'
                                ]) ?>
                            </div> -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Locality</label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('locality_id', [
                                        'type' => 'select',
                                        'options' => [],
                                        'empty' => 'Select Locality',
                                        'class' => 'form-control form-select',
                                        'label' => false,
                                        'id' => 'locality_id'
                                    ]) ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Address</label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('address', [
                                        'type' => 'textarea',
                                        'rows' => '2',
                                        'class' => 'form-control',
                                        'id' => 'address',
                                        'placeholder' => __('Address'),
                                        'label' => false,
                                    ]); ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Latitude</label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('latitude', [
                                        'type' => 'textarea',
                                        'rows' => '2',
                                        'class' => 'form-control',
                                        'id' => 'latitude',
                                        'placeholder' => __('Latitude'),
                                        'label' => false,
                                    ]); ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Longitude</label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('longitude', [
                                        'type' => 'textarea',
                                        'rows' => '2',
                                        'class' => 'form-control',
                                        'id' => 'longitude',
                                        'placeholder' => __('Longitude'),
                                        'label' => false,
                                    ]); ?>
                                </div>
                            </div>
                           <div class="form-group row">
                                <label for="image" class="col-sm-2 col-form-label">Women Only</label>
                                <div class="col-sm-2 main-field">
                                    <?= $this->Form->checkbox('is_women_only', [
                                        'class' => 'form-check-input',
                                        'id' => 'women_only',
                                    ]); ?>
                                </div>
                                <label for="image" class="col-sm-2 col-form-label">Is Featured</label>
                                <div class="col-sm-2 main-field">
                                    <?= $this->Form->checkbox('is_featured', [
                                        'class' => 'form-check-input',
                                        'id' => 'is_featured',
                                    ]); ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                 <label for="image" class="col-sm-2 col-form-label">Accommodation Included</label>
                                <div class="col-sm-2 main-field">
                                    <?= $this->Form->checkbox('is_accommodation_included', [
                                        'class' => 'form-check-input',
                                        'id' => 'accommodation_included',
                                    ]); ?>
                                </div>
                                <label for="image" class="col-sm-2 col-form-label">Food Included</label>
                                <div class="col-sm-2 main-field">
                                    <?= $this->Form->checkbox('is_food_included', [
                                        'class' => 'form-check-input',
                                        'id' => 'food_included',
                                    ]); ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="accommodation_options" class="col-sm-2 col-form-label">Accommodation Options</label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->input('accommodation_options', [
                                        'type' => 'hidden',
                                        'id' => 'accommodation_options',
                                        'label' => false
                                    ]); ?>
                                    <div class="tag-input-container" id="accommodation-tag-container">
                                        <div class="tag-input-wrapper">
                                            <div class="tags-display" id="accommodation-tags-display"></div>
                                            <input type="text"
                                                class="tag-input-field"
                                                id="accommodation-tag-input"
                                                placeholder="Type keyword and press Enter..."
                                                autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="food_options" class="col-sm-2 col-form-label">Food Options</label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->input('food_options', [
                                        'type' => 'hidden',
                                        'id' => 'food_options',
                                        'label' => false
                                    ]); ?>
                             
                                    <div class="tag-input-container" id="food-tag-container">
                                        <div class="tag-input-wrapper">
                                            <div class="tags-display" id="food-tags-display"></div>
                                            <input type="text"
                                                class="tag-input-field"
                                                id="food-tag-input"
                                                placeholder="Type keyword and press Enter..."
                                                autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div> 
                         
                             <div class="form-group row">
                                <label for="has_certification" class="col-sm-2 col-form-label">Has Certification</label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->checkbox('has_certification', [
                                        'class' => 'form-check-input',
                                        'id' => 'has_certification',
                                    ]); ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="image" class="col-sm-2 col-form-label"> Banner Image</label>
                                <div class="col-sm-6 main-field">
                                    <?php echo $this->Form->control('banner_image', [
                                        'type' => 'file',
                                        'class' => 'form-control',
                                        'id' => 'image',
                                        'accept' => 'image/jpeg, image/jpg, image/png',
                                        'title' => !empty($course->image) ? h(basename($course->image)) : '',
                                        'label' => false
                                    ]); ?>
                                    <small class="text-muted"><?= __("Only .jpg, .jpeg, .png files are allowed. Max size: 2MB") ?></small>
                                    <?php if (!empty($course->banner_image)): ?>
                                        <div class="mt-3">
                                            <?php
                                            $pathUrl = $media->displayImage($course->banner_image); // Get image path from MediaComponent
                                            $imageUrl = $this->Url->webroot($pathUrl); // Convert to full URL
                                            ?>
                                            <img src="<?= h($imageUrl) ?>" alt="<?= __("Banner Image") ?>" style="max-width: 100px; max-height: 100px;">


                                        </div>
                                        <small class="text-muted"><?= h(basename($course->image)) ?></small>
                                    <?php endif; ?>
                                </div>
                            </div>
                              <div class="form-group row">
                                <label for="status" class="col-sm-2 col-form-label">Status</label>
                                <div class="col-sm-6 main-field">
                                   <?= $this->Form->control('status', [
                                        'type' => 'select',
                                        'class' => 'form-control form-select',
                                        'id' => 'status',
                                        'options' => [
                                            'A' => __('Active'),
                                            'I' => __('Inactive')
                                        ],
                                        'label' => false
                                    ]); ?>
                                </div>
                            </div>
                        </div>
                        <!-- Location -->
                        <div class="tab-pane fade" id="details" role="tabpanel">
                            <?= $this->element('Admin/Courses/details') ?>
                        </div>
                        <div class="tab-pane fade" id="teachers" role="tabpanel">
                            <?= $this->element('Admin/Courses/add_teacher_form', ['i' => '__INDEX__']) ?>
                        </div>
                        <div class="tab-pane fade" id="batches" role="tabpanel">
                          
                            <!-- <div class="accordion" id="batchAccordion"> -->
                                <?php 
                                //foreach ($course->course_batches as $i => $batch): ?>
                                   <div id="batch-template" >
                                        <?= $this->element('Admin/Courses/course_batch_form', ['i' => '__INDEX__']) ?>
                                    </div>
                                <?php //endforeach; ?>
                            </div>
                        <!-- </div> -->
                        <!-- pricing -->
                        <div class="tab-pane fade" id="pricing" role="tabpanel">
                            <?= $this->element('Admin/Courses/course_base_price_form', ['currencies' => $currencies]) ?>
                            <?= $this->element('Admin/Courses/add_course_addons', ['addonTypes' => $addonTypes]) ?>
                        </div>

                        <div class="tab-pane fade" id="media" role="tabpanel">
                            <div class="form-group row">
                                <label for="media" class="col-sm-2 col-form-label"> Upload Media</label>
                                <div class="col-sm-6 main-field">
                                    <?= $this->Form->control('media[]', [
                                        'type' => 'file',
                                        'class' => 'form-file',
                                        'label' => false,
                                        'multiple' => 'multiple',
                                        'id' => 'imageInput',
                                        'accept' => 'image/*,video/*',
                                        'placeholder' => __("Upload images and videos")
                                    ]); ?>
                                    <small class="text-muted"><?= __("Only .jpg, .jpeg, .png, .gif, .webp, .mp4, .mov, .mkv, .avi files are allowed. Max size: 2MB") ?></small>
                                    <div id="previewContainer">
                                        <ul id="imagePreviewContainer" class="list-unstyled d-flex flex-wrap gap-2 mt-2"></ul>
                                        <?= $this->Form->hidden('deletedImages', ['id' => 'deletedImagesInput', 'value' => '']); ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group row align-items-start">
                                <label for="video_link" class="col-sm-2 col-form-label">Video Link</label>
                                <!-- Input Field Column -->
                                <div class="col-sm-6">
                                    <div id="video-links-wrapper">
                                        <div class="video-link-group mb-2 d-flex gap-2">
                                            <input type="text" name="video_url[]" class="form-control video-link-input" placeholder="Video Link 1">
                                        </div>
                                    </div>
                                </div>

                                <!-- Add More Button Column -->
                                <div class="col-sm-2">
                                    <button type="button" id="add-video-link" class="btn btn-secondary btn-sm">Add More</button>
                                </div>
                            </div>
                            <?php if (!empty($videoLinks)): ?>
                                <?php $index = 1; foreach ($videoLinks as $videoLink): ?>
                                    <div class="form-group row align-items-start mb-2">
                                        <label class="col-sm-2 col-form-label">
                                            &nbsp;
                                        </label>

                                         <div class="video-link-row col-sm-6 d-flex gap-2">
                                            <input type="text" name="video_url[]" class="form-control" placeholder="Video Link <?= $index ?>" value="<?= h($videoLink['path']) ?>">
                                            <input type="hidden" name="video_id[]" value="<?= h($videoLink['id']) ?>">
                                            <button type="button" class="btn btn-danger btn-sm del-video-link" data-id="<?= h($videoLink['id']) ?>">Delete</button>
                                        </div>
                                    </div>
                                <?php $index++; endforeach; ?>
                            <?php endif; ?>                            
                        </div>
                        <!-- SEO Metadata -->
                        <div class="tab-pane fade" id="seo" role="tabpanel">
                            <?= $this->element('Admin/Courses/add_seo_data') ?>
                        </div>
                    </div>
                      
                    <div class="text-end mt-3">
                        <button name="action" value="save" class="btn btn-primary submitBtn">Save</button>
                        <button name="action" value="validate" class="btn btn-primary submitBtn">Validate & Save</button>
                    </div>
                    <?= $this->Form->end(); ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php
$teachersJson = json_encode($teachersJson ?? [], JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP);
?>
<?php $this->append('script'); ?>
<?= $this->element('Admin/Courses/script') ?>
<script>
    function generateSlug(text) {
        return text
            .toString()
            .toLowerCase()
            .trim()
            .replace(/[^a-z0-9\s-]/g, '')     // Remove all non-alphanumeric chars except space and hyphen
            .replace(/\s+/g, '-')             // Replace spaces with hyphens
            .replace(/-+/g, '-');             // Replace multiple hyphens with single one
    }

    function generateSlugWithId(text, courseId) {
        const cleanSlug = generateSlug(text);
        return courseId + '-' + cleanSlug;
    }
document.addEventListener('DOMContentLoaded', function () {

        // Auto-generate slug from course name with course ID
        const courseNameField = document.getElementById('course-name');
        const courseSlugNameField = document.getElementById('course-slug-name');
        const courseSlugHiddenField = document.getElementById('course-slug-hidden');
        const courseId = <?= $course->id ?? 0 ?>;

        function updateSlugFields() {
            if (courseSlugNameField && courseSlugHiddenField && courseId) {
                const slugName = courseSlugNameField.value || generateSlug(courseNameField.value);
                const fullSlug = courseId + '-' + slugName;
                courseSlugHiddenField.value = fullSlug;
            }
        }

        if (courseNameField && courseSlugNameField && courseId) {
            // Update slug when course name changes
            courseNameField.addEventListener('input', function() {
                const newSlugName = generateSlug(this.value);
                courseSlugNameField.value = newSlugName;
                updateSlugFields();
            });

            // Update hidden field when slug name changes manually
            courseSlugNameField.addEventListener('input', function() {
                updateSlugFields();
            });

            // Initialize the hidden field on page load
            updateSlugFields();
        }

       
    });

    $(document).ready(function() {

        // First define the custom validation method for duplicate course name
        $.validator.addMethod('checkDuplicateCourseName', function(value, element) {
            let isValid = false;
            const recordId = $('#record_id').val();

            $.ajax({
                url:  "<?= $this->Url->build(['controller'=> 'Courses', 'action' => 'checkDuplicateCourseName']) ?>",
                method: 'POST',
                dataType: 'json',
                data: {
                    name: value,
                    id: recordId
                },
                async: false,
                headers: {
                    'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                success: function(response) {
                    isValid = !response.isDuplicate;
                },
                error: function() {
                    isValid = false;
                }
            });

            return isValid;
        }, 'This name is already in use. Please enter another one.');

        
    });
    const formAction = "<?= $this->Url->build(['controller'=> 'Courses', 'action' => 'edit', $course->id]) ?>";
    const existingmedia = <?php echo !empty($mediaImages) ? json_encode($mediaImages, JSON_HEX_TAG) : '[]'; ?>;
    let allFiles = [];
    let deletedImages = [];

    function initializeExistingImages(existingmedia) {
        existingmedia.forEach((media) => {
            const ext = media.url.split('.').pop().toLowerCase();
            const isVideo = ['mp4', 'mov', 'mkv', 'avi'].includes(ext);
            let file = {
                id: media.id,
                name: media.displayname,
                type: isVideo ? 'video' : 'image',
                value: media.path,
                url: media.url
            };
            allFiles.push(file);
        });
        renderPreviews();
    }

    initializeExistingImages(existingmedia);
    document.getElementById('imageInput').addEventListener('change', function(event) {
        let newFiles = Array.from(event.target.files);

        // Validation for file types and size
        const allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'mp4', 'mov', 'mkv', 'avi'];
        const maxSizeMB = 2;
        const maxSizeBytes = maxSizeMB * 1024 * 1024;
        let invalidFiles = [];

        newFiles.forEach(file => {
            let fileExtension = file.name.split('.').pop().toLowerCase();
            let fileSize = file.size;

            if (!allowedExtensions.includes(fileExtension)) {
                invalidFiles.push({
                    file: file.name,
                    reason: '<?= __('Invalid file type. Only .jpg, .jpeg, .png, .gif,.webp ,.mp4, .mov, .mkv, .avi files are allowed.') ?>'
                });
            } else if (fileSize > maxSizeBytes) {
                invalidFiles.push({
                    file: file.name,
                    reason: `<?= __('File size exceeds the maximum limit of 2MB.') ?>`
                });
            }
        });
        if (invalidFiles.length > 0) {
            var html = "<ul>";
            invalidFiles.forEach(function(invalidFile) {
                html += `<li>${invalidFile.file} - ${invalidFile.reason}</li>`;
            });
            html += '</ul>';

            const wrapper = document.createElement('div');
            wrapper.innerHTML = html;

            swal.fire({
                title: "<?= __("Invalid Files") ?>",
                content: wrapper,
                confirmButtonText: "<?= __("OK") ?>",
                allowOutsideClick: "true"
            });
            document.getElementById('imageInput').value = "";
            return; // Exit early if there are invalid files
        }

        // Proceed with processing valid files
        newFiles.forEach(file => {
            let fileType = file.type.startsWith('image') ? 'image' : 'video';
            let fileObj = {
                id: Date.now() + Math.random(), // to avoid duplicate IDs
                name: file.name,
                type: fileType,
                value: file.name,
                url: URL.createObjectURL(file),
                file: file
            };
            allFiles.push(fileObj);
        });

        renderPreviews();
        updateFileInput();
    });


    function renderPreviews() {
        let previewContainer = document.getElementById('imagePreviewContainer');
        previewContainer.innerHTML = '';
        allFiles.forEach((file) => {
            let li = document.createElement('li');
            li.classList.add('media-thumbnail', 'position-relative');

            let fileName = file.name;
            let shortName = fileName.length > 20 ? fileName.slice(0, 17) + '...' : fileName;

            let mediaPreview = '';
            if (file.type.startsWith('image')) {
                mediaPreview = `<img src="${file.url}" alt="Image Preview" class="preview-img" style="width: 120px; height: 100px; object-fit: cover;"/>`;
            } else if (file.type.startsWith('video') || ['mp4', 'mov', 'mkv', 'avi'].includes(file.name.split('.').pop().toLowerCase())) {
                mediaPreview = `<video src="${file.url}" class="preview-img" controls style="width: 120px; height: 100px; object-fit: cover;"></video>`;
            }

            li.innerHTML = `
            ${mediaPreview}
            <span class="image-name d-block text-truncate" title="${file.name}">${shortName}</span>
            <button type="button"class="delete-img-btn btn btn-sm btn-danger position-absolute top-0 end-0" data-id="${file.id}">
                <i class="fas fa-times"></i>
            </button>`;
            previewContainer.appendChild(li);
        });
    }

    document.getElementById('imagePreviewContainer').addEventListener('click', function(e) {
        if (e.target.closest('.delete-img-btn')) {
            let id = e.target.closest('.delete-img-btn').getAttribute('data-id');
            let index = allFiles.findIndex(file => file.id == id);
            if (index !== -1) {
                let removedFile = allFiles.splice(index, 1)[0];
                if (removedFile.id) {
                    deletedImages.push(removedFile.id);
                }
                renderPreviews();
                updateFileInput();
                updateDeletedImagesInput();
            }
        }
    });

    function updateDeletedImagesInput() {
        document.getElementById('deletedImagesInput').value = JSON.stringify(deletedImages);
    }

    function updateFileInput() {
        let dataTransfer = new DataTransfer();
        allFiles.forEach(file => {
            if (file.file) {
                dataTransfer.items.add(file.file);
            }
        });
        document.getElementById('imageInput').files = dataTransfer.files;
    }
</script>
<?php $this->end(); ?>