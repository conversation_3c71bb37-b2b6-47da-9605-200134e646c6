<?php

namespace App\Controller\Component;

use <PERSON><PERSON>\Controller\Component;
use <PERSON>ake\Controller\ComponentRegistry;
use <PERSON>ake\Core\Configure;
use Aws\S3\S3Client;
use Aws\Exception\AwsException;
use Aws\CloudFront\CloudFrontClient;
use <PERSON>ake\Cache\Cache;
use Cake\Log\Log;

require_once(ROOT . DS . 'vendor' . DS . 'autoload.php');

class MediaComponent extends Component
{

    public $allowed_ext = array('jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'swf', 'mp4', 'mp3', 'pdf', 'doc', 'docx', 'odt', 'rtf', 'xlsx', 'ppt', 'pptx', 'mpp', 'csv', 'wma');
    public $min_filesize = 10;
    public $max_filesize = 1000000000;

    /**	
     * uploads files to the folder
     * @params:
     * $source 	    = temporary file name
     * $targetdir 	= target directory path
     * $targetfile 	= original file name
     * @return:
     * will return an array with the success of each file upload
     */
    public function uploadMedia($file, $newFileName, $uploadDir)
    {
        if (empty($file)) {
            return 'No file provided.';
        }

        // Create directory if it doesn't exist
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0775, true);
        }

        // Check if it's UploadedFileInterface (used in CakePHP forms)
        if (is_object($file) && method_exists($file, 'getStream')) {
            $ext = strtolower(pathinfo($newFileName, PATHINFO_EXTENSION));

            // You can validate extension here if needed
            if (!in_array($ext, $this->allowed_ext)) {
                return 'Invalid file extension.';
            }

            $filePath = $uploadDir . $newFileName;
            $file->moveTo($filePath); // move uploaded file to destination

            return 'Success';
        }

        // If file is a temp path (string)
        if (is_string($file)) {
            $filePath = $uploadDir . $newFileName;
            if (copy($file, $filePath)) {
                return 'Success';
            }
            return 'Failed to copy file.';
        }

        return 'Invalid file format.';
    }
    public function deleteMedia($relativePath)
    {
        if (!$relativePath) {
            return false;
        }

        // Normalize path
        $relativePath = ltrim($relativePath, '/');
        if (strpos($relativePath, 'webroot/') === 0) {
            $relativePath = substr($relativePath, strlen('webroot/'));
        }

        $fullPath = WWW_ROOT . $relativePath;

        if (file_exists($fullPath)) {
            if (unlink($fullPath)) {
                return true;
            } else {
                return false;
            }
        }

        Log::write('error', 'File does not exist: ' . $fullPath);
        return false;
    }


    public function human_filesizebk($bytes, $decimals = 0)
    {
        $sz = 'BKMGTP';
        $factor = floor((strlen($bytes) - 1) / 3);
        return sprintf("%.{$decimals}f", $bytes / pow(1000, $factor)) . @$sz[$factor] . 'B';
    }

    public function displayImage($image)
    {
        // $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
        // return  $uploadFolder . $image;

        return 'uploads/' . $image;
    }




    // AWS code start here remov above code later because it store in local machine all media informations

    public function upload($source, $targetdir, $targetfile, $folderPath = null)
    {
        
        $filesize = filesize($source);
        if (($filesize < $this->min_filesize) || ($filesize > $this->max_filesize)) {
            return __('File size must be between {0} and {1}', $this->human_filesize($this->min_filesize), $this->human_filesize($this->max_filesize));
        }

        $ext = pathinfo($targetfile, PATHINFO_EXTENSION);
        if (!in_array($ext, $this->allowed_ext)) {

            return __('File type not allowed.');
        }

        /********************/
        if ($ext == "jpg" || $ext == "png" || $ext == "jpeg" || $ext == "gif") {
            $data = getimagesize($source);

            if (empty($data) || !is_array($data)) {
                return __('Image is invalid');
            }

            list($srcWidth, $srcHeight, $type) = $data;

            if (!$srcWidth || !$srcHeight) {
                return __('Image size invalid');
            }

            if ($srcWidth < 1 || $srcHeight < 1) {
                return __('Image size must be at least 100x100');
            }

        }
        /********************/

        if (!file_exists($targetdir)) {
            mkdir($targetdir, 0777, true);
        }

        $response = $this->awsUpload($source, $targetfile, $folderPath);
        if ($response) {
            $imgPath = $folderPath . $targetfile;
            @unlink($imgPath);
            return $response;
        }

        /* if (!file_exists($targetdir)) {
               mkdir($targetdir, 0777, true);
             }*/

        if (move_uploaded_file($source, $targetdir . '/' . $targetfile)) {
            return 'Success';
        }

        if (!is_writeable($targetdir . '/' . $targetfile)) {
            return __("Cannot write to destination file");
        }

        return __('File upload is invalid');
    }

    ////////////////////////////////////////////////////////////

    public function human_filesize($bytes, $decimals = 0)
    {
        $sz = 'BKMGTP';
        $factor = floor((strlen($bytes) - 1) / 3);
        return sprintf("%.{$decimals}f", $bytes / pow(1000, $factor)) . @$sz[$factor] . 'B';
    }

    public function mkdir($targetdir)
    {
        if (!is_dir($targetdir)) {
            App::uses('Folder', 'Utility');
            $dir = new Folder($targetdir, true, 0777);
            if (!$dir) {
                return false;
            }
        }
        return true;
    }



    ///////////////////////////////////////////////////////

    public function ext($file)
    {
        return mb_strtolower(trim(mb_strrchr($file, '.'), '.'));
    }

    public function awsDelete($key)
    {
        $client = S3Client::factory(array(
            'credentials' => array(
                'key' => Configure::read('Settings.CKEY'),
                'secret' => Configure::read('Settings.CSECRET')
            ),
            'region' => Configure::read('Settings.CREGION'),
            'version' => Configure::read('Settings.CVERSION')
        ));

        $client->deleteObject(array(
            'Bucket' => Configure::read('Settings.CBUCKET'),
            'Key' => $key
        ));
    }

    public function awsUpload($imgURL, $filename, $path, $flag = null)
    {
        if ($flag != 1) {
            $pathFile = WWW_ROOT . $path . $filename;
            copy($imgURL, $pathFile);
        }
        else{
            $pathFile = $imgURL;
        }
        $client = S3Client::factory(array(
            'credentials' => array(
                'key' => Configure::read('Settings.CKEY'),
                'secret' => Configure::read('Settings.CSECRET'),

            ),
            'region' => Configure::read('Settings.CREGION'),
            'version' => Configure::read('Settings.CVERSION')
        ));
        try {

            $result = $client->putObject(array(
                'Bucket' => Configure::read('Settings.CBUCKET'),
                'Key' => $path . $filename,
                'SourceFile' => $pathFile,
                // 'ACL' => Configure::read ('Settings.CACL'),
            ));

            //return $result['ObjectURL']; 
            return 'Success';

        } catch (Exception $e) {

            $logtext = "Error:- " . $e->getMessage();
            $res = $this->logwrite($logtext);
            return 'Fail';
        }
    }

    public function awsDownload($filename)
    {
        return $filename;
        try {
            $path = configure::read('Settings.IMG_QRCODE');
            $pathFile = WWW_ROOT . $path . $filename;
            $url_path = $path . $filename;

            $client = S3Client::factory(array(
                'credentials' => array(
                    'key' => Configure::read('Settings.CKEY'),
                    'secret' => Configure::read('Settings.CSECRET'),
                ),
                'region' => Configure::read('Settings.CREGION'),
                'version' => Configure::read('Settings.CVERSION'),

            ));

            $bucket = Configure::read('Settings.CBUCKET');
            $signedUrl = $client->getObjectUrl($bucket, $url_path, '+10 minutes');

            return $signedUrl;
        } catch (Exception $e) {
            $logtext = "Error:- " . $e->getMessage();
            $res = $this->logwrite($logtext);
            return $res;
        }
    }

    public function awsDownloadDoc($url_path, $localFilePath)
    {

        try {

            $client = S3Client::factory(array(
                'credentials' => array(
                    'key' => Configure::read('Settings.CKEY'),
                    'secret' => Configure::read('Settings.CSECRET'),
                ),
                'region' => Configure::read('Settings.CREGION'),
                'version' => Configure::read('Settings.CVERSION'),

            ));

            $bucket = Configure::read('Settings.CBUCKET');

            $key = ltrim(parse_url($url_path, PHP_URL_PATH), '/');

            try {
                $result = $client->getObject([
                    'Bucket' => $bucket,
                    'Key' => $key,
                    'SaveAs' => $localFilePath,
                ]);

            } catch (\Exception $e) {
                $this->log('S3 Error: ' . $e->getMessage());
            }

            return $result;
        } catch (Exception $e) {
            $logtext = "Error:- " . $e->getMessage();
            $res = $this->logwrite($logtext);
            return $res;
        }
    }

    public function getCloudFrontURL($keyName)
    {
        if (empty($keyName)) {
            Log::write('error', 'getCloudFrontURL called with empty keyName');
            return false;
        }

        $cacheKey = 'cloudfront_' . md5($keyName);

        $signedUrl = Cache::read($cacheKey);

        if ($signedUrl) {
            Log::write('debug', "Cache hit for key: $cacheKey");
        } else {
            Log::write('debug', "Cache miss for key: $cacheKey");

            $s3 = new S3Client([
                'version'     => 'latest',
                'region'      => Configure::read('Settings.CREGION'),
                'credentials' => [
                    'key'    => Configure::read('Settings.CKEY'),
                    'secret' => Configure::read('Settings.CSECRET'),
                ]
            ]);

            $cmd = $s3->getCommand('GetObject', [
                'Bucket' => Configure::read('Settings.CBUCKET'),
                'Key'    => $keyName,
                'ResponseContentDisposition' => 'inline'
            ]);

            $request = $s3->createPresignedRequest($cmd, '+1 week');

            $signedUrl = (string) $request->getUri();

            Cache::write($cacheKey, $signedUrl, 'default');
        }

        return $signedUrl;
    }

    // public function upload_resize($source, $targetdir, $targetfile, $folderPath) {            
    //     ob_start();  
    //     $file_size  = filesize($source);
    //     $file_type = pathinfo($targetfile, PATHINFO_EXTENSION);
    
    //     if ($file_size) {
            
    //         $source = str_replace("\\", "/", $source);
    //         if ($file_type == "pjpeg" || $file_type == "jpeg" || $file_type == "jpg") {
    //             $new_img = imagecreatefromjpeg($source);
    //         } elseif ($file_type == "png" || $file_type == "x-png") {
    //             $new_img = imagecreatefrompng($source);
    //         } elseif ($file_type == "gif") {
    //             $new_img = imagecreatefromgif($source);
    //         } elseif ($file_type == "bmp") {
    //             $new_img = imagecreatefrombmp($source);
    //         } else {
    //             echo "Unsupported file type: $file_type";
    //             return;
    //         }

    //         if ($new_img === false) {
    //             echo "Failed to load image.";
    //         } else {
    //             echo "Image loaded successfully.";
    //         }

    //         echo "<pre>"; print_r($new_img); die;
    //         list($width, $height) = getimagesize($source);
            
    //         // Get the minimum and maximum dimensions from the configuration
    //         $minWidth = Configure::read('Constants.PRODUCT_IMAGE_MIN_WIDTH');
    //         $maxWidth = Configure::read('Constants.PRODUCT_IMAGE_MAX_WIDTH');
    //         $minHeight = Configure::read('Constants.PRODUCT_IMAGE_MIN_HEIGHT');
    //         $maxHeight = Configure::read('Constants.PRODUCT_IMAGE_MAX_HEIGHT');
    
    //         // Calculate aspect ratio
    //         $aspectRatio = $width / $height;
    
    //         // Calculate the new width and height based on the aspect ratio
    //         $newWidth = $maxWidth;
    //         $newHeight = $maxHeight;
    
    //         // Resize based on the maximum width and height constraints, maintaining aspect ratio
    //         if ($width > $height) {
    //             // Landscape mode
    //             $newHeight = intval($maxWidth / $aspectRatio);
    //             if ($newHeight > $maxHeight) {
    //                 $newHeight = $maxHeight;
    //                 $newWidth = intval($newHeight * $aspectRatio);
    //             }
    //         } else {
    //             // Portrait or square mode
    //             $newWidth = intval($maxHeight * $aspectRatio);
    //             if ($newWidth > $maxWidth) {
    //                 $newWidth = $maxWidth;
    //                 $newHeight = intval($newWidth / $aspectRatio);
    //             }
    //         }
    
    //         // Ensure the new size is within the min and max bounds
    //         $newWidth = max($minWidth, min($newWidth, $maxWidth));
    //         $newHeight = max($minHeight, min($newHeight, $maxHeight));
    
    //         // Create the true color image resource for resizing
    //         if (function_exists('imagecreatetruecolor')) {
    //             $resized_img_preview = imagecreatetruecolor($newWidth, $newHeight);
    //         } else {
    //             return __("Error: Please make sure you have GD library ver 2+");
    //         }
    
    //         echo "<pre>"; print_r($resized_img_preview); die;
    //         // Resize the image to the new dimensions
    //         imagecopyresized($resized_img_preview, $new_img, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
    
    //         ImageJpeg($resized_img_preview,"$targetdir$targetfile");
    //         ImageDestroy ($resized_img_preview);   
    //         $imgPath = $targetdir.$targetfile;   
    //         $uploadResult = $this->awsUpload($imgPath, $targetfile, $folderPath);

    //         // $tempPath = $targetdir . 'temp_' . $targetfile;
    //         // if ($file_type == "jpeg" || $file_type == "jpg" || $file_type == "pjpeg") {
    //         //     imagejpeg($resized_img_preview, $tempPath, 90);
    //         // } elseif ($file_type == "png") {
    //         //     imagepng($resized_img_preview, $tempPath);
    //         // } elseif ($file_type == "gif") {
    //         //     imagegif($resized_img_preview, $tempPath);
    //         // } elseif ($file_type == "bmp") {
    //         //     imagebmp($resized_img_preview, $tempPath);
    //         // }
    //         // imagedestroy($new_img);
    //         // imagedestroy($resized_img_preview);
    //         // @unlink($tempPath);
    //         //return $uploadResult;

    //         ImageDestroy($new_img);
    //         $imgPath = $targetdir.$targetfile;   
    //         @unlink($imgPath);
    //         return $response;
    //     }
    
    //     return __('File size or type is not valid');
    // }    


    public function upload_resize($source, $targetdir, $targetfile, $folderPath) {
        ob_start();
    
        // Ensure the file exists and is readable
        if (!file_exists($source)) {
            echo "Source file does not exist: $source";
            return;
        }
    
        $file_size  = filesize($source);
        $file_type = pathinfo($targetfile, PATHINFO_EXTENSION);
    
        // Check if the file size is valid
        if ($file_size == 0) {
            echo "File size is zero.";
            return;
        }
    
        // Normalize file path (in case of Windows vs. UNIX paths)
        $source = str_replace("\\", "/", $source);
    
        $image_info = getimagesize($source);
        if ($image_info === false) {
            echo "Error: The file '$source' is not a valid image.";
            return;
        }

        $image_mime = $image_info['mime'];

        $new_img = false;
        // Only proceed if the MIME type is correct for JPEG
        if ($image_mime == 'image/jpeg' || $image_mime == 'image/pjpeg') {
            $new_img = @imagecreatefromjpeg($source);
        } elseif ($image_mime == 'image/png') {
            $new_img = @imagecreatefrompng($source);
        } elseif ($image_mime == 'image/gif') {
            $new_img = @imagecreatefromgif($source);
        } elseif ($image_mime == 'image/bmp') {
            $new_img = @imagecreatefrombmp($source);
        } else {
            echo "Unsupported file type: $image_mime for file: $source";
            return;
        }
    
        // If the image could not be loaded, display the error
        if ($new_img === false) {
            $error = error_get_last();
            echo "Error loading image: " . $error['message'];  // Display the error message
            return;
        }

        // Get the original dimensions of the image
        $width = imagesx($new_img);
        $height = imagesy($new_img);
    
        if ($width === false || $height === false) {
            echo "Failed to get image dimensions.";
            return;
        }
    
        // Get the minimum and maximum dimensions from the configuration
        $minWidth = Configure::read('Constants.PRODUCT_IMAGE_MIN_WIDTH');
        $maxWidth = Configure::read('Constants.PRODUCT_IMAGE_MAX_WIDTH');
        $minHeight = Configure::read('Constants.PRODUCT_IMAGE_MIN_HEIGHT');
        $maxHeight = Configure::read('Constants.PRODUCT_IMAGE_MAX_HEIGHT');
    
        // Calculate aspect ratio
        $aspectRatio = $width / $height;
    
        // Calculate the new width and height based on the aspect ratio
        $newWidth = $maxWidth;
        $newHeight = $maxHeight;
    
        // Resize based on the maximum width and height constraints, maintaining aspect ratio
        if ($width > $height) {
            // Landscape mode
            $newHeight = intval($maxWidth / $aspectRatio);
            if ($newHeight > $maxHeight) {
                $newHeight = $maxHeight;
                $newWidth = intval($newHeight * $aspectRatio);
            }
        } else {
            // Portrait or square mode
            $newWidth = intval($maxHeight * $aspectRatio);
            if ($newWidth > $maxWidth) {
                $newWidth = $maxWidth;
                $newHeight = intval($newWidth / $aspectRatio);
            }
        }
    
        // Ensure the new size is within the min and max bounds
        $newWidth = max($minWidth, min($newWidth, $maxWidth));
        $newHeight = max($minHeight, min($newHeight, $maxHeight));
    
        // Create the true color image resource for resizing
        if (function_exists('imagecreatetruecolor')) {
            $resized_img_preview = imagecreatetruecolor($newWidth, $newHeight);
        } else {
            return __("Error: Please make sure you have GD library ver 2+");
        }
    
        // Resize the image to the new dimensions
        imagecopyresized($resized_img_preview, $new_img, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
    
        switch (strtolower($file_type)) {
            case "jpeg":
            case "jpg":
            case "pjpeg":
                imagejpeg($resized_img_preview, $targetdir . $targetfile);
                break;
            case "png":
                imagejpeg($resized_img_preview, $targetdir . $targetfile);
                break;
            case "gif":
                imagejpeg($resized_img_preview, $targetdir . $targetfile);
                break;
            case "bmp":
                imagejpeg($resized_img_preview, $targetdir . $targetfile);
                break;
            default:
                echo "Unsupported image format for saving: $file_type";
                return;
        }
    
        // Clean up the resources
        imagedestroy($new_img);
        imagedestroy($resized_img_preview);
    
        // Optionally upload the image (AWS or other destination)
        $imgPath = $targetdir . $targetfile;
        $uploadResult = $this->awsUpload($imgPath, $targetfile, $folderPath);
    
        // Return success response
        return $uploadResult; // Or return any custom response you need
    
        // @unlink($imgPath);
    }
    

    
}
