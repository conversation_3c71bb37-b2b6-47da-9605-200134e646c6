<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * Cities Model
 *
 * @property \App\Model\Table\CountriesTable&\Cake\ORM\Association\BelongsTo $Countries
 * @property \App\Model\Table\RegionsTable&\Cake\ORM\Association\BelongsTo $Regions
 * @property \App\Model\Table\StatesTable&\Cake\ORM\Association\BelongsTo $States
 * @property \App\Model\Table\LocalitiesTable&\Cake\ORM\Association\BelongsTo $Localities
 * @property \App\Model\Table\CoursesTable&\Cake\ORM\Association\HasMany $Courses
 * @property \App\Model\Table\CustomersTable&\Cake\ORM\Association\HasMany $Customers
 * @property \App\Model\Table\DistrictsTable&\Cake\ORM\Association\HasMany $Districts
 * @property \App\Model\Table\PartnersTable&\Cake\ORM\Association\HasMany $Partners
 *
 * @method \App\Model\Entity\City newEmptyEntity()
 * @method \App\Model\Entity\City newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\City> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\City get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\City findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\City patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\City> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\City|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\City saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\City>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\City>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\City>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\City> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\City>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\City>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\City>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\City> deleteManyOrFail(iterable $entities, array $options = [])
 */
class DestinationsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */


    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('destinations');
        $this->setDisplayField('name');
        $this->setPrimaryKey('id');

        $this->belongsTo('Countries', [
            'foreignKey' => 'country_id',
            'joinType' => 'LEFT',
        ]);
        $this->belongsTo('Regions', [
            'foreignKey' => 'region_id',
            'joinType' => 'LEFT',
        ]);
        $this->belongsTo('States', [
            'foreignKey' => 'state_id',
            'joinType' => 'LEFT',
        ]);
        $this->belongsTo('Cities', [
            'foreignKey' => 'city_id',
            'joinType' => 'LEFT',
        ]);
        $this->belongsTo('Localities', [
            'foreignKey' => 'locality_id',
            'joinType' => 'LEFT',
        ]);
        $this->hasMany('Courses', [
            'foreignKey' => 'city_id',
        ]);
        $this->hasMany('Customers', [
            'foreignKey' => 'city_id',
        ]);
        $this->hasMany('Districts', [
            'foreignKey' => 'city_id',
        ]);
        $this->hasMany('Partners', [
            'foreignKey' => 'city_id',
        ]);
        $this->hasMany('ChildLocalities', [
            'className' => 'Localities',
            'foreignKey' => 'city_id',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->scalar('type')
            ->inList('type', ['destination', 'country', 'region', 'state', 'city', 'locality'])
            ->requirePresence('type', 'create')
            ->notEmptyString('type');

        // Conditional validation based on type
        $validator
            ->integer('country_id')
            ->allowEmptyString('country_id')
            ->add('country_id', 'requiredForTypes', [
                'rule' => function ($value, $context) {
                    $type = $context['data']['type'] ?? null;
                    // Country is required for all types except 'country' itself
                    if (in_array($type, ['region', 'state', 'city', 'locality', 'destination'])) {
                        return !empty($value);
                    }
                    return true;
                },
                'message' => 'Country is required for this type'
            ]);

        $validator
            ->integer('state_id')
            ->allowEmptyString('state_id')
            ->add('state_id', 'optionalForTypes', [
                'rule' => function ($value, $context) {
                    // State is optional for all types - let the business logic handle requirements
                    return true;
                },
                'message' => 'State validation failed'
            ]);

        $validator
            ->integer('region_id')
            ->allowEmptyString('region_id');

        $validator
            ->integer('locality_id')
            ->allowEmptyString('locality_id');

        $validator
            ->scalar('old_url')
            ->maxLength('old_url', 255)
            ->allowEmptyString('old_url');

        $validator
            ->scalar('lat')
            ->maxLength('lat', 20)
            ->allowEmptyString('lat');

        $validator
            ->scalar('lng')
            ->maxLength('lng', 20)
            ->allowEmptyString('lng');

        $validator
            ->scalar('slug')
            ->maxLength('slug', 255)
            ->requirePresence('slug', 'create')
            ->notEmptyString('slug')
            ->add('slug', 'unique', ['rule' => 'validateUnique', 'provider' => 'table']);

        $validator
            ->scalar('name')
            ->maxLength('name', 100)
            ->requirePresence('name', 'create')
            ->notEmptyString('name');

        $validator
            ->integer('near_type')
            ->allowEmptyString('near_type');

        $validator
            ->scalar('description')
            ->allowEmptyString('description');

        $validator
            ->scalar('banner_image')
            ->maxLength('banner_image', 255)
            ->allowEmptyFile('banner_image');

        $validator
            ->scalar('thumb_image')
            ->maxLength('thumb_image', 255)
            ->allowEmptyFile('thumb_image');

        $validator
            ->boolean('is_feature')
            ->allowEmptyString('is_feature');

        $validator
            ->boolean('near_beach')
            ->allowEmptyString('near_beach');

        $validator
            ->boolean('near_mountain')
            ->allowEmptyString('near_mountain');

        $validator
            ->allowEmptyString('information');

        $validator
            ->scalar('meta_title')
            ->maxLength('meta_title', 255)
            ->allowEmptyString('meta_title');

        $validator
            ->scalar('meta_description')
            ->allowEmptyString('meta_description');

        $validator
            ->scalar('meta_keywords')
            ->allowEmptyString('meta_keywords');

        $validator
            ->inList('status', ['A', 'I', 'D'])
            ->allowEmptyString('status');

        $validator
            ->dateTime('created_at')
            ->notEmptyDateTime('created_at');

        $validator
            ->dateTime('modified_at')
            ->notEmptyDateTime('modified_at');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->isUnique(['slug']), ['errorField' => 'slug']);
        
        // Only validate foreign key constraints if the value is not empty
        $rules->add($rules->existsIn(['country_id'], 'Countries'), [
            'errorField' => 'country_id',
            'on' => function ($context) {
                return !empty($context['data']['country_id']);
            }
        ]);
        
        $rules->add($rules->existsIn(['state_id'], 'States'), [
            'errorField' => 'state_id',
            'on' => function ($context) {
                return !empty($context['data']['state_id']);
            }
        ]);

        return $rules;
    }

    /**
     * Create or update a city (destination) record.
     * If $data['id'] is present, update; else create.
     * Returns the saved entity or false.
     */
    public function saveOrUpdate(array $data)
    {
        // Handle information field for LONGTEXT storage
        if (isset($data['information']) && ($data['information'] === '' || $data['information'] === null)) {
            $data['information'] = null;
        }
        
        // Clean up empty string values to null for foreign keys
        $foreignKeys = ['country_id', 'state_id', 'region_id', 'locality_id', 'near_type'];
        foreach ($foreignKeys as $key) {
            if (isset($data[$key]) && ($data[$key] === '' || $data[$key] === '0')) {
                $data[$key] = null;
            }
        }
        
        if (!empty($data['id'])) {
            $entity = $this->get($data['id']);
        } else {
            $entity = $this->newEmptyEntity();
        }
        
        $entity = $this->patchEntity($entity, $data);
        
        if ($this->save($entity)) {
            return $entity;
        }
        
        // Log validation errors for debugging
        if ($entity->hasErrors()) {
            error_log('Destination save errors: ' . json_encode($entity->getErrors()));
        }
        
        return false;
    }

    /**
     * Check if a slug exists in the table, excluding a given id (for update).
     * @param string $slug
     * @param int|null $excludeId
     * @return bool
     */
    public function slugExists(string $slug, $excludeId = null): bool
    {
        $query = $this->find()->where(['slug' => $slug]);
        if (!empty($excludeId)) {
            $query->where(['id !=' => $excludeId]);
        }
        return $query->count() > 0;
    }

    /**
     * Find active records (excludes deleted)
     */
    public function findActive(SelectQuery $query, array $options): SelectQuery
    {
        return $query->where(['status !=' => 'D']);
    }

    /**
     * Soft delete a record by updating status to 'D'
     */
    public function softDelete($id): bool
    {
        $entity = $this->get($id);
        $entity->status = 'D';
        return (bool)$this->save($entity);
    }

    /**
     * Find destinations for index with optional status filter
     */
    public function findForIndex(SelectQuery $query, array $options): SelectQuery
    {
        $query = $query->contain(['Countries', 'States', 'Regions', 'Localities'])
                      ->order(['name' => 'ASC']);
        
        if (!empty($options['status'])) {
            $query->where(['status' => $options['status']]);
        } else {
            $query->where(['status !=' => 'D']);
        }
        
        return $query;
    }
    /**
     * Find a city by slug or id.
     * If $type is empty or 'slug', use $slug as slug.
     * Otherwise, treat $slug as id.
     */
    public function findBySlug($slug, $type = null)
    {
        // Get all other cities' lat/lng except the current one
        $allLatLng = $this->find()
            ->select(['id','name', 'lat', 'lng'])
            ->where([
            'status !=' => 'D',
            'lat !=' => '',
            'lng !=' => '',
            'lat IS NOT' => null,
            'lng IS NOT' => null
            ])
            ->enableHydration(false)
            ->toArray();

        $query = $this->find()
            ->select([
                'id',
                'state_id',
                'type',
                'slug',
                'old_url',
                'name',
                'country_id',
                'description',
                'banner_image',
                'information',
                'near_type',
                'meta_title',
                'meta_description',
                'meta_keywords',
                'lat',
                'lng',
                'status',
                'created_at',
                'modified_at',
                'StateName' => 'States.name',
                'CountryName' => 'Countries.name'
            ])
            ->contain(['Countries', 'States']);

        if (empty($type) || $type === 'slug') {
            $query->where(['slug' => $slug]);
        } else {
            $query->where(['id' => $slug]);
        }

        // Attach all other lat/lng as a virtual field in the result
        $query->formatResults(function ($results) use ($allLatLng) {
            return $results->map(function ($row) use ($allLatLng) {
                $row['locations'] = $allLatLng;
                return $row;
            });
        });

        return $query;
    }
    
    public function getListByState($stateId){
        $res = $this->find()
        ->select(['id', 'name'])
        ->where([
            'state_id' => $stateId,
            'status' => 'A'
        ])
        ->orderAsc('name')
        ->all()
        ->toArray();

        return  $res;
    }

    public function getDestinationsList()
    {
        return $this->find()
            ->where(['type' => 'destination', 'status !=' => 'D'])
            ->orderAsc('name')
            ->all()
            ->toArray();
    }

    public function getActiveCityNames()
    {
        $query = $this->find()
            ->where([
                'Destinations.type IN' => ['city', 'state'],
                'Destinations.status' => 'A'
            ])
            ->contain(['States.Regions'])
            ->orderAsc('Destinations.name');

        return $query->all()->toArray();
    }

   public function getActiveCityNamesGroupedByRegionState()
{
    // Helper function to create URL-friendly slugs
    $createSlug = function($text) {
        return strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $text), '-'));
    };
    
    // Get both cities AND states from destinations table
    
    // 1. Get all active cities and states
    $destinations = $this->find()
        ->select(['id', 'name', 'slug', 'state_id', 'type', 'country_id'])
        ->where([
            'type IN' => ['city', 'state'],
            'status' => 'A'
        ])
        ->orderAsc('name')
        ->enableHydration(false)
        ->toArray();
    
    if (empty($destinations)) {
        error_log("DEBUG: No active cities or states found");
        return [];
    }
    
    error_log("DEBUG: Found " . count($destinations) . " active destinations (cities + states)");
    
    // Separate cities and states
    $cities = [];
    $stateDestinations = [];
    
    foreach ($destinations as $dest) {
        if ($dest['type'] === 'city') {
            $cities[] = $dest;
        } elseif ($dest['type'] === 'state') {
            $stateDestinations[] = $dest;
        }
    }
    
    error_log("DEBUG: Found " . count($cities) . " cities and " . count($stateDestinations) . " states");
    
    // 2. Get all states from States table (for region relationships)
    $allStateIds = array_unique(array_filter(array_column($cities, 'state_id')));
    
    // Also include state IDs that might be referenced by state destinations
    foreach ($stateDestinations as $stateDest) {
        if ($stateDest['state_id']) {
            $allStateIds[] = $stateDest['state_id'];
        }
    }
    
    $statesTable = \Cake\ORM\TableRegistry::getTableLocator()->get('States');
    $states = [];
    if (!empty($allStateIds)) {
        $states = $statesTable->find()
            ->select(['id', 'name', 'region_id'])
            ->where(['id IN' => $allStateIds])
            ->enableHydration(false)
            ->toArray();
    }
    
    // Create state lookup
    $stateById = [];
    foreach ($states as $state) {
        $stateById[$state['id']] = $state;
    }
    
    // 3. Get all regions
    $regionIds = array_unique(array_filter(array_column($states, 'region_id')));
    
    $regions = [];
    if (!empty($regionIds)) {
        $regionsTable = \Cake\ORM\TableRegistry::getTableLocator()->get('Regions');
        $regions = $regionsTable->find()
            ->select(['id', 'name'])
            ->where(['id IN' => $regionIds])
            ->enableHydration(false)
            ->toArray();
    }
    
    // Create region lookup
    $regionById = [];
    foreach ($regions as $region) {
        $regionById[$region['id']] = $region;
    }
    
    // 4. Get country information
    $countryIds = array_unique(array_filter(array_column($destinations, 'country_id')));
    $countries = [];
    if (!empty($countryIds)) {
        $countriesTable = \Cake\ORM\TableRegistry::getTableLocator()->get('Countries');
        $countries = $countriesTable->find()
            ->select(['id', 'name'])
            ->where(['id IN' => $countryIds])
            ->enableHydration(false)
            ->toArray();
    }
    
    // Create country lookup
    $countryById = [];
    foreach ($countries as $country) {
        $countryById[$country['id']] = $country;
    }
    
    // 5. Group destinations by region and state
    $result = [];
    
    // First, add states as primary entries
    foreach ($stateDestinations as $stateDest) {
        $regionName = 'Unknown Region';
        $stateName = $stateDest['name'];
        
        // Try to find region through state_id if available
        if ($stateDest['state_id'] && isset($stateById[$stateDest['state_id']])) {
            $parentState = $stateById[$stateDest['state_id']];
            if ($parentState['region_id'] && isset($regionById[$parentState['region_id']])) {
                $regionName = $regionById[$parentState['region_id']]['name'];
            }
        } else {
            // For state destinations, we might need to find region differently
            // Try to match by name or use a default region
            foreach ($regions as $region) {
                // You might want to add logic here to match states to regions
                // For now, we'll use the first region or a default
                $regionName = $region['name'];
                break;
            }
        }
        
        // Initialize region if not exists
        if (!isset($result[$regionName])) {
            $regionSlug = $createSlug($regionName);
            
            // Get country info for this destination
            $countryName = 'Unknown Country';
            $countrySlug = 'unknown-country';
            if ($stateDest['country_id'] && isset($countryById[$stateDest['country_id']])) {
                $countryName = $countryById[$stateDest['country_id']]['name'];
                $countrySlug = $createSlug($countryName);
            }
            
            $result[$regionName] = [
                'region_slug' => $regionSlug,
                'country_name' => $countryName,
                'country_slug' => $countrySlug,
                'states' => []
            ];
        }
        
        // Check if this state already exists in the region
        $stateExists = false;
        foreach ($result[$regionName]['states'] as $index => $existingState) {
            if ($existingState['state_name'] === $stateName) {
                $stateExists = true;
                break;
            }
        }
        
        if (!$stateExists) {
            $result[$regionName]['states'][] = [
                'state_name' => $stateName,
                'state_slug' => $stateDest['slug'],
                'type' => $stateDest['type'], // Include type information
                'cities' => []
            ];
        }
    }
    
    // Then, add cities under their respective states
    foreach ($cities as $city) {
        $stateId = $city['state_id'];
        
        if (!isset($stateById[$stateId])) {
            error_log("DEBUG: State not found for city " . $city['name'] . " with state_id " . $stateId);
            continue;
        }
        
        $state = $stateById[$stateId];
        $regionId = $state['region_id'];
        
        if ($regionId && isset($regionById[$regionId])) {
            $regionName = $regionById[$regionId]['name'];
        } else {
            // Fallback: try to infer region from state name
            $regionName = 'Other Regions';
        }
        
        $stateName = $state['name'];
        
        // Initialize region if not exists
        if (!isset($result[$regionName])) {
            $regionSlug = $createSlug($regionName);
            
            // Get country info for this city
            $countryName = 'Unknown Country';
            $countrySlug = 'unknown-country';
            if ($city['country_id'] && isset($countryById[$city['country_id']])) {
                $countryName = $countryById[$city['country_id']]['name'];
                $countrySlug = $createSlug($countryName);
            }
            
            $result[$regionName] = [
                'region_slug' => $regionSlug,
                'country_name' => $countryName,
                'country_slug' => $countrySlug,
                'states' => []
            ];
        }
        
        // Find state entry or create
        $stateIndex = -1;
        foreach ($result[$regionName]['states'] as $index => $stateEntry) {
            if ($stateEntry['state_name'] === $stateName) {
                $stateIndex = $index;
                break;
            }
        }
        
        if ($stateIndex === -1) {
            // Get state slug from states table if not available from destinations
            $stateSlug = null;
            if (isset($stateById[$stateId])) {
                $stateSlug = $createSlug($stateById[$stateId]['name']);
            }
            
            $result[$regionName]['states'][] = [
                'state_name' => $stateName,
                'state_slug' => $stateSlug,
                'type' => null, // No type since this state is not a destination
                'cities' => []
            ];
            $stateIndex = count($result[$regionName]['states']) - 1;
        }
        
        // Add city to the state
        $cityObject = (object) [
            'id' => $city['id'],
            'name' => $city['name'],
            'slug' => $city['slug']
        ];
        
        $result[$regionName]['states'][$stateIndex]['cities'][] = $cityObject;
    }
    
    error_log("DEBUG: Final result has " . count($result) . " regions");
    foreach ($result as $regionName => $regionData) {
        error_log("DEBUG: Region '$regionName' (slug: " . ($regionData['region_slug'] ?? 'none') . ") has " . count($regionData['states']) . " states");
        foreach ($regionData['states'] as $state) {
            error_log("DEBUG: State '" . $state['state_name'] . "' (slug: " . ($state['state_slug'] ?? 'none') . ") has " . count($state['cities']) . " cities");
        }
    }
    
    return $result;
}

    public function getFeatured(){
        return $this->find()
            ->where(['is_feature' => true, 'status' => 'A'])
            ->orderAsc('name')
            ->all()
            ->toArray();
    }


}
     