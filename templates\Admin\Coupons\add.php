<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Coupon $coupon
 */
?>
<?php $this->append('style'); ?>
<style>
/* Remove validation styling */
.form-control:valid,
.form-control:invalid,
.form-select:valid,
.form-select:invalid {
    border-color: #ced4da !important;
    background-image: none !important;
}

.was-validated .form-control:valid,
.was-validated .form-control:invalid,
.was-validated .form-select:valid,
.was-validated .form-select:invalid {
    border-color: #ced4da !important;
    background-image: none !important;
}

.valid-feedback,
.invalid-feedback {
    display: none !important;
}

.nav-tabs .nav-link.active {
    background: #fff;
    border-bottom: 2px solid #007bff;
    color: #007bff;
}
.tab-content {
    padding-top: 1.5rem;
}
.form-label {
    font-weight: 600;
    color: #495057;
}
.required {
    color: #dc3545 !important;
}
.form-control {
    height: 45px;
}
.form-control:not(textarea):not(select) {
    height: 45px;
}
textarea.form-control {
    min-height: 100px;
}
.row.g-3 .col-12, .row.g-3 .col-md-6 {
    margin-bottom: 1rem;
}
</style>
<?php $this->end(); ?>

<div class="main-content bg-container">
    <div class="row">
        <div class="col-12 col-xl-12">
            <div class="card card-primary">
                <div class="d-flex justify-content-between align-items-center">
                    <ul class="breadcrumb breadcrumb-style m-0">
                        <li class="breadcrumb-item">Marketing</li>
                        <li class="breadcrumb-item">
                            <a href="<?= $this->Url->build(['action' => 'index']) ?>">Coupons</a>
                        </li>
                        <li class="breadcrumb-item">Add Coupon</li>
                    </ul>

                    <a href="javascript:void(0);" class="d-flex align-items-center category-back-button breadcrumb m-0" id="back-button-mo" onclick="history.back();">
                        <span class="rotate me-2">⤣</span>
                        <small class="fw-bold"><?= __("BACK") ?></small>
                    </a>
                </div>

                <div class="card-header">
                    <h4>Add New Coupon</h4>
                </div>
                <div class="card-body">
                    <?= $this->Form->create($coupon, ['id' => 'add-coupon-form']) ?>

                    <ul class="nav nav-tabs" id="couponTab" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="basic-tab" data-bs-toggle="tab" href="#basic" role="tab" aria-controls="basic" aria-selected="true">
                                Basic Info
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="discount-tab" data-bs-toggle="tab" href="#discount" role="tab" aria-controls="discount" aria-selected="false">
                                Discount Details
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="limits-tab" data-bs-toggle="tab" href="#limits" role="tab" aria-controls="limits" aria-selected="false">
                                Usage & Validity
                            </a>
                        </li>
                    </ul>
                    <div class="tab-content p-3 border border-top-0" id="couponTabContent">
                        <!-- Basic Information Tab -->
                        <div class="tab-pane fade show active" id="basic" role="tabpanel" aria-labelledby="basic-tab">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <?= $this->Form->control('code', [
                                                'label' => '<span class="form-label fw-bold text-dark">Coupon Code <span class="required">*</span></span>',
                                                'class' => 'form-control',
                                                'placeholder' => 'e.g., SAVE20, WELCOME10',
                                                'required' => true,
                                                'minlength' => 3,
                                                'maxlength' => 20,
                                                'pattern' => '[A-Z0-9]+',
                                                'title' => 'Coupon code must contain only uppercase letters and numbers (3-20 characters)',
                                                'escape' => false,
                                                'style' => 'text-transform: uppercase;'
                                            ]) ?>
                                        </div>
                                        <div class="col-12">
                                            <?= $this->Form->control('title', [
                                                'label' => '<span class="form-label fw-bold text-dark">Title</span>',
                                                'class' => 'form-control',
                                                'placeholder' => 'e.g., Welcome Discount, Summer Sale',
                                                'maxlength' => 100,
                                                'escape' => false
                                            ]) ?>
                                        </div>
                                       
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="row  g-3">
                                        <div class="col-12">
                                            
                                            <?= $this->Form->control('description', [
                                                'label' => '<span class="form-label fw-bold text-dark">Description</span>',
                                               
                                                'class' => 'form-control',
                                                
                                                'placeholder' => 'Describe the coupon and its terms...',
                                                'escape' => false
                                            ]) ?>
                                        </div>
                                         <div class="col-12">
                                            <?= $this->Form->control('status', [
                                                'label' => '<span class="form-label fw-bold text-dark">Status *</span>',
                                                'type' => 'select',
                                                'options' => ['Active' => 'Active', 'Inactive' => 'Inactive'],
                                                'class' => 'form-select',
                                                'default' => 'Active',
                                                'escape' => false
                                            ]) ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        

                        <!-- Discount Settings Tab -->
                        <div class="tab-pane fade" id="discount" role="tabpanel" aria-labelledby="discount-tab">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <?= $this->Form->control('discount_type', [
                                                'label' => '<span class="form-label fw-bold text-dark">Discount Type <span class="required">*</span></span>',
                                                'type' => 'select',
                                                'options' => [
                                                    'percentage' => 'Percentage (%)',
                                                    'fixed' => 'Fixed Amount'
                                                ],
                                                'class' => 'form-select',
                                                'id' => 'discount-type',
                                                'required' => true,
                                                'escape' => false
                                            ]) ?>
                                        </div>
                                        <div class="col-12">
                                            <div class="row">
                                                <div class="col-12">
                                                    <?= $this->Form->control('discount_value', [
                                                        'label' => '<span class="form-label fw-bold text-dark">Discount Value <span class="required">*</span></span>',
                                                        'type' => 'text',
                                                        'class' => 'form-control',
                                                        'placeholder' => '0.00',
                                                        'required' => true,
                                                        'pattern' => '[0-9.]+',
                                                        'title' => 'Please enter only numbers and decimal point',
                                                        'oninput' => 'this.value = this.value.replace(/[^0-9.]/g, "")',
                                                        'escape' => false
                                                    ]) ?>
                                                </div>
                                                <!-- <div class="col-4" id="currency-field">
                                                    <?= $this->Form->control('currency_code', [
                                                        'label' => '<span class="form-label fw-bold text-dark">Currency</span>',
                                                        'class' => 'form-control',
                                                        'placeholder' => 'USD',
                                                        'maxlength' => 10,
                                                        'value' => 'USD',
                                                        'escape' => false
                                                    ]) ?>
                                                </div> -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <?= $this->Form->control('min_cart_value', [
                                                'label' => '<span class="form-label fw-bold text-dark">Minimum Booking Value</span>',
                                                'type' => 'text',
                                                'class' => 'form-control',
                                                'placeholder' => '0.00',
                                                'pattern' => '[0-9.]+',
                                                'title' => 'Please enter only numbers and decimal point',
                                                'oninput' => 'this.value = this.value.replace(/[^0-9.]/g, "")',
                                                'escape' => false
                                            ]) ?>
                                        </div>
                                        <!-- <div class="col-12" id="max-discount-field">
                                            <?= $this->Form->control('max_discount_value', [
                                                'label' => '<span class="form-label fw-bold text-dark">Maximum Discount Amount</span>',
                                                'type' => 'number',
                                                'step' => '0.01',
                                                'min' => '0',
                                                'class' => 'form-control',
                                                'placeholder' => '0.00',
                                                'escape' => false
                                            ]) ?>
                                        </div> -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Usage & Validity Tab -->
                        <div class="tab-pane fade" id="limits" role="tabpanel" aria-labelledby="limits-tab">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <h6 class="fw-bold text-dark mb-3">Usage Limits</h6>
                                        </div>
                                        <div class="col-12">
                                            <?= $this->Form->control('usage_limit', [
                                                'label' => '<span class="form-label fw-bold text-dark">Total Usage Limit</span>',
                                                'type' => 'text',
                                                'class' => 'form-control',
                                                'placeholder' => 'Leave empty for unlimited',
                                                'pattern' => '[0-9]+',
                                                'title' => 'Please enter only numbers',
                                                'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")',
                                                'escape' => false
                                            ]) ?>
                                            <small class="form-text text-muted">Maximum number of times this coupon can be used in total</small>
                                        </div>
                                        <div class="col-12">
                                            <?= $this->Form->control('per_user_limit', [
                                                'label' => '<span class="form-label fw-bold text-dark">Per User Limit</span>',
                                                'type' => 'text',
                                                'class' => 'form-control',
                                                'placeholder' => 'Leave empty for unlimited',
                                                'pattern' => '[0-9]+',
                                                'title' => 'Please enter only numbers',
                                                'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")',
                                                'escape' => false
                                            ]) ?>
                                            <small class="form-text text-muted">Maximum number of times a single user can use this coupon</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <h6 class="fw-bold text-dark mb-3">Validity Period</h6>
                                        </div>
                                        <div class="col-12">
                                            <?= $this->Form->control('start_date', [
                                                'label' => '<span class="form-label fw-bold text-dark">Start Date & Time</span>',
                                                'type' => 'datetime-local',
                                                'class' => 'form-control',
                                                'escape' => false
                                            ]) ?>
                                            <small class="form-text text-muted">Leave empty if the coupon should be active immediately</small>
                                        </div>
                                        <div class="col-12">
                                            <?= $this->Form->control('end_date', [
                                                'label' => '<span class="form-label fw-bold text-dark">End Date & Time</span>',
                                                'type' => 'datetime-local',
                                                'class' => 'form-control',
                                                'min' => date('Y-m-d\TH:i'),
                                                'escape' => false
                                            ]) ?>
                                            <small class="form-text text-muted">Leave empty if the coupon should never expire</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between mt-4">
                        <?= $this->Html->link(__('Cancel'), ['action' => 'index'], ['class' => 'btn btn-secondary']) ?>
                        <div>
                            <?= $this->Form->button(__('Save Draft'), [
                                'type' => 'submit',
                                'name' => 'status',
                                'value' => 'Inactive',
                                'class' => 'btn btn-outline-primary me-2'
                            ]) ?>
                            <?= $this->Form->button(__('Save & Validate'), [
                                'type' => 'submit',
                                'name' => 'status',
                                'value' => 'Active',
                                'class' => 'btn btn-primary',
                                'onclick' => 'return validateForm()'
                            ]) ?>
                        </div>
                    </div>

                    <?= $this->Form->end() ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $this->append('script'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const discountTypeSelect = document.getElementById('discount-type');
    const currencyField = document.getElementById('currency-field');
    const maxDiscountField = document.getElementById('max-discount-field');

    // function toggleFields() {
    //     const isPercentage = discountTypeSelect.value === 'percentage';

    //     // Show/hide currency field based on discount type
    //     if (isPercentage) {
    //         currencyField.style.display = 'none';
    //       //  maxDiscountField.style.display = 'block';
    //     } else {
    //         currencyField.style.display = 'block';
    //        // maxDiscountField.style.display = 'none';
    //     }

    //     // Update placeholder and label for discount value
    //     const discountValueInput = document.querySelector('input[name="discount_value"]');

    //     if (isPercentage) {
    //         discountValueInput.placeholder = 'e.g., 20 (for 20%)';
    //         discountValueInput.max = '100';
    //     } else {
    //         discountValueInput.placeholder = 'e.g., 10.00';
    //         discountValueInput.removeAttribute('max');
    //     }
    // }

    // Initialize on page load
    //toggleFields();

    // Update when discount type changes
    //discountTypeSelect.addEventListener('change', toggleFields);

    // Generate coupon code suggestion
    const codeInput = document.querySelector('input[name="code"]');
    const titleInput = document.querySelector('input[name="title"]');

    if (titleInput && codeInput) {
        titleInput.addEventListener('blur', function() {
            if (!codeInput.value && this.value) {
                // Generate a simple code from title
                const code = this.value
                    .toUpperCase()
                    .replace(/[^A-Z0-9]/g, '')
                    .substring(0, 10);
                if (code.length >= 3) {
                    codeInput.value = code;
                }
            }
        });
    }

    // Set minimum date for end date field
    const endDateInput = document.querySelector('input[name="end_date"]');
    if (endDateInput) {
        const now = new Date();
        const minDateTime = now.toISOString().slice(0, 16);
        endDateInput.min = minDateTime;
    }

    // Setup real-time validation
    setupRealTimeValidation();
});

function validateForm() {
    let isValid = true;
    let missingFields = [];

    // Clear previous tab indicators
    document.querySelectorAll('.nav-link .text-danger').forEach(el => el.remove());

    // Check required fields and mark tabs
    const requiredFields = [
        { name: 'code', label: 'Coupon Code', tab: 'basic-tab' },
        { name: 'discount_type', label: 'Discount Type', tab: 'discount-tab' },
        { name: 'discount_value', label: 'Discount Value', tab: 'discount-tab' }
    ];

    let tabsWithErrors = new Set();

    requiredFields.forEach(field => {
        const input = document.querySelector(`[name="${field.name}"]`);
        if (input && !input.value.trim()) {
            isValid = false;
            missingFields.push(field.label);
            input.style.borderColor = '#dc3545';
            tabsWithErrors.add(field.tab);
        } else if (input) {
            input.style.borderColor = '#ced4da';
        }
    });

    // Check end date is not in the past
    const endDateInput = document.querySelector('input[name="end_date"]');
    if (endDateInput && endDateInput.value) {
        const endDate = new Date(endDateInput.value);
        const now = new Date();
        if (endDate < now) {
            isValid = false;
            alert('End date cannot be in the past.');
            endDateInput.style.borderColor = '#dc3545';
            tabsWithErrors.add('limits-tab');
            return false;
        }
    }

    // Add red asterisk to tabs with errors
    tabsWithErrors.forEach(tabId => {
        const tab = document.getElementById(tabId);
        if (tab && !tab.querySelector('.text-danger')) {
            tab.innerHTML += ' <span class="text-danger">*</span>';
        }
    });

    if (!isValid) {
        alert('Please fill in the following required fields: ' + missingFields.join(', '));
        // Focus on first tab with error
        const firstErrorTab = document.getElementById(Array.from(tabsWithErrors)[0]);
        if (firstErrorTab) {
            firstErrorTab.click();
        }
    }

    return isValid;
}

// Real-time validation to remove asterisks when fields are filled
function setupRealTimeValidation() {
    const requiredFields = [
        { name: 'code', tab: 'basic-tab' },
        { name: 'discount_type', tab: 'discount-tab' },
        { name: 'discount_value', tab: 'discount-tab' }
    ];

    requiredFields.forEach(field => {
        const input = document.querySelector(`[name="${field.name}"]`);
        if (input) {
            input.addEventListener('input', function() {
                if (this.value.trim()) {
                    this.style.borderColor = '#ced4da';
                    // Check if all fields in this tab are now valid
                    const tabFields = requiredFields.filter(f => f.tab === field.tab);
                    const allValid = tabFields.every(f => {
                        const inp = document.querySelector(`[name="${f.name}"]`);
                        return inp && inp.value.trim();
                    });

                    if (allValid) {
                        const tab = document.getElementById(field.tab);
                        const asterisk = tab?.querySelector('.text-danger');
                        if (asterisk) {
                            asterisk.remove();
                        }
                    }
                }
            });
        }
    });
}
</script>
<?php $this->end(); ?>
