<?php

/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\User> $users
 */
?>
<!DOCTYPE html>
<html lang="en">
    
<head>
    <?= $this->Html->meta('csrfToken', $this->request->getAttribute('csrfToken')) ?>
</head>
<style>
    /* Add to your CSS file */
#entriesPerPage {
    background: #f8f9fa;
    border: 1px solid #ced4da;
    border-radius: 6px;
    padding: 4px 8px;
    font-size: 15px;
    color: #495057;
    transition: border-color 0.2s;
}
#entriesPerPage:focus {
    border-color: #80bdff;
    outline: none;
}

    th.no-sort::after,
    th.no-sort::before {
        display: none !important;
    }

    /* Optional: prevent pointer cursor & sort highlight on hover */
    th.no-sort {
        cursor: default !important;
        background-image: none !important;
    }

    td a{
        color: black;
    }

    /* Sortable column styling */
    .sortable {
        cursor: pointer;
        user-select: none;
        transition: background-color 0.2s ease;
    }

    .sortable:hover {
        background-color: #f8f9fa;
    }

    .sortable i {
        margin-left: 5px;
        opacity: 0.6;
    }

    .sortable:hover i {
        opacity: 1;
    }

    /* Filter section styling */
    .filter-action {
        display: none !important;
        /* background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); */
        /* border: 1px solid #dee2e6; */
        border-radius: 8px;
        /* padding: 20px; */
        /* margin: 15px 0; */
        /* box-shadow: 0 2px 4px rgba(0,0,0,0.1); */
    }

    .filter-action.show {
        display: block !important;
    }

    .filter-toggle {
        transition: all 0.3s ease;
        position: relative;
    }

    .filter-toggle:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .filter-action .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }

    .filter-action .form-select {
        border-radius: 6px;
        border: 1px solid #ced4da;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .filter-action .form-select:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    }
</style>
<?php $this->append('style'); ?>
<?php $this->end(); ?>
<div class="main-content">
    <section class="section">
        <div class="section-body1">
            <div class="container-fluid">
              <!-- <div id="deleteErrorMessage" class="text-danger mb-2 p-2 rounded" style="display: none; background-color: #f8d7da; border: 1px solid #f5c6cb;"></div> -->

                <!-- <?php
                    $successMessage = $this->Flash->render();
                    $errorMessage = $this->Flash->render();

                    if (!empty($successMessage)) {
                        echo $successMessage;
                    } elseif (!empty($errorMessage)) {
                        echo $errorMessage;
                    }
                ?> -->
            </div>
        </div>
        <div class="section-body" id="list">
            <div class="container-fluid">
                <div class="card card-primary">
                    <ul class="breadcrumb breadcrumb-style ">
                        <li class="breadcrumb-item">Master Data</li>
                        <li class="breadcrumb-item">Master Data List</li>
                    </ul>
                    <div class="card-header">
                        <div class="d-block d-sm-flex actions">
                            <div class="action-header">
                                <h4>Master Data List</h4>
                            </div>
                            <div class="filter-options">
                                <div class="action-button">
                                    <button onclick="window.location.href='<?= $this->Url->build(['controller' => 'MasterData', 'action' => 'add']) ?>'">
                                        <i class="fas fa-plus"></i> <?= __("Add Master Data") ?>
                                    </button>
                                </div>
                                <div class="filter-button">
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-primary filter-toggle">
                                            <i class="fas fa-filter"></i> Filter
                                        </button>
                                    </div>
                                </div>
                                <div class="download-icon">
                                    <button class="btn btn-primary" id="downloadUsers"><i class="fas fa-file-download"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                   <div class="class23" style="padding:18px">
                    <!-- Search Section -->
                   
                           <div class="d-flex justify-content-between align-items-center mb-3" style="gap: 10px;">
                            <div class="d-flex align-items-center" style="gap: 10px;">
                                <label for="entriesPerPage" class="form-label mb-0" style="font-weight: 500;">Show</label>
                                <select id="entriesPerPage" class="form-select" style="width: 90px; display: inline-block; border-radius: 6px; border: 1px solid #ced4da; padding: 4px 8px;">
                                    <option value="10">10</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                                <span class="ms-2">entries</span>
                            </div>
                            <div class="search-container d-flex align-items-center">
                                <label for="searchInput" class="me-2 mb-0" style="font-weight: 500; white-space: nowrap;">Search:</label>
                                <input type="text"
                                    id="searchInput"
                                    class="form-control"
                                    placeholder="Type to search..."
                                    value="<?= h($search ?? '') ?>"
                                    style="border: 2px solid #e0e0e0; border-radius: 8px; padding: 8px 12px;">
                            </div>
                        </div>

                    <div class='filter-action'>
                        <div class="row g-3 align-items-end">
                            <div class='col-12 col-md-4'>
                                <label for="filterStatus" class="form-label">
                                    <!-- <i class="fas fa-toggle-on me-1"></i> -->
                                    <?= __("Status") ?></label>
                                <select id='filterStatus' class='form-control form-select'>
                                    <option value=""><?= __("All Status") ?></option>
                                    <option value="A" <?= ($status ?? '') === 'A' ? 'selected' : '' ?>><?= __("Active") ?></option>
                                    <option value="I" <?= ($status ?? '') === 'I' ? 'selected' : '' ?>><?= __("Inactive") ?></option>
                                </select>
                            </div>
                            <div class='col-12 col-md-4'>
                                <label for="filterType" class="form-label">
                                    <!-- <i class="fas fa-tags me-1"></i> -->
                                    <?= __("Type") ?></label>
                                <select id='filterType' class='form-control form-select'>
                                    <option value=""><?= __("All Types") ?></option>
                                    <option value="yoga_style" <?= ($type ?? '') === 'yoga_style' ? 'selected' : '' ?>><?= __("Yoga Style") ?></option>
                                    <option value="special_need" <?= ($type ?? '') === 'special_need' ? 'selected' : '' ?>><?= __("Special Need") ?></option>
                                    <option value="accommodation" <?= ($type ?? '') === 'accommodation' ? 'selected' : '' ?>><?= __("Accommodation") ?></option>
                                    <option value="food" <?= ($type ?? '') === 'food' ? 'selected' : '' ?>><?= __("Food") ?></option>
                                    <option value="technique" <?= ($type ?? '') === 'technique' ? 'selected' : '' ?>><?= __("Technique") ?></option>
                                    <option value="addon" <?= ($type ?? '') === 'addon' ? 'selected' : '' ?>><?= __("Addon") ?></option>
                                    <option value="level" <?= ($type ?? '') === 'level' ? 'selected' : '' ?>><?= __("Level") ?></option>
                                    <option value="location_type" <?= ($type ?? '') === 'location_type' ? 'selected' : '' ?>><?= __("Location Type") ?></option>
                                    <option value="climate" <?= ($type ?? '') === 'climate' ? 'selected' : '' ?>><?= __("Climate") ?></option>
                                </select>
                            </div>
                            <div class='col-6 col-md-3'>
                                <div class="d-flex gap-2">
                                    <button type='button' id='filter' class='btn btn-primary flex-fill'>
                                        <i class='fas fa-search me-1'></i> <?= __("Apply Filter") ?>
                                    </button>
                                    <button type="button" class="btn btn-primary" onclick="resetFilters()">
                                        <i class="fas fa-undo me-1"></i> <?= __("Reset") ?>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                        <!-- Error/Success Message Area -->
                        <div id="messageContainer"></div>
                       
                        <div id="table-container">
                            <?= $this->element('Admin/MasterData/table_content', compact('masters', 'pagination')) ?>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Vertically Center Modal -->
    <div class="modal fade" id="exampleModalCenter" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalCenterTitle">Delete Confirmation</h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p class="icon"><i class="fas fa-exclamation-triangle danger"></i></p>
                    <p>Are you sure you want to delete master data <strong id="masterTitle"></strong>?</p>
                </div>
                <div class="modal-footer br">
                    <button type="button" class="btn btn-primary confirm-delete" id="confirmDeleteBtn">Delete</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
    
</div>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script src="<?= $this->Url->webroot('js/delete.js'); ?>"></script>
<script>
    $(document).on('click', '.pagination-link', function(e) {
    e.preventDefault();

    if ($(this).parent().hasClass('disabled')) {
        return;
    }

    var page = $(this).data('page');
    var entriesPerPage = $('#entriesPerPage').val(); // <-- get current limit

    if (page && page > 0) {
        currentPage = page;
        loadData(page, null, null, entriesPerPage); // <-- pass limit
    }
});
    // Initialize DataTable function (disabled for server-side sorting)
    function initializeDataTable() {
        // Destroy existing DataTable if it exists
        if ($.fn.DataTable.isDataTable('#master-data-table')) {
            $('#master-data-table').DataTable().destroy();
        }

        // Don't initialize DataTable for sorting - we're using server-side sorting
        // Just add basic styling if needed
        $('#master-data-table').addClass('table-sortable');
    }

    // Initialize DataTable on page load
    initializeDataTable();

    // Track current page for reload after delete
    var currentPage = 1;

    // Function to show messages at the top of the table
    function showMessage(message, type = 'success') {
        var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        var iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';

        var messageHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
            '<i class="fas ' + iconClass + ' me-2"></i>' + message +
            '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
            '</div>';

        $('#messageContainer').html(messageHtml);

        // Auto-hide after 5 seconds
        setTimeout(function() {
            $('#messageContainer .alert').fadeOut(500, function() {
                $(this).remove();
            });
        }, 5000);
    }

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Delete master data
    $(document).on('click', '.delete-master', function() {
        var masterId = $(this).data('id');
        var masterTitle = $(this).data('title');

        $('#masterTitle').text(masterTitle);
        $('#confirmDeleteBtn').data('id', masterId);
        $('#exampleModalCenter').modal('show');
    });

    // Reset modal when it's opened
    $('#exampleModalCenter').on('show.bs.modal', function () {
        $('#confirmDeleteBtn').prop('disabled', false).text('Delete');
    });

    // Search functionality with real-time search on keypress
    var searchTimeout;

    $('#searchInput').on('input keyup', function() {
        clearTimeout(searchTimeout);
        var searchTerm = $(this).val().trim();

        // Debounce search - wait 500ms after user stops typing
        searchTimeout = setTimeout(function() {
            performSearch(searchTerm);
        }, 500);
    });

    // Clear search when input is completely empty
    $('#searchInput').on('keyup', function(e) {
        if (e.which === 8 || e.which === 46) { // Backspace or Delete
            var searchTerm = $(this).val().trim();
            if (searchTerm === '') {
                clearTimeout(searchTimeout);
                performSearch('');
            }
        }
    });

    function performSearch(searchTerm) {
        if (searchTerm === undefined) {
            searchTerm = $('#searchInput').val().trim();
        }

        var filterStatus = $('#filterStatus').val();
        var filterType = $('#filterType').val();

        // Use AJAX for real-time search instead of page reload
        loadDataWithSearch(1, searchTerm, filterStatus, filterType);
    }


    $('#entriesPerPage').on('change', function() {
    var entries = $(this).val();
    var searchTerm = $('#searchInput').val().trim();
    var filterStatus = $('#filterStatus').val();
    var filterType = $('#filterType').val();
    loadDataWithSearch(1, searchTerm, filterStatus, filterType, entries);
});

    function loadDataWithSearch(page, searchTerm, filterStatus, filterType,entriesPerPage) {
        var currentSort = new URLSearchParams(window.location.search).get('sort');
        var currentDirection = new URLSearchParams(window.location.search).get('direction');

        var requestData = {
            status: filterStatus,
            type: filterType,
            search: searchTerm,
            page: page,
            ajax: 1,
             limit: entriesPerPage || $('#entriesPerPage').val() || 10,
        };

        // Preserve current sorting
        if (currentSort) {
            requestData.sort = currentSort;
            requestData.direction = currentDirection || 'desc';
        }

        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'MasterData', 'action' => 'index']) ?>',
            type: 'GET',
            data: requestData,
            beforeSend: function() {
                $('#table-container').html('<div class="text-center p-4"><i class="fas fa-spinner fa-spin"></i> Loading...</div>');
            },
            success: function(response) {
                $('#table-container').html(response);

                // Reinitialize DataTable for basic styling
                initializeDataTable();

                // Reinitialize tooltips for new content
                var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });

                // Update URL without page reload
                var url = new URL(window.location.href);
                if (searchTerm) {
                    url.searchParams.set('search', searchTerm);
                } else {
                    url.searchParams.delete('search');
                }
                if (page > 1) {
                    url.searchParams.set('page', page);
                } else {
                    url.searchParams.delete('page');
                }
                window.history.replaceState({}, '', url.toString());
            },
            error: function() {
                console.error('Failed to load search results');
                $('#table-container').html('<div class="text-center p-4 text-danger">Failed to load results. Please try again.</div>');
            }
        });
    }

     // Filter toggle functionality with CSS classes
    $('.filter-toggle').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        var $filterSection = $('.filter-action');
        var $button = $(this);

        // Toggle using CSS class
        if ($filterSection.hasClass('show')) {
            // Hide the filter
            $filterSection.removeClass('show');
            $button.html('<i class="fas fa-filter"></i> Filter');
            $button.removeClass('btn-outline-primary').addClass('btn-primary');
        } else {
            // Show the filter
            $filterSection.addClass('show');
            $button.html('<i class="fas fa-times"></i> Hide Filter');
            $button.removeClass('btn-primary').addClass('btn-outline-primary');
        }
    });

    // Prevent filter section from closing when clicking inside it
    $('.filter-action').on('click', function(e) {
        e.stopPropagation();
    });


    // // Filter toggle functionality (same as Coupons/Reviews)
    // $('.filter-toggle').click(function () {
    //     $('.filter-action').slideToggle();
    // });

    // Current page tracking
    var currentPage = 1;

    function resetFilters() {
        $('#filterStatus').val('');
        $('#filterType').val('');
        currentPage = 1;
        loadData(1);
    }

    $('#filter').on('click', function(event) {
        event.preventDefault();
        currentPage = 1;
        loadData(1);
    });

    // Pagination click handler
    $(document).on('click', '.pagination-link', function(e) {
        e.preventDefault();

        if ($(this).parent().hasClass('disabled')) {
            return;
        }

        var page = $(this).data('page');

        if (page && page > 0) {
            currentPage = page;
            loadData(page);
        }
    });

    // Sorting click handler
    $(document).on('click', '.sortable', function(e) {
        e.preventDefault();

        var sortField = $(this).data('sort');
        var currentSort = new URLSearchParams(window.location.search).get('sort');
        var currentDirection = new URLSearchParams(window.location.search).get('direction') || 'desc';

        // Determine new direction
        var newDirection = 'asc';
        if (currentSort === sortField && currentDirection === 'asc') {
            newDirection = 'desc';
        }

        // Load data with new sorting
        loadData(1, sortField, newDirection); // Reset to page 1 when sorting

        // Update URL
        var url = new URL(window.location.href);
        url.searchParams.set('sort', sortField);
        url.searchParams.set('direction', newDirection);
        url.searchParams.set('page', '1'); // Reset to page 1
        window.history.replaceState({}, '', url.toString());
    });

    function loadData(page = 1, sort = null, direction = null,entriesPerPage = null) {
        var filterStatus = $('#filterStatus').val();
        var filterType = $('#filterType').val();
        var searchTerm = $('#searchInput').val().trim();

        var requestData = {
            status: filterStatus,
            type: filterType,
            search: searchTerm,
            page: page,
            ajax: 1,
            limit: entriesPerPage || $('#entriesPerPage').val() || 10 
        };

        // Add sorting parameters if provided
        if (sort) {
            requestData.sort = sort;
            requestData.direction = direction || 'asc';
        }

        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'MasterData', 'action' => 'index']) ?>',
            type: 'GET',
            data: requestData,
            beforeSend: function() {
                $('#table-container').html('<div class="text-center p-4"><i class="fas fa-spinner fa-spin"></i> Loading...</div>');
            },
            success: function(response) {
                $('#table-container').html(response);

                // Reinitialize DataTable for basic styling
                initializeDataTable();

                // Reinitialize tooltips for new content
                var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });
            },
            error: function(xhr, status, error) {
                $('#table-container').html('<div class="alert alert-danger">Error loading data. Please try again.</div>');
            }
        });
    }

    // Update delete functionality to work with new modal
    $('#confirmDeleteBtn').on('click', function () {
        var masterId = $(this).data('id');

        // Hide any previous error messages
        // $('#deleteErrorMessage').hide();

        if (masterId) {
            // Disable the delete button to prevent multiple clicks
            $(this).prop('disabled', true).text('Deleting...');

            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'MasterData', 'action' => 'delete']) ?>/" + masterId,
                type: 'POST',
                headers: {
                    'X-CSRF-Token': $('meta[name="csrfToken"]').attr('content')
                },
                dataType: 'json',
                success: function (res) {
                    if (res.success) {
                        $('#exampleModalCenter').modal('hide');

                        // Show success message at the top of table
                        showMessage(res.message, 'success');

                        // Reload the table data
                        loadData(currentPage);
                    } else {
                        // Show error message at the top of table
                        $('#exampleModalCenter').modal('hide');
                        showMessage(res.message, 'error');
                        $('#confirmDeleteBtn').prop('disabled', false).text('Delete');
                    }
                },
                error: function (xhr) {
                    let msg = 'Delete failed. Please try again.';
                    try {
                        var response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            msg = response.message;
                        }
                    } catch (e) {
                        // Use default message if JSON parsing fails
                    }

                    // Hide modal and show error message at top of table
                    $('#exampleModalCenter').modal('hide');
                    showMessage(msg, 'error');
                    $('#confirmDeleteBtn').prop('disabled', false).text('Delete');
                }
            });
        } else {
            showMessage('Invalid master data ID.', 'error');
        }
    });



    // Download CSV
    $(document).ready(function () {
        $("#downloadUsers").on("click", function () {
            var status = $("#filterStatus").val();
            var type = $("#filterType").val();
            var downloadUrl = '<?= $this->Url->build(['controller' => 'MasterData', 'action' => 'exportmasters']) ?>';

            var params = [];
            if (status !== "") {
                params.push("status=" + encodeURIComponent(status));
            }
            if (type !== "") {
                params.push("type=" + encodeURIComponent(type));
            }

            if (params.length > 0) {
                downloadUrl += "?" + params.join("&");
            }

            console.log("Downloading CSV with filters:", {status: status, type: type});
            window.location.href = downloadUrl;
        });
    });

</script>
<?php $this->end(); ?>
