<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * Coupons Model
 *
 * @method \App\Model\Entity\Coupon newEmptyEntity()
 * @method \App\Model\Entity\Coupon newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Coupon> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Coupon get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Coupon findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Coupon patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Coupon> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Coupon|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Coupon saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Coupon>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Coupon> saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Coupon>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Coupon> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Coupon>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Coupon> deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Coupon>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Coupon> deleteManyOrFail(iterable $entities, array $options = [])
 */
class CouponsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('coupons');
        $this->setDisplayField('title');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp', [
            'events' => [
                'Model.beforeSave' => [
                    'created_at' => 'new',
                    'updated_at' => 'always'
                ]
            ]
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->scalar('code')
            ->maxLength('code', 50)
            ->requirePresence('code', 'create')
            ->notEmptyString('code')
            ->add('code', 'unique', ['rule' => 'validateUnique', 'provider' => 'table']);

        $validator
            ->scalar('title')
            ->maxLength('title', 100)
            ->allowEmptyString('title');

        $validator
            ->scalar('description')
            ->allowEmptyString('description');

        $validator
            ->scalar('discount_type')
            ->requirePresence('discount_type', 'create')
            ->notEmptyString('discount_type')
            ->inList('discount_type', ['percentage', 'fixed']);

        $validator
            ->decimal('discount_value')
            ->allowEmptyString('discount_value')
            ->greaterThan('discount_value', 0);

        $validator
            ->scalar('currency_code')
            ->maxLength('currency_code', 10)
            ->allowEmptyString('currency_code');

        $validator
            ->decimal('min_cart_value')
            ->allowEmptyString('min_cart_value')
            ->greaterThanOrEqual('min_cart_value', 0);

        $validator
            ->decimal('max_discount_value')
            ->allowEmptyString('max_discount_value')
            ->greaterThan('max_discount_value', 0);

        $validator
            ->integer('usage_limit')
            ->allowEmptyString('usage_limit')
            ->greaterThan('usage_limit', 0);

        $validator
            ->integer('per_user_limit')
            ->allowEmptyString('per_user_limit')
            ->greaterThan('per_user_limit', 0);

        $validator
            ->dateTime('start_date')
            ->allowEmptyDateTime('start_date');

        $validator
            ->dateTime('end_date')
            ->allowEmptyDateTime('end_date');

        $validator
            ->scalar('status')
            ->notEmptyString('status')
            ->inList('status', ['Active', 'Inactive']);

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->isUnique(['code']), ['errorField' => 'code']);

        // Custom rule to ensure end_date is after start_date
        $rules->add(function ($entity, $options) {
            if ($entity->start_date && $entity->end_date) {
                return $entity->end_date >= $entity->start_date;
            }
            return true;
        }, 'validDateRange', [
            'errorField' => 'end_date',
            'message' => 'End date must be after start date.'
        ]);

        return $rules;
    }

    /**
     * Find active coupons
     */
    public function findActive(SelectQuery $query, array $options): SelectQuery
    {
        return $query->where([
            'Coupons.status' => 'Active',
            'OR' => [
                'Coupons.start_date IS NULL',
                'Coupons.start_date <=' => date('Y-m-d H:i:s')
            ],
            'OR' => [
                'Coupons.end_date IS NULL',
                'Coupons.end_date >=' => date('Y-m-d H:i:s')
            ]
        ]);
    }

    /**
     * Get filter counts for the index page
     */
    public function getFilterCounts(array $filters = []): array
    {
        $counts = [];

        // Status counts
        $counts['status'] = [];
        $statusCounts = $this->find()
            ->select(['status', 'count' => 'COUNT(*)'])
            ->group(['status'])
            ->toArray();

        foreach ($statusCounts as $statusCount) {
            $counts['status'][$statusCount->status] = $statusCount->count;
        }

        // Discount type counts
        $counts['discount_type'] = [];
        $typeCounts = $this->find()
            ->select(['discount_type', 'count' => 'COUNT(*)'])
            ->group(['discount_type'])
            ->toArray();

        foreach ($typeCounts as $typeCount) {
            $counts['discount_type'][$typeCount->discount_type] = $typeCount->count;
        }

        return $counts;
    }

    /**
     * Apply filters to query
     */
    public function applyFilters(SelectQuery $query, array $filters): SelectQuery
    {
        if (!empty($filters['search'])) {
            $query->where([
                'OR' => [
                    'Coupons.code LIKE' => '%' . $filters['search'] . '%',
                    'Coupons.title LIKE' => '%' . $filters['search'] . '%',
                    'Coupons.description LIKE' => '%' . $filters['search'] . '%'
                ]
            ]);
        }

        if (!empty($filters['status'])) {
            $query->where(['Coupons.status' => $filters['status']]);
        }

        if (!empty($filters['discount_type'])) {
            $query->where(['Coupons.discount_type' => $filters['discount_type']]);
        }

        if (!empty($filters['date_from'])) {
            $query->where(['Coupons.created_at >=' => $filters['date_from']]);
        }

        if (!empty($filters['date_to'])) {
            $query->where(['Coupons.created_at <=' => $filters['date_to']]);
        }

        return $query;
    }
}
