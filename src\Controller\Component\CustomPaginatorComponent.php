<?php
namespace App\Controller\Component;

use <PERSON>ake\Controller\Component;
use Cake\Datasource\RepositoryInterface;
use Cake\ORM\Query;

class CustomPaginatorComponent extends Component
{

    public function initialize(array $config): void
    {
        parent::initialize($config); // Call parent initialization
        // Optionally merge custom configurations
        $this->setConfig($config);
    }

    public function paginate(Query $query, array $options = [])
    {
        // Set defaults if not provided
        $limit = $options['limit'] ?? 5;
        $page = $options['page'] ?? 1;

        // Get total items count
        $totalItems = $query->count();

        // Calculate offset based on limit and page
        $offset = ($page - 1) * $limit;

        // Fetch paginated items
        $items = $query
            ->limit($limit)
            ->offset($offset)
            ->toArray();

        // Calculate total pages
        $totalPages = ceil($totalItems / $limit);

        return [
            'items' => $items,
            'pagination' => [
                'total_items' => $totalItems,
                'total_pages' => $totalPages,
                'current_page' => $page,
                'per_page' => $limit,
                'limit' => $limit,
                'has_prev' => $page > 1,
                'has_next' => $page < $totalPages,
                'has_more' => $page < $totalPages
            ]
        ];
    }
}