<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Offer $offer
 */
?>
<?php $this->append('style'); ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <?= $this->Html->meta('csrfToken', $this->request->getAttribute('csrfToken')) ?>

    <link rel="stylesheet" href="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.css') ?>">
    <link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
    <link rel="stylesheet" href="<?= $this->Url->webroot('bundles/intlTelInput/css/intlTelInput.min.css') ?>">
    <script src="<?= $this->Url->webroot('bundles/intlTelInput/js/intlTelInput.min.js') ?>"></script>
    <script src="<?= $this->Url->webroot('bundles/intlTelInput/js/utils.js') ?>"></script>
</head>
<?php $this->end(); ?>

<div class="main-content bg-container">
    <div class="row">
        <div class="col-12 col-xl-12">
            <div class="card card-primary">
                <div class="d-flex justify-content-between align-items-center">
                    <ul class="breadcrumb breadcrumb-style m-0">
                        <li class="breadcrumb-item">
                            <a href="<?= $this->Url->build(['controller' => 'MasterData', 'action' => 'index']) ?>">Master Data</a>
                        </li>
                        <li class="breadcrumb-item">Edit Master Data</li>
                    </ul>

                    <a href="javascript:void(0);" class="d-flex align-items-center category-back-button  breadcrumb m-0" id="back-button-mo" onclick="history.back();">
                        <span class="rotate me-2">⤣</span>
                        <small class="fw-bold" data-translate="back"><?= __("BACK") ?></small>
                    </a>
                </div>
                <?php
                    $successMessage = $this->Flash->render();
                    $errorMessage = $this->Flash->render();

                    if (!empty($successMessage)) {
                        echo $successMessage;
                    } elseif (!empty($errorMessage)) {
                        echo $errorMessage;
                    }
                ?>

                <div class="card-header">
                    <h4>Edit Master Data</h4>
                </div>
                <div class="card-body">
                    <?php echo $this->Form->create($master, ['id' => 'edit', 'type' => 'file']); ?>
                    <input type="hidden" id="record_id" value="<?= $master->id; ?>">

                        <!-- Type -->
                        <div class="form-group row">
                            <label for="type" class="col-sm-2 col-form-label"><?= __("Type") ?></label>
                            <div class="col-sm-6 main-field">
                                <?= $this->Form->control('type', [
                                    'type' => 'select',
                                    'options' => ['yoga_style' => 'Yoga Style', 'special_need' => 'Special Need', 'accommodation' => 'Accommodation', 'food' => 'Food', 'technique' => 'Technique', 'addon' => 'Addon', 'level' => 'Level', 'location_type' => 'Location Type', 'climate' => 'Climate', 'domestic_tax_percentage' => 'Domestic Tax Percentage', 'international_tax_percentage' => 'International Tax Percentage'],
                                    'empty' => 'Select Type',
                                    'class' => 'form-control form-select',
                                    'label' => false,
                                    'id' => 'type',
                                     'required' => true,
                                ]) ?>
                            </div>
                        </div>

                        <!-- Title -->
                        <div class="form-group row">
                            <label for="title" class="col-sm-2 col-form-label"><?= __("Title") ?></label>
                            <div class="col-sm-6 main-field">
                                <?= $this->Form->control('title', [
                                    'type' => 'text',
                                    'class' => 'form-control',
                                    'label' => false,
                                    'required' => true,
                                    'placeholder' => 'Enter Title',
                                    'id' => 'title'
                                ]) ?>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="form-group row">
                            <label for="description" class="col-sm-2 col-form-label"><?= __("Description") ?></label>
                            <div class="col-sm-6 main-field">
                                <?= $this->Form->control('description', [
                                    'type' => 'textarea',
                                    'class' => 'form-control',
                                    'label' => false,
                                   
                                    'placeholder' => 'Enter Description',
                                    'id' => 'description'
                                ]) ?>
                            </div>
                        </div>

                         <!-- Status -->
                         <div class="form-group row">
                            <label for="status" class="col-sm-2 col-form-label"><?= __("Status") ?></label>
                            <div class="col-sm-6 main-field">
                                <?= $this->Form->control('status', [
                                    'type' => 'select',
                                    'class' => 'form-control form-select',
                                    'id' => 'status',
                                    'options' => [
                                        'A' => __('Active'),
                                        'I' => __('Inactive')
                                    ],
                                    'empty' => __('Select Status'),
                                    'label' => false
                                ]) ?>
                            </div>
                        </div>

                        <div class="form-group row">
                            <div class="col-sm-10 offset-sm-2">
                                <button type="submit" class="btn btn-primary" id="saveButton" data-translate="save"><?= __("Save") ?></button>
                            </div>
                        </div>

                    <?= $this->Form->end(); ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->append('script'); ?>
<script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/jquery.validate.min.js"></script>
<?php $this->end(); ?>
