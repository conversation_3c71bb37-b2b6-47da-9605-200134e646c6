<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\ORM\TableRegistry;

/**
 * Reviews Model
 *
 * @method \App\Model\Entity\Review newEmptyEntity()
 * @method \App\Model\Entity\Review newEntity(array $data, array $options = [])
 * @method \App\Model\Entity\Review[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Review get($primaryKey, $options = [])
 * @method \App\Model\Entity\Review findOrCreate($search, ?callable $callback = null, $options = [])
 * @method \App\Model\Entity\Review patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\Review[] patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Review|false save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\Review saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\Review[]|\Cake\Datasource\ResultSetInterface|false saveMany(iterable $entities, $options = [])
 * @method \App\Model\Entity\Review[]|\Cake\Datasource\ResultSetInterface saveManyOrFail(iterable $entities, $options = [])
 * @method \App\Model\Entity\Review[]|\Cake\Datasource\ResultSetInterface|false deleteMany(iterable $entities, $options = [])
 * @method \App\Model\Entity\Review[]|\Cake\Datasource\ResultSetInterface deleteManyOrFail(iterable $entities, $options = [])
 */
class ReviewsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('reviews');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp', [
            'events' => [
                'Model.beforeSave' => [
                    'created_at' => 'new',
                    'updated_at' => 'always'
                ]
            ]
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('customer_id')
            ->requirePresence('customer_id', 'create')
            ->notEmptyString('customer_id');

        $validator
            ->integer('booking_id')
            ->allowEmptyString('booking_id');

        $validator
            ->integer('booking_item_id')
            ->allowEmptyString('booking_item_id');

        $validator
            ->scalar('review_type')
            ->maxLength('review_type', 255)
            ->requirePresence('review_type', 'create')
            ->notEmptyString('review_type')
            ->inList('review_type', ['course', 'teacher', 'center'], 'Please select a valid review type');

        $validator
            ->integer('reference_id')
            ->requirePresence('reference_id', 'create')
            ->notEmptyString('reference_id');

        $validator
            ->integer('rating')
            ->requirePresence('rating', 'create')
            ->notEmptyString('rating')
            ->range('rating', [1, 5], 'Rating must be between 1 and 5');

        $validator
            ->scalar('comment')
            ->allowEmptyString('comment')
            ->maxLength('comment', 1000, 'Comment cannot exceed 1000 characters');

        $validator
            ->scalar('status')
            ->allowEmptyString('status')
            ->inList('status', ['Pending', 'Approved', 'Rejected'], 'Please select a valid status');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        return $rules;
    }

    /**
     * Find reviews with filters
     *
     * @param \Cake\ORM\Query $query
     * @param array $options
     * @return \Cake\ORM\Query
     */
    public function findFiltered(Query $query, array $options): Query
    {
        if (!empty($options['status'])) {
            $query->where(['Reviews.status' => $options['status']]);
        }

        if (!empty($options['review_type'])) {
            $query->where(['Reviews.review_type' => $options['review_type']]);
        }

        if (!empty($options['rating'])) {
            $query->where(['Reviews.rating' => $options['rating']]);
        }

        if (!empty($options['search'])) {
            $query->where([
                'OR' => [
                    'Reviews.comment LIKE' => '%' . $options['search'] . '%',
                    'Reviews.id' => $options['search']
                ]
            ]);
        }

        return $query;
    }

    /**
     * Get review statistics
     *
     * @return array
     */
    public function getStatistics(): array
    {
        $total = $this->find()->count();
        $pending = $this->find()->where(['status' => 'Pending'])->count();
        $approved = $this->find()->where(['status' => 'Approved'])->count();
        $rejected = $this->find()->where(['status' => 'Rejected'])->count();

        $avgRating = $this->find()
            ->select(['avg_rating' => $this->find()->func()->avg('rating')])
            ->first();

        return [
            'total' => $total,
            'pending' => $pending,
            'approved' => $approved,
            'rejected' => $rejected,
            // 'average_rating' => $avgRating ? round($avgRating->avg_rating, 1) : 0
            'average_rating' =>  0
        ];
    }

    /**
     * Get review type options
     *
     * @return array
     */
    public static function getReviewTypeOptions(): array
    {
        return [
            'course' => 'Course Review',
            'teacher' => 'Teacher Review',
            'center' => 'Center Review'
        ];
    }

    /**
     * Get status options
     *
     * @return array
     */
    public static function getStatusOptions(): array
    {
        return [
            'Pending' => 'Pending',
            'Approved' => 'Approved',
            'Rejected' => 'Rejected'
        ];
    }

    /**
     * Get rating options
     *
     * @return array
     */
    public static function getRatingOptions(): array
    {
        return [
            1 => '1 Star',
            2 => '2 Stars',
            3 => '3 Stars',
            4 => '4 Stars',
            5 => '5 Stars'
        ];
    }
}
