<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * Coupon Entity
 *
 * @property int $id
 * @property string $code
 * @property string|null $title
 * @property string|null $description
 * @property string $discount_type
 * @property float|null $discount_value
 * @property string|null $currency_code
 * @property float|null $min_cart_value
 * @property float|null $max_discount_value
 * @property int|null $usage_limit
 * @property int|null $per_user_limit
 * @property \Cake\I18n\DateTime|null $start_date
 * @property \Cake\I18n\DateTime|null $end_date
 * @property string $status
 * @property \Cake\I18n\DateTime $created_at
 * @property \Cake\I18n\DateTime $updated_at
 */
class Coupon extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'code' => true,
        'title' => true,
        'description' => true,
        'discount_type' => true,
        'discount_value' => true,
        'currency_code' => true,
        'min_cart_value' => true,
        'max_discount_value' => true,
        'usage_limit' => true,
        'per_user_limit' => true,
        'start_date' => true,
        'end_date' => true,
        'status' => true,
        'created_at' => true,
        'updated_at' => true,
    ];

    /**
     * Fields that are excluded from JSON versions of the entity.
     *
     * @var array<string>
     */
    protected array $_hidden = [];

    /**
     * Get formatted discount value with type
     */
    // protected function _getFormattedDiscount(): string
    // {
    //     if ($this->discount_type === 'percentage') {
    //         return $this->discount_value . '%';
    //     } elseif ($this->discount_type === 'fixed') {
    //         return ($this->currency_code ?? 'USD') . ' ' . number_format($this->discount_value, 2);
    //     }
    //     return '';
    // }

    /**
     * Get status badge class for display
     */
    protected function _getStatusBadgeClass(): string
    {
        switch ($this->status) {
            case 'Active':
                return 'badge-success';
            case 'Inactive':
                return 'badge-secondary';
            default:
                return 'badge-secondary';
        }
    }

    /**
     * Check if coupon is currently active
     */
    protected function _getIsActive(): bool
    {
        if ($this->status !== 'Active') {
            return false;
        }

        $now = new \DateTime();
        
        if ($this->start_date && $this->start_date > $now) {
            return false;
        }

        if ($this->end_date && $this->end_date < $now) {
            return false;
        }

        return true;
    }

    /**
     * Get remaining usage count
     */
    protected function _getRemainingUsage(): ?int
    {
        if ($this->usage_limit === null) {
            return null; // Unlimited
        }

        // This would need to be calculated based on actual usage
        // For now, returning the limit as placeholder
        return $this->usage_limit;
    }
}
