<?php

declare(strict_types=1);

namespace App\Controller;

use Cake\Routing\Router;
use Cake\I18n\DateTime;
use Cake\Core\Configure;
use Cake\Http\Exception\BadRequestException;
use Cake\Log\Log;

/**
 * CoursesController Controller
 *
 */
class CoursesController extends AppController
{
    protected $CoursesTable;
    protected $CourseTypesTable;
    protected $YogaStylesTable;
    protected $Partners;
    protected $CourseTypes;
    protected $CourseGalleries;
    protected $Countries;
    protected $States;
    protected $Cities;
    protected $SpecialNeeds;
    protected $MasterData;
    protected $Modalities;
    protected $paginationCount;

    public function initialize(): void
    {
        parent::initialize();

        $this->viewBuilder()->setLayout('default');
        $this->loadComponent('Common');
        $this->paginationCount = $this->viewBuilder()->getVar('paginationCount');
        $this->CoursesTable = $this->fetchTable('Courses');
        $this->CourseTypesTable = $this->fetchTable('CourseTypes');
        $this->YogaStylesTable  = $this->fetchTable('YogaStyles');
        $this->MasterData  = $this->fetchTable('MasterData');
        $this->Partners = $this->fetchTable('Partners');
        $this->CourseTypes = $this->fetchTable('CourseTypes');
        $this->CourseGalleries = $this->fetchTable('CourseGalleries');
        $this->Countries = $this->fetchTable('Countries');
        $this->States = $this->fetchTable('States');
        $this->Cities = $this->fetchTable('Cities');
        $this->SpecialNeeds = $this->fetchTable('SpecialNeeds');
        $this->Modalities = $this->fetchTable('Modalities');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
        $this->Authentication->addUnauthenticatedActions(['index', 'view', 'details']);
    }

    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function index(?string $country = null, ?string $region = null, ?string $state = null, ?string $city = null, ?string $slug = null)
    {
        
        $this->set('pageTitle', 'Explore Yoga Courses Worldwide');
        $this->set('metaDescription', 'Browse and discover verified yoga courses offered by certified partners across various countries and regions.');
        $locationUrl = $city ?? $state ?? $this->request->getQuery('location');
        $courseTypeFilter = $this->request->getQuery('type');
        $specialNeedFilter = $this->request->getQuery('specialNeeds');
        $styleFilter = $this->request->getQuery('style');

        if ($this->request->getParam('type')) {
            $courseTypeFilter = $this->request->getParam('type');
            $locationUrl = '';
        }

        if ($this->request->getParam('specialNeeds')) {
            $specialNeedFilter = $this->request->getParam('specialNeeds');
            $locationUrl = '';
        }

        if ($this->request->getParam('style')) {
            $styleFilter = $this->request->getParam('style');
            $locationUrl = '';
        }

        $modeList = $this->Modalities->selectInputOptions();

        $filters = [
            'search'     => $this->request->getQuery('search'),
            'course_type' => $courseTypeFilter,
            'yoga_style' => $styleFilter,
            'location'   => $locationUrl,
            'course_date' => $this->request->getQuery('courseDate'),
            'language'   => $this->request->getQuery('language'),
            'site'       => $this->request->getQuery('site'),
            'mode'       => $this->request->getQuery('mode'),
            'sort'       => $this->request->getQuery('sort'),
            'special_needs' => $specialNeedFilter,
            'region'    => $this->request->getQuery('region'),
        ];

        $siteUrl = Router::url('/', true);
        $limit= $this->request->getQuery('limit') ?? $this->paginationCount;
        // $limit=100;
        $query = $this->CoursesTable->getList($filters);
        $totalRecords = $query->count();

        $listData = $this->paginate($query, [
            'limit' => $limit,
            'page' => $this->request->getQuery('page') ?? 1,
            'order' => ['Courses.created_at' => 'desc']
        ]);
        // dd($listData);
       
        $course_types = $this->CourseTypes->getNameList();
        $yoga_styles = $this->MasterData->getYogaStyleList();
        $special_needs = $this->MasterData->getspecialNeedList();
        $modes = $this->Modalities->selectInputOptions();

         // $yoga_styles  = $this->YogaStylesTable->getListArray();
        // $special_needs= $this->SpecialNeeds->selectInputOptions();
        $filterCounts = $this->CoursesTable->getFilterCounts($filters);
      
        $start = new DateTime(); // current date
        $end   = (clone $start)->modify('+1 year');

        $months = [];
        while ($start <= $end) {
            $months[] = $start->format('F Y'); // e.g., "April 2025"
            $start = (clone $start)->modify('first day of next month');
        }

        $languages = Configure::read('Language');
        $selectedLang   = $this->request->getParam('lang');
        $selectedCountry = $this->request->getParam('country') ?? 'India';

        $filterLabels = [
            'selectedTypes' => $course_types,
            'selectedStyles'=> $yoga_styles,
            'selectedNeeds' => $special_needs,
            'selectedMode' => $modes
        ];
        
        $allCourseSchema=$this->generateAllCourseSchema($listData);


    
        $bookmarkedCourseIds = [];
        $user = $this->request->getAttribute('identity');
        if ($user) {
            $customerId = $user->id;
            $courseIds = [];
            if ($listData && is_object($listData)) {
                foreach ($listData as $course) {
                    if (is_object($course) && isset($course->id)) {
                        $courseIds[] = $course->id;
                    }
                }
            }
            $bookmarksTable = $this->fetchTable('Bookmarks');
            $bookmarks = $bookmarksTable->find()
                ->where(['customer_id' => $customerId, 'type' => 'course', 'ref_id IN' => $courseIds])
                ->all();
               
           $bookmarkedCourseIds = collection($bookmarks)->extract('ref_id')->toList();
          
        }
        foreach ($listData as $course) {
                $course->is_bookmarked = in_array($course->id, $bookmarkedCourseIds);
            }
            
     
        
        $this->set(compact('listData', 'siteUrl', 'limit', 'totalRecords', 'course_types', 'yoga_styles', 'months', 'languages', 'special_needs', 'filterCounts', 'selectedLang', 'selectedCountry', 'locationUrl', 'courseTypeFilter', 'specialNeedFilter', 'styleFilter','region', 'filterLabels','allCourseSchema', 'modeList','bookmarkedCourseIds'));

        // Check if JSON is requested
        if ($this->request->accepts('application/json')) {
            $this->viewBuilder()->setClassName('Json');

            $pagination = $this->request->getAttribute('paging')['Courses'] ?? [];

            $this->viewBuilder()->setOption('serialize', ['listData', 'pagination', 'totalRecords', 'filterCounts']);

            return;
        }
    }

    public function generateAllCourseSchema($courses)
    {
 
        $items = [];
        foreach ($courses as $index => $course) {
            $lang = 'en'; // dynamically from route or config
            $courseSlug = h($course->slug);
            $countrySlug = $course->country ? $this->slugify($course->country->name) : '';
            $stateSlug = $course->state ? $this->slugify($course->state->name) : '';
            $regionSlug = $course->region->name ?? '';
            $citySlug = $course->city ? $this->slugify($course->city->name) : '';
            $courseId= $course->id;
            // Full SEO URL
            // $seoUrl = Router::url("/$lang/yoga-courses/$countrySlug/$regionSlug/$stateSlug/$citySlug/$courseId-$courseSlug", true);
            $seoUrl = Router::url("/$lang/yoga-courses/$countrySlug/$regionSlug/$stateSlug/$citySlug/$courseSlug", true);
            $items[] = [
                '@type' => 'ListItem',
                'position' => $index + 1,
                 'url' => $seoUrl
            ];
        }

        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'ItemList',
            'name' => 'Explore Yoga Courses Worldwide',
            'description' => 'Browse and discover verified yoga courses offered by certified partners across various countries and regions.',
            'url' => '',
            'numberOfItems' => count($items),
            'itemListElement' => $items
        ];
        return $schema;

    }
    public function slugify($string)
    {
        $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $string)));
        return urlencode($slug); // ensures safe URL usage
    }

    /**
     * View method
     *
     * @param string|null $id Courses Controller id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view(string $slug)
    {
       
        $coursesController = $this->CoursesController->get($id, contain: []);
        $this->set(compact('coursesController'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $coursesController = $this->CoursesController->newEmptyEntity();
        if ($this->request->is('post')) {
            $coursesController = $this->CoursesController->patchEntity($coursesController, $this->request->getData());
            if ($this->CoursesController->save($coursesController)) {
                $this->Flash->success(__('The courses controller has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The courses controller could not be saved. Please, try again.'));
        }
        $this->set(compact('coursesController'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Courses Controller id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $coursesController = $this->CoursesController->get($id, contain: []);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $coursesController = $this->CoursesController->patchEntity($coursesController, $this->request->getData());
            if ($this->CoursesController->save($coursesController)) {
                $this->Flash->success(__('The courses controller has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The courses controller could not be saved. Please, try again.'));
        }
        $this->set(compact('coursesController'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Courses Controller id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $coursesController = $this->CoursesController->get($id);
        if ($this->CoursesController->delete($coursesController)) {
            $this->Flash->success(__('The courses controller has been deleted.'));
        } else {
            $this->Flash->error(__('The courses controller could not be deleted. Please, try again.'));
        }

        return $this->redirect(['action' => 'index']);
    }

    // Course Details - handles both modality-based and location-based URLs
    public function details(...$args)
    {
        // dd($args);
        // Check if this is a modality-based URL
        $modality = $this->request->getParam('modality');

        if ($modality) {
            // Handle modality-based URLs: /yoga-courses/video/slug or /yoga-courses/online/slug
           
            return $this->detailsByModality($modality, $args[0] ?? null);
        } else {
            if (count($args) === 6) {
                // Locality is included
                return $this->detailsByLocation($args[0], $args[1], $args[2], $args[3], $args[4], $args[5]);
            } elseif (count($args) === 5) {
                // Locality is not included
                return $this->detailsByLocation($args[0], $args[1], $args[2], $args[3], null, $args[4]);
            } else {
                throw new BadRequestException('Invalid course URL.');
            }

            // // Handle location-based URLs: /yoga-courses/country/region/state/city/slug
            // return $this->detailsByLocation($args[0] ?? null, $args[1] ?? null, $args[2] ?? null, $args[3] ?? null, $args[4] ?? null, $args[5] ?? null);
        }
    }

    // Course Details by Modality (new method)
    private function detailsByModality(string $modality, string $slug)
    {
        if (!$slug) {
            throw new \Cake\Http\Exception\NotFoundException(__('Course not found.'));
        }

        $Courses = $this->getTableLocator()->get('Courses');
        $ReviewRatings = $this->getTableLocator()->get('ReviewRatings');

        $course = $Courses->find()
            ->where([
                'Courses.slug' => trim($slug),
                'Courses.status' => 'A'
            ])
            ->contain([
                'CourseTypes',
                'CourseGalleries',
                'Partners' => ['Countries','Cities', 'States'],
                'YogaStyleMasterData',
                'SpecialNeedMasterData',
                'Countries',
                'States' => ['Regions'],
                'Cities',
                'CourseBatches',
                'CourseBasePrices.Currencies',
                'Modalities'
            ])
            ->first();
           

        if (empty($course)) {
            throw new \Cake\Http\Exception\NotFoundException(__('Course not found.'));
        }

        // Verify the course has the required modality
        $modalityNames = [];
        if (!empty($course->modalities)) {
            $modalityNames = array_column($course->modalities, 'name');
        }

        $hasRequiredModality = false;
        if ($modality === 'video' && in_array('Online VOD', $modalityNames)) {
            $hasRequiredModality = true;
        } elseif ($modality === 'online' && in_array('Online Live', $modalityNames)) {
            $hasRequiredModality = true;
        }

        if (!$hasRequiredModality) {
            throw new \Cake\Http\Exception\NotFoundException(__('Course not found for this modality.'));
        }

        return $this->renderCourseDetails($course, $modality);
    }

    // Course Details by Location (original method)
    private function detailsByLocation(string $country, string $region, string $state, string $city,?string $locality,string $slug)
    {
        
        if (!$slug) {
            throw new \Cake\Http\Exception\NotFoundException(__('Course not found.'));
        }
       
        $Courses = $this->getTableLocator()->get('Courses');

        $course = $Courses->find()
            ->where([
                'Courses.slug' => trim($slug),
                'Courses.status' => 'A'
            ])
            ->contain([
                'CourseTypes',
                'CourseGalleries',
                'Partners' => ['Countries','Cities', 'States'],
                'YogaStyleMasterData',
                'SpecialNeedMasterData',
                'Countries',
                'States' => ['Regions'],
                'Cities',
                'CourseBatches',
                'CourseBasePrices.Currencies',
                'Modalities'
            ])
            ->first();

        // If course not found by current slug, check old_url for SEO redirect
        if (empty($course)) {
            $course = $Courses->find()
                ->where([
                    'Courses.old_url LIKE' => '%' . trim($slug) . '%',
                    'Courses.status' => 'A'
                ])
                ->contain([
                    'CourseTypes',
                    'CourseGalleries',
                    'Partners' => ['Countries','Cities', 'States'],
                    'YogaStyleMasterData',
                    'SpecialNeedMasterData',
                    'Countries',
                    'States' => ['Regions'],
                    'Cities',
                    'CourseBatches',
                    'CourseBasePrices.Currencies',
                    'Modalities'
                ])
                ->first();

            // If found in old_url, redirect to new URL for SEO
            if (!empty($course)) {
                $newUrl = $this->generateCourseUrl($course);
                return $this->redirect($newUrl, 301); // 301 Permanent Redirect for SEO
            }
        }

        if (empty($course)) {
            throw new \Cake\Http\Exception\NotFoundException(__('Course not found.'));
        }

        return $this->renderCourseDetails($course, null, $country, $state, $city,$locality);
    }


    public function generateCourseSchema($course)
    {
        $schema = [
            "@context" => "https://schema.org",
            "@type" => "Course",
            "name" => $course->name,
            "description" => $course->short_description ?? $course->description,
            "provider" => [
                "@type" => "Organization",
                "name" => $course->partner->name ?? 'Our Yoga School',
                "sameAs" => Router::url('/', true)
            ],
            "educationalCredentialAwarded" => $course->certification_internationally ?? null,
            "hasCourseInstance" => [
                "@type" => "CourseInstance",
                "courseMode" => $course->mode,
                "startDate" => $course->start_date?->format('Y-m-d'),
                "endDate" => $course->end_date?->format('Y-m-d'),
                "location" => [
                    "@type" => "Place",
                    "name" => $course->venue_name ?? $course->address,
                    "address" => [
                        "@type" => "PostalAddress",
                        "addressLocality" => $course->city->name ?? '',
                        "addressRegion" => $course->state->name ?? '',
                        "addressCountry" => $course->country->name ?? ''
                    ]
                ],
                "offers" => [
                    "@type" => "Offer",
                    "price" => $course->price ?? 0,
                    "priceCurrency" => $course->currency->code ?? 'USD',
                    "availability" => "https://schema.org/InStock",
                    //"url" => Router::url(['controller' => 'Courses', 'action' => 'view', $course->slug], true)
                ]
            ]
        ];
          return $schema;
        // return json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }
     public function generateOgTags($course)
    {
         $ogTags = [
                'og:locale' => 'en_US',
                'og:title' => $course->meta_title ?? $course->name,
                'og:description' => $course->meta_description ?? strip_tags(substr($course->short_description, 0, 200)),
                'og:type' => 'article',
                'og:updated_time' => $course->modified_at , 
                // 'og:url' => Router::url(['controller' => 'Courses', 'action' => 'view', $course->slug], true),
                'og:image' => !empty($course->featured_image) 
                    ? Router::url('/img/courses/' . $course->featured_image, true)
                    : Router::url('/img/default-course.png', true),
                 // Twitter Card
                'twitter:card' => 'summary_large_image',
                'twitter:site' => '@yourBrandHandle', 
                'twitter:title' => $course->meta_title ?? $course->name,
                'twitter:description' => $course->meta_description ?? strip_tags(substr($course->short_description, 0, 200)),
                'twitter:image' => !empty($course->image)
                    ? Router::url('/img/courses/' . $course->image, true)
                    : Router::url('/img/default-course.png', true),     
                
            ];
            return $ogTags;
    }

    /**
     * Generate SEO-friendly URL for a course based on modalities
     */
    private function generateCourseUrl($course)
    {
        $lang = $this->request->getParam('lang') ?? 'en';

        // Get course modalities
        $modalityNames = [];
        if (!empty($course->modalities)) {
            $modalityNames = array_column($course->modalities, 'name');
        }

        $hasOnlineVOD = in_array('Online VOD', $modalityNames);
        $hasOnlineLive = in_array('Online Live', $modalityNames);
        $hasOnSite = in_array('On Site', $modalityNames);
        $hasHybrid = in_array('Hybrid', $modalityNames);

        // Determine URL pattern based on modalities
        if ($hasOnlineVOD && !$hasOnlineLive && !$hasOnSite && !$hasHybrid) {
            // Only Online-VOD
            return Router::url("/{$lang}/yoga-courses/video/{$course->slug}", true);
        } elseif ($hasOnlineLive && !$hasOnlineVOD && !$hasOnSite && !$hasHybrid) {
            // Only Online-Live
            return Router::url("/{$lang}/yoga-courses/online/{$course->slug}", true);
        } else {
            // On-site, hybrid, or multiple modalities - use location-based URL
            $country = $this->slugify($course->country->name ?? '');
            $region = $this->slugify($course->state->region->name ?? '');
            $state = $this->slugify($course->state->name ?? '');
            $city = $this->slugify($course->city->name ?? '');
            $slug = $course->slug;

            return Router::url("/{$lang}/yoga-courses/{$country}/{$region}/{$state}/{$city}/{$slug}", true);
        }
    }

    /**
     * Common method to render course details for both modality and location-based URLs
     */
    private function renderCourseDetails($course, $modality = null, $country = null, $state = null, $city = null, $locality = null)
    {
        $ReviewRatings = $this->getTableLocator()->get('ReviewRatings');

        $yogaStyles = [];
        if(!empty($course->yoga_style_master_data)){
            foreach ($course->yoga_style_master_data as $yoga_style) {
                $yogaStyles[] = $yoga_style->title;
            }
        }

        $levels =[];
        if(!empty($course->level)){
            $levelIds = explode(',', $course->level);
            $levels = $this->MasterData->getNamesList($levelIds);
        }

        $modes = [];
        if($course->modalities){
            $modes = $this->Common->formatCourseModes($course, $this->request);
        }

        // Process course galleries to determine media types
        if (!empty($course->course_galleries)) {
            foreach ($course->course_galleries as $gallery) {
                $extension = strtolower(pathinfo($gallery->media, PATHINFO_EXTENSION));
                $gallery->is_video = in_array($extension, ['mp4', 'webm', 'ogg', 'mov', 'mkv', 'avi']);
            }
        }

        // Get review ratings
        $reviewRatings = $ReviewRatings->find()
            ->where([
                'ReviewRatings.course_id' => $course->id,
                'ReviewRatings.status' => 'A'
            ])
            ->all();

        // Get more courses from the same partner
        $moreCourses = $this->CoursesTable->find()
            ->contain([
                'Partners',
                'Countries',
                'States',
                'Cities',
                'Modalities'
            ])
            ->where([
                'Courses.partner_id' => $course->partner_id,
                'Courses.id !=' => $course->id,
                'Courses.status' => 'A'
            ])
            ->select([
                'Courses.id',
                'Courses.name',
                'Courses.slug',
               // 'Courses.image',
                // 'Courses.start_date',
                // 'Courses.end_date',
                'Courses.language'
            ])
            ->select($this->Partners)
            ->select($this->Countries)
            ->select($this->Cities)
            ->select($this->States)
            ->limit(6)
            ->all();

        // Convert more courses to JSON for frontend
        $moreCoursesJson = json_encode($moreCourses->toArray());

        // Get user data if logged in
        $identity = $this->Authentication->getIdentity();
        $userdata = $userId = $isLoggedIn = '';

        if ($identity) {
            $userId = $this->Authentication->getIdentity()->id;
            $usersTable = $this->getTableLocator()->get('Users');
            $userdata = $usersTable->find()
                ->where(['Users.id' => $userId])
                ->first();
            $isLoggedIn = $userId !== null && $userdata->get('user_type') === 'Customer';
        }

        //seo og tags for detail page
        $ogTags=$this->generateOgTags($course);
        $schemaJson = $this->generateCourseSchema($course);
        // get first base price //
        $firstBasePrice = $course->course_base_prices[0] ?? null;
        // dd($country);
        $this->set(compact('course', 'isLoggedIn', 'moreCourses', 'country', 'state', 'city','locality', 'moreCoursesJson','ogTags','schemaJson', 'levels', 'yogaStyles', 'modes', 'firstBasePrice', 'modality'));
    }



}



