<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Booking $booking
 */
?>
<?= $this->Html->meta('csrfToken', $this->request->getAttribute('csrfToken')) ?>

<style>
    .compact-booking-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px 25px;
        border-radius: 12px;
        margin-bottom: 25px;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
    }

    .booking-number {
        font-size: 2rem;
        font-weight: bold;
        color: white;
    }

    .header-info h6 {
        color: white;
        font-weight: 600;
        margin-bottom: 2px;
    }

    .header-info small {
        color: rgba(255, 255, 255, 0.8);
        font-size: 12px;
    }

    .status-badge-header {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 12px;
        letter-spacing: 0.5px;
        border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .status-badge-header.status-confirmed {
        background: rgba(40, 167, 69, 0.9);
        color: white;
        border-color: rgba(40, 167, 69, 0.5);
    }
    .status-badge-header.status-pending {
        background: rgba(255, 193, 7, 0.9);
        color: #212529;
        border-color: rgba(255, 193, 7, 0.5);
    }
    .status-badge-header.status-cancelled {
        background: rgba(220, 53, 69, 0.9);
        color: white;
        border-color: rgba(220, 53, 69, 0.5);
    }
    
    .info-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        border-left: 5px solid #667eea;
        transition: transform 0.3s ease;
    }
    
    .info-card:hover {
        transform: translateY(-5px);
    }
    
    .info-card h5 {
        color: #667eea;
        margin-bottom: 20px;
        font-weight: 600;
    }
    
    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .info-row:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 600;
        color: #666;
    }
    
    .info-value {
        color: #333;
        font-weight: 500;
    }
    
    .timeline {
        position: relative;
        padding-left: 30px;
    }
    
    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #e9ecef;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 30px;
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    }
    
    .timeline-item::before {
        content: '';
        position: absolute;
        left: -37px;
        top: 20px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #667eea;
        border: 3px solid white;
        box-shadow: 0 0 0 3px #e9ecef;
    }
    
    .timeline-icon {
        position: absolute;
        left: -45px;
        top: 15px;
        width: 28px;
        height: 28px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;
    }
    
    .timeline-primary { background: #007bff; }
    .timeline-success { background: #28a745; }
    .timeline-warning { background: #ffc107; }
    .timeline-danger { background: #dc3545; }
    
    .addon-item {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 10px;
        border-left: 4px solid #28a745;
    }
    
    .price-breakdown {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 20px;
        border-radius: 15px;
        margin-top: 20px;
    }
    
    .price-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }
    
    .price-total {
        border-top: 2px solid rgba(255,255,255,0.3);
        padding-top: 15px;
        margin-top: 15px;
        font-size: 1.2rem;
        font-weight: bold;
    }
    
    .status-update-form {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        margin-top: 20px;
    }
    
    .customer-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 24px;
        font-weight: bold;
        margin-right: 15px;
    }

    /* Compact Status Panel */
    .compact-status-panel {
        margin-bottom: 25px;
    }

    .status-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        border: 1px solid #e9ecef;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        display: flex;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        margin-bottom: 15px;
    }

    .status-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        border-color: #667eea;
    }

    .booking-status-card {
        border-left: 4px solid #28a745;
    }

    .payment-status-card {
        border-left: 4px solid #007bff;
    }

    .status-card .status-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
        font-size: 20px;
        color: white;
    }

    .booking-status-card .status-icon {
        background: linear-gradient(135deg, #28a745, #20c997);
    }

    .payment-status-card .status-icon {
        background: linear-gradient(135deg, #007bff, #6610f2);
    }

    .status-info {
        flex: 1;
    }

    .status-info h6 {
        margin: 0 0 8px 0;
        font-size: 12px;
        font-weight: 600;
        color: #6c757d;
        letter-spacing: 1px;
    }

    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        letter-spacing: 0.5px;
        margin-bottom: 5px;
    }

    .status-badge i {
        margin-right: 6px;
        font-size: 8px;
    }

    .status-badge.status-pending { background: #fff3cd; color: #856404; }
    .status-badge.status-confirmed { background: #d4edda; color: #155724; }
    .status-badge.status-cancelled { background: #f8d7da; color: #721c24; }

    .status-badge.payment-pending { background: #fff3cd; color: #856404; }
    .status-badge.payment-paid { background: #d4edda; color: #155724; }
    .status-badge.payment-cancelled { background: #f8d7da; color: #721c24; }
    .status-badge.payment-refunded { background: #d1ecf1; color: #0c5460; }

    .status-info small {
        font-size: 11px;
        color: #6c757d;
    }

    .edit-icon {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
        font-size: 14px;
        transition: all 0.3s ease;
    }

    .status-card:hover .edit-icon {
        background: #667eea;
        color: white;
        transform: scale(1.1);
    }

    /* Modal Styling */
    .modal-content {
        border-radius: 15px;
        border: none;
        box-shadow: 0 20px 60px rgba(0,0,0,0.2);
    }

    .modal-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
        border-bottom: none;
        padding: 20px 25px;
    }

    .modal-title {
        font-weight: 600;
        font-size: 18px;
    }

    .btn-close {
        filter: brightness(0) invert(1);
    }

    .modal-body {
        padding: 25px;
    }

    .current-status-info .alert {
        border-radius: 10px;
        border: none;
        background: #f8f9fa;
        color: #495057;
    }

    .status-option {
        display: block;
        padding: 15px;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
    }

    .status-option:hover {
        border-color: #667eea;
        background: #f8f9fa;
    }

    .form-check-input:checked + .status-option {
        border-color: #667eea;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .option-content {
        display: flex;
        align-items: center;
    }

    .option-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 16px;
        transition: all 0.3s ease;
    }

    .option-icon.pending { background: #fff3cd; color: #856404; }
    .option-icon.confirmed, .option-icon.paid { background: #d4edda; color: #155724; }
    .option-icon.cancelled { background: #f8d7da; color: #721c24; }
    .option-icon.refunded { background: #d1ecf1; color: #0c5460; }

    .form-check-input:checked + .status-option .option-icon {
        background: rgba(255, 255, 255, 0.2);
        color: white;
    }

    .option-text strong {
        display: block;
        font-size: 14px;
        margin-bottom: 2px;
    }

    .option-text small {
        font-size: 12px;
        opacity: 0.8;
    }

    .modal-footer {
        padding: 20px 25px;
        border-top: 1px solid #e9ecef;
    }

    .modal-footer .btn {
        padding: 10px 25px;
        border-radius: 8px;
        font-weight: 600;
    }


    .status-card {
        background: white;
        padding: 15px;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        height: 100%;
    }

    .status-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 20px;
        color: white;
    }

    .booking-status .status-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .payment-status .status-icon {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }

    .status-info label {
        font-size: 12px;
        color: #6c757d;
        text-transform: uppercase;
        font-weight: 600;
        margin-bottom: 5px;
        display: block;
    }

    .current-status {
        font-size: 16px;
        font-weight: bold;
        padding: 5px 12px;
        border-radius: 20px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .current-status.status-pending { background: #fff3cd; color: #856404; }
    .current-status.status-confirmed { background: #d4edda; color: #155724; }
    .current-status.status-cancelled { background: #f8d7da; color: #721c24; }

    .current-status.payment-pending { background: #fff3cd; color: #856404; }
    .current-status.payment-paid { background: #d4edda; color: #155724; }
    .current-status.payment-cancelled { background: #f8d7da; color: #721c24; }
    .current-status.payment-refunded { background: #d1ecf1; color: #0c5460; }

    .status-actions {
        margin-top: 20px;
    }

    .action-section {
        background: white;
        padding: 20px;
        border-radius: 10px;
        border: 1px solid #e9ecef;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }

    .action-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 2px solid #f8f9fa;
    }

    .status-btn-group {
        margin-bottom: 15px;
        width: 100%;
    }

    .status-btn-group .btn {
        flex: 1;
        padding: 10px 15px;
        font-size: 14px;
        font-weight: 500;
        border-width: 2px;
        transition: all 0.3s ease;
    }

    .status-btn-group .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .status-btn-group .btn-check:checked + .btn {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .update-btn {
        width: 100%;
        padding: 12px;
        font-weight: 600;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .update-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(0,0,0,0.2);
    }

    .batch-info, .pricing-info {
        transition: all 0.3s ease;
    }

    .batch-info:hover, .pricing-info:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    /* Professional SweetAlert Customization */
    .professional-popup {
        border-radius: 20px !important;
        box-shadow: 0 20px 60px rgba(0,0,0,0.2) !important;
    }

    .professional-confirm-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border: none !important;
        border-radius: 10px !important;
        padding: 12px 30px !important;
        font-weight: 600 !important;
    }

    .professional-cancel-btn {
        background: #6c757d !important;
        border: none !important;
        border-radius: 10px !important;
        padding: 12px 30px !important;
        font-weight: 600 !important;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .status-management-panel {
            margin: 0 -15px 30px -15px;
            border-radius: 0;
        }

        .panel-header {
            padding: 20px;
        }

        .panel-header h4 {
            font-size: 20px;
        }

        .status-overview {
            padding: 20px;
        }

        .status-controls {
            padding: 20px;
        }

        .control-panel {
            margin-bottom: 20px;
            padding: 20px;
        }

        .option-label {
            padding: 12px 15px;
        }

        .option-icon {
            width: 35px;
            height: 35px;
            margin-right: 12px;
        }

        .update-button {
            padding: 12px;
            font-size: 14px;
        }
    }
    i.fas.fa-question-circle {
    font-size: 45px;
    }
    i.fas.fa-check-circle{
        font-size: 45px;
    }

</style>

<div class="main-content">
    <section class="section">
        <div class="section-body1">
            <div class="container-fluid">
                <?php
                $successMessage = $this->Flash->render('success');
                $errorMessage = $this->Flash->render('error');
                ?>
                <?php if (!empty($successMessage)): ?>
                    <?= $successMessage ?>
                <?php elseif (!empty($errorMessage)): ?>
                    <?= $errorMessage ?>
                <?php endif; ?>
            </div>
        </div>

        <div class="section-body">
            <div class="container-fluid">
                <!-- Compact Booking Header -->
                <div class="compact-booking-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="booking-number">#<?= $booking->id ?></div>
                            <div class="header-info ms-3">
                                <h6 class="mb-0">Booking Details</h6>
                                <small class="text-muted">Created: <?= $booking->created_at->format('M d, Y H:i') ?></small>
                            </div>
                        </div>

                        <div class="d-flex align-items-center">
                            <div class="status-badge-header status-<?= h($booking->booking_status) ?>">
                                <?= strtoupper(h($booking->booking_status)) ?>
                            </div>
                            <!-- Invoice Options Dropdown -->
                            <div class="btn-group me-2" role="group">
                                <button type="button" class="btn btn-sm status-badge-header status-confirmed dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-file-invoice"></i> Invoice Designs
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <a class="dropdown-item" href="<?= $this->Url->build(['action' => 'invoice', $booking->id]) ?>" target="_blank">
                                            <i class="fas fa-file-alt me-2"></i> Standard Invoice
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="<?= $this->Url->build(['action' => 'invoiceModern', $booking->id]) ?>" target="_blank">
                                            <i class="fas fa-magic me-2"></i> Modern Design
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="<?= $this->Url->build(['action' => 'invoiceCorporate', $booking->id]) ?>" target="_blank">
                                            <i class="fas fa-building me-2"></i> Corporate Design
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <a href="<?= $this->Url->build(['action' => 'index']) ?>" class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-arrow-left"></i> Back to List
                            </a>
                            
                        </div>
                    </div>
                </div>

                <!-- Compact Status Display -->
                <div class="row">
                    <div class="col-12">
                        <div class="compact-status-panel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="status-card booking-status-card" data-bs-toggle="modal" data-bs-target="#bookingStatusModal">
                                        <div class="status-icon">
                                            <i class="fas fa-calendar-check"></i>
                                        </div>
                                        <div class="status-info">
                                            <h6>BOOKING STATUS</h6>
                                            <div class="status-badge status-<?= h($booking->booking_status) ?>">
                                                <i class="fas fa-circle"></i>
                                                <?= strtoupper(h($booking->booking_status)) ?>
                                            </div>
                                            <small>Last updated: <?= $booking->modified_at->format('M d, Y H:i') ?></small>
                                        </div>
                                        <div class="edit-icon">
                                            <i class="fas fa-edit"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="status-card payment-status-card" data-bs-toggle="modal" data-bs-target="#paymentStatusModal">
                                        <div class="status-icon">
                                            <i class="fas fa-credit-card"></i>
                                        </div>
                                        <div class="status-info">
                                            <h6>PAYMENT STATUS</h6>
                                            <div class="status-badge payment-<?= h($booking->payment_status ?? 'pending') ?>">
                                                <i class="fas fa-circle"></i>
                                                <?= strtoupper(h($booking->payment_status ?? 'pending')) ?>
                                            </div>
                                            <small>Amount: <?= h($booking->currency ?? 'USD') ?> <?= number_format($booking->grand_total ?? $booking->total_price, 2) ?></small>
                                        </div>
                                        <div class="edit-icon">
                                            <i class="fas fa-edit"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Customer Information -->
                    <div class="col-lg-6">
                        <div class="info-card">
                            <h5><i class="fas fa-user"></i> Customer Information</h5>
                            <div class="d-flex align-items-center mb-3">
                                <div class="customer-avatar">
                                    <?php 
                                    $name = $booking->full_name ?: ($booking->customer->user->first_name ?? 'U');
                                    echo strtoupper(substr($name, 0, 1));
                                    ?>
                                </div>
                                <div>
                                    <h6 class="mb-1">
                                        <?= !empty($booking->full_name) ? h($booking->full_name) : 
                                            (!empty($booking->customer->user) ? h($booking->customer->user->first_name . ' ' . $booking->customer->user->last_name) : 'N/A') ?>
                                    </h6>
                                    <small class="text-muted">Customer ID: <?= $booking->customer_id ?></small>
                                </div>
                            </div>
                            
                            <div class="info-row">
                                <span class="info-label">Email:</span>
                                <span class="info-value">
                                    <?= !empty($booking->email) ? h($booking->email) : 
                                        (!empty($booking->customer->user->email) ? h($booking->customer->user->email) : 'N/A') ?>
                                </span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Phone:</span>
                                <span class="info-value">
                                    <?= !empty($booking->phone) ? h($booking->phone) : 
                                        (!empty($booking->customer->user->mobile) ? h($booking->customer->user->mobile) : 'N/A') ?>
                                </span>
                            </div>
                            <!-- <div class="info-row">
                                <span class="info-label">Age:</span>
                                <span class="info-value"><?= $booking->age ? h($booking->age) . ' years' : 'N/A' ?></span>
                            </div> -->
                            <!-- <div class="info-row">
                                <span class="info-label">Food Preference:</span>
                                <span class="info-value"><?= !empty($booking->food) ? h($booking->food) : 'None specified' ?></span>
                            </div> -->
                        </div>
                    </div>

                    <!-- Course & Booking Details -->
                    <div class="col-lg-6">
                        <div class="info-card">
                            <h5><i class="fas fa-graduation-cap"></i> Course Information</h5>
                            <div class="info-row">
                                <span class="info-label">   Title:</span>
                                <span class="info-value">
                                    <strong><?= !empty($booking->course) ? h($booking->course->name) : 'N/A' ?></strong>
                                </span>
                            </div>
                        
                            <div class="info-row">
                                <span class="info-label">Partner:</span>
                                <span class="info-value">
                                    <?= !empty($booking->partner) ? h($booking->partner->name) : 'N/A' ?>
                                </span>
                            </div>
                            <!-- <div class="info-row">
                                <span class="info-label">Course Duration:</span>
                                <span class="info-value">
                                    <?= !empty($booking->course->duration_days) ? $booking->course->duration_days . ' days' : 'N/A' ?>
                                </span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Base Course Price:</span>
                                <span class="info-value">
                                    <strong><?= !empty($booking->course->price) ? h($booking->course->currency ?? 'USD') . ' ' . number_format($booking->course->price, 2) : 'N/A' ?></strong>
                                </span>
                            </div> -->
                            <div class="info-row">
                                <span class="info-label">Course Type:</span>
                                <span class="info-value">
                                    <?= !empty($booking->course->course_type->name) ? $booking->course->course_type->name  : 'N/A' ?>
                                </span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Language:</span>
                                <span class="info-value">
                                    <strong><?= !empty($booking->course->language) ? $booking->course->language  : 'N/A' ?></strong>
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Batch & Booking Details -->
                    <!-- <div class="col-lg-6">
                        <div class="info-card">
                            <h5><i class="fas fa-calendar-alt"></i> Batch & Booking Details</h5>

                            <?php if (!empty($booking->booking_details)): ?>
                                <?php foreach ($booking->booking_details as $detail): ?>
                                    <?php if (!empty($detail->course_batch)): ?>
                                        <div class="batch-info mb-3 p-3" style="background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff;">
                                            <h6 class="mb-2"><i class="fas fa-users"></i> Selected Batch</h6>
                                            <div class="info-row">
                                                <span class="info-label">Batch Name:</span>
                                                <span class="info-value"><strong><?= h($detail->course_batch->name) ?></strong></span>
                                            </div>
                                            <div class="info-row">
                                                <span class="info-label">Duration:</span>
                                                <span class="info-value"><?= h($detail->course_batch->duration_details ?? 'N/A') ?></span>
                                            </div>
                                            <div class="info-row">
                                                <span class="info-label">Schedule:</span>
                                                <span class="info-value">
                                                    <?= $detail->course_batch->start_date->format('M d') ?> - <?= $detail->course_batch->end_date->format('M d, Y') ?>
                                                </span>
                                            </div>
                                            <div class="info-row">
                                                <span class="info-label">Timing:</span>
                                                <span class="info-value">
                                                    <?= $detail->course_batch->start_time->format('H:i') ?> - <?= $detail->course_batch->end_time->format('H:i') ?>
                                                </span>
                                            </div>
                                           
                                            
                                        </div>

                                        <div class="pricing-info mb-3 p-3" style="background: #e8f5e8; border-radius: 8px; border-left: 4px solid #28a745;">
                                            <h6 class="mb-2"><i class="fas fa-tag"></i> Pricing Details</h6>
                                            <div class="info-row">
                                                <span class="info-label">Price Package:</span>
                                                <span class="info-value"><strong><?= h($detail->base_price_title ?? 'Standard Price') ?></strong></span>
                                            </div>
                                            <div class="info-row">
                                                <span class="info-label">Base Amount:</span>
                                                <span class="info-value">
                                                    <?= h($detail->base_price_currency ?? $booking->currency ?? 'USD') ?> <?= number_format($detail->base_price_amount ?? $booking->total_price, 2) ?>
                                                </span>
                                            </div>
                                            <?php if (!empty($detail->hourly_rate)): ?>
                                            <div class="info-row">
                                                <span class="info-label">Hourly Rate:</span>
                                                <span class="info-value">
                                                    <?= h($detail->currency ?? $booking->currency ?? 'USD') ?> <?= number_format($detail->hourly_rate, 2) ?>/hour
                                                </span>
                                            </div>
                                            <?php endif; ?>
                                            <?php if (!empty($detail->addons)): ?>
                                            <div class="info-row">
                                                <span class="info-label">Included:</span>
                                                <span class="info-value"><?= h($detail->addons) ?></span>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            <?php endif; ?>

                            <div class="info-row">
                                <span class="info-label">Booking Date:</span>
                                <span class="info-value"><?= $booking->booking_date->format('F d, Y') ?></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Payment Status:</span>
                                <span class="info-value">
                                    <span class="badge badge-<?= $booking->payment_status === 'paid' ? 'success' : 'warning' ?>">
                                        <?= ucfirst(h($booking->payment_status ?? 'pending')) ?>
                                    </span>
                                </span>
                            </div>
                        </div>
                    </div>
                    <?php if (!empty($booking->booking_addons)): ?>
                     <div class="col-6">
                        <div class="info-card">
                            <h5><i class="fas fa-plus-circle"></i> Booking Addons</h5>
                            <?php foreach ($booking->booking_addons as $addon): ?>
                                <div class="addon-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong><?= h($addon->addon_title) ?></strong>
                                            <small class="text-muted d-block">Quantity: <?= $addon->quantity ?></small>
                                        </div>
                                        <div class="text-right">
                                            <div><?= h($addon->currency ?? $booking->currency ?? 'USD') ?> <?= number_format($addon->addon_price, 2) ?> each</div>
                                            <strong><?= h($addon->currency ?? $booking->currency ?? 'USD') ?> <?= number_format($addon->total_price, 2) ?></strong>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?> -->
                </div>

                <!-- Participants Section -->
                <?php if (!empty($booking->booking_items)): ?>
                <div class="row">
                    <div class="col-12">
                        <div class="info-card">
                            <h5><i class="fas fa-users"></i> Participants (<?= count($booking->booking_items) ?>)</h5>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th>Phone</th>
                                            <th>Age</th>
                                            <th>Food Preference</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($booking->booking_items as $participant): ?>
                                        <tr>
                                            <td>
                                                <strong><?= h($participant->first_name . ' ' . $participant->last_name) ?></strong>
                                                <br>
                                                <small class="text-muted"><?= h($participant->title) ?></small>
                                            </td>
                                            <td><?= h($participant->email) ?></td>
                                            <td><?= h($participant->phone) ?></td>
                                            <td><?= h($participant->age) ?> years</td>
                                            <td>
                                                <span class="badge badge-info"><?= h($participant->food) ?></span>
                                            </td>
                                            <td>
                                                <strong><?= h($participant->base_price_currency) ?> <?= number_format($participant->base_price_amount ?? 0, 2) ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge badge-<?= ($participant->status ?? 'confirmed') == 'confirmed' ? 'success' : 'secondary' ?>">
                                                    <?= h(ucfirst($participant->status ??'confirmed')) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="<?= $this->Url->build(['action' => 'viewParticipant', $participant->id]) ?>"
                                                   class="btn btn-sm btn-primary" title="View Participant Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Addons Section -->
                <!-- <?php if (!empty($booking->booking_addons)): ?>
                <div class="row">
                    <div class="col-12">
                        <div class="info-card">
                            <h5><i class="fas fa-plus-circle"></i> Booking Addons</h5>
                            <?php foreach ($booking->booking_addons as $addon): ?>
                                <div class="addon-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong><?= h($addon->addon_title) ?></strong>
                                            <small class="text-muted d-block">Quantity: <?= $addon->quantity ?></small>
                                        </div>
                                        <div class="text-right">
                                            <div><?= h($addon->currency ?? $booking->currency ?? 'USD') ?> <?= number_format($addon->addon_price, 2) ?> each</div>
                                            <strong><?= h($addon->currency ?? $booking->currency ?? 'USD') ?> <?= number_format($addon->total_price, 2) ?></strong>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?> -->

                <!-- Price Breakdown -->
                <div class="row">
                    <div class="col-lg-6">
                       
                        <div class="info-card">
                              <h5><i class="fas fa-calculator"></i> Price Breakdown</h5>
                            <div class="price-breakdown">
                               
                                <div class="price-row">
                                    <span>Total Amount:</span>
                                    <span><?= h($booking->billing_currency ?? 'USD') ?> <?= number_format($booking->sub_total, 2) ?></span>
                                </div>
                                <?php if (!empty($booking->discount_value)): ?>
                                <div class="price-row">
                                    <span>Discount:</span>
                                    <span>-<?= h($booking->billing_currency ?? 'USD') ?> <?= number_format($booking->discount_value, 2) ?></span>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($booking->tax_amount)): ?>
                                <div class="price-row">
                                    <span>Tax (<?= $booking->tax_rate ?>%):</span>
                                    <span><?= h($booking->currency ?? 'USD') ?> <?= number_format($booking->tax_amount, 2) ?></span>
                                </div>
                                <?php endif; ?>
                                <?php if ($addonTotal > 0): ?>
                                <!-- <div class="price-row">
                                    <span>Addons Total:</span>
                                    <span><?= h($booking->currency ?? 'USD') ?> <?= number_format($addonTotal, 2) ?></span>
                                </div> -->
                                <?php endif; ?>
                                <div class="price-row price-total">
                                    <span>Grand Total:</span>
                                    <span><?= h($booking->currency ?? 'USD') ?> <?= number_format($booking->grand_total ?? $booking->total_price, 2) ?></span>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>

                <!-- Timeline -->
                <!-- <div class="row">
                    <div class="col-12">
                        <div class="info-card">
                            <h5><i class="fas fa-history"></i> Booking Timeline</h5>
                            <div class="timeline">
                                <?php foreach ($timeline as $event): ?>
                                    <div class="timeline-item">
                                        <div class="timeline-icon timeline-<?= $event['color'] ?>">
                                            <i class="<?= $event['icon'] ?>"></i>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1"><?= h($event['event']) ?></h6>
                                                <p class="mb-0 text-muted"><?= h($event['description']) ?></p>
                                            </div>
                                            <small class="text-muted"><?= $event['date']->format('M d, Y H:i') ?></small>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div> -->
            </div>
        </div>
    </section>
</div>

<!-- Booking Status Update Modal -->
<div class="modal fade" id="bookingStatusModal" tabindex="-1" aria-labelledby="bookingStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bookingStatusModalLabel">
                    <i class="fas fa-calendar-check"></i> Update Booking Status
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <?= $this->Form->create(null, [
                    'url' => ['action' => 'updateStatus', $booking->id],
                    'class' => 'status-update-form',
                    'id' => 'bookingStatusForm'
                ]) ?>
                <div class="current-status-info mb-4">
                    <div class="alert alert-info">
                        <strong>Current Status:</strong>
                        <span class="badge bg-secondary"><?= ucfirst(h($booking->booking_status)) ?></span>
                        <br><small>Last updated: <?= $booking->modified_at->format('M d, Y H:i') ?></small>
                    </div>
                </div>

                <div class="status-options">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="radio" name="status" value="pending" id="modal-booking-pending"
                               <?= $booking->booking_status === 'pending' ? 'checked' : '' ?>>
                        <label class="form-check-label status-option" for="modal-booking-pending">
                            <div class="option-content">
                                <div class="option-icon pending"><i class="fas fa-clock"></i></div>
                                <div class="option-text">
                                    <strong>Pending</strong>
                                    <small>Awaiting confirmation</small>
                                </div>
                            </div>
                        </label>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="radio" name="status" value="confirmed" id="modal-booking-confirmed"
                               <?= $booking->booking_status === 'confirmed' ? 'checked' : '' ?>>
                        <label class="form-check-label status-option" for="modal-booking-confirmed">
                            <div class="option-content">
                                <div class="option-icon confirmed"><i class="fas fa-check-circle"></i></div>
                                <div class="option-text">
                                    <strong>Confirmed</strong>
                                    <small>Booking approved and confirmed</small>
                                </div>
                            </div>
                        </label>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="radio" name="status" value="cancelled" id="modal-booking-cancelled"
                               <?= $booking->booking_status === 'cancelled' ? 'checked' : '' ?>>
                        <label class="form-check-label status-option" for="modal-booking-cancelled">
                            <div class="option-content">
                                <div class="option-icon cancelled"><i class="fas fa-times-circle"></i></div>
                                <div class="option-text">
                                    <strong>Cancelled</strong>
                                    <small>Booking has been cancelled</small>
                                </div>
                            </div>
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> Cancel
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Update Status
                </button>
                <?= $this->Form->end() ?>
            </div>
        </div>
    </div>
</div>

<!-- Payment Status Update Modal -->
<div class="modal fade" id="paymentStatusModal" tabindex="-1" aria-labelledby="paymentStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="paymentStatusModalLabel">
                    <i class="fas fa-credit-card"></i> Update Payment Status
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <?= $this->Form->create(null, [
                    'url' => ['action' => 'updatePaymentStatus', $booking->id],
                    'class' => 'status-update-form',
                    'id' => 'paymentStatusForm'
                ]) ?>
                <div class="current-status-info mb-4">
                    <div class="alert alert-info">
                        <strong>Current Status:</strong>
                        <span class="badge bg-secondary"><?= ucfirst(h($booking->payment_status ?? 'pending')) ?></span>
                        <br><strong>Amount:</strong> <?= h($booking->currency ?? 'USD') ?> <?= number_format($booking->grand_total ?? $booking->total_price, 2) ?>
                    </div>
                </div>

                <div class="status-options">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="radio" name="payment_status" value="pending" id="modal-payment-pending"
                               <?= ($booking->payment_status ?? 'pending') === 'pending' ? 'checked' : '' ?>>
                        <label class="form-check-label status-option" for="modal-payment-pending">
                            <div class="option-content">
                                <div class="option-icon pending"><i class="fas fa-hourglass-half"></i></div>
                                <div class="option-text">
                                    <strong>Pending</strong>
                                    <small>Payment is due</small>
                                </div>
                            </div>
                        </label>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="radio" name="payment_status" value="paid" id="modal-payment-paid"
                               <?= ($booking->payment_status ?? 'pending') === 'paid' ? 'checked' : '' ?>>
                        <label class="form-check-label status-option" for="modal-payment-paid">
                            <div class="option-content">
                                <div class="option-icon paid"><i class="fas fa-check-circle"></i></div>
                                <div class="option-text">
                                    <strong>Paid</strong>
                                    <small>Payment received successfully</small>
                                </div>
                            </div>
                        </label>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="radio" name="payment_status" value="cancelled" id="modal-payment-cancelled"
                               <?= ($booking->payment_status ?? 'pending') === 'cancelled' ? 'checked' : '' ?>>
                        <label class="form-check-label status-option" for="modal-payment-cancelled">
                            <div class="option-content">
                                <div class="option-icon cancelled"><i class="fas fa-ban"></i></div>
                                <div class="option-text">
                                    <strong>Cancelled</strong>
                                    <small>Payment was cancelled</small>
                                </div>
                            </div>
                        </label>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="radio" name="payment_status" value="refunded" id="modal-payment-refunded"
                               <?= ($booking->payment_status ?? 'pending') === 'refunded' ? 'checked' : '' ?>>
                        <label class="form-check-label status-option" for="modal-payment-refunded">
                            <div class="option-content">
                                <div class="option-icon refunded"><i class="fas fa-undo"></i></div>
                                <div class="option-text">
                                    <strong>Refunded</strong>
                                    <small>Amount has been refunded</small>
                                </div>
                            </div>
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> Cancel
                </button>
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-save"></i> Update Status
                </button>
                <?= $this->Form->end() ?>
            </div>
        </div>
    </div>
</div>

<?php $this->append('script'); ?>
<script>
$(document).ready(function() {
    // Add smooth animations
    $('.info-card').each(function(index) {
        $(this).delay(index * 100).fadeIn(500);
    });

    // Compact status card animations
    $('.status-card').on('mouseenter', function() {
        $(this).find('.status-icon').addClass('animate__animated animate__pulse');
    }).on('mouseleave', function() {
        $(this).find('.status-icon').removeClass('animate__animated animate__pulse');
    });

    // Modal form submission
    $('.status-update-form').on('submit', function(e) {
        e.preventDefault();

        var form = $(this);
        var formData = form.serialize();
        var url = form.attr('action');
        var isBookingStatus = form.attr('id') === 'bookingStatusForm';
        var statusType = isBookingStatus ? 'booking status' : 'payment status';
        var selectedValue = form.find('input[type="radio"]:checked').val();
        var selectedLabel = form.find('input[type="radio"]:checked').next('label').find('strong').text();
        var modalId = isBookingStatus ? '#bookingStatusModal' : '#paymentStatusModal';

        // Close modal first
        $(modalId).modal('hide');

        // Show confirmation
        Swal.fire({
            title: 'Confirm Status Update',
            html: `
                <div style="text-align: center;">
                    <div style="color: #667eea; font-size: 48px;">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <p><strong>Update ${statusType} to:</strong></p>
                    <p style="color: #667eea; font-size: 18px; font-weight: 600;">${selectedLabel}</p>
                    <hr >
                    <p style="color: #6c757d; font-size: 18px;">Booking ID: #<?= $booking->id ?></p>
                </div>
            `,
            showCancelButton: true,
            confirmButtonColor: '#667eea',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-check"></i> Confirm',
            cancelButtonText: '<i class="fas fa-times"></i> Cancel',
            customClass: {
                popup: 'professional-popup'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // Show loading
                Swal.fire({
                    title: 'Updating Status',
                    html: `
                        <div style="text-align: center; padding: 30px;">
                            <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p style="margin-top: 20px; color: #6c757d;">Please wait...</p>
                        </div>
                    `,
                    allowOutsideClick: false,
                    showConfirmButton: false
                });

                // Submit via AJAX
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: formData,
                    headers: {
                        'X-CSRF-Token': $('meta[name="csrfToken"]').attr('content')
                    },
                    success: function(response) {
                        Swal.fire({
                            title: 'Success!',
                            html: `
                                <div style="text-align: center; padding: 20px;">
                                    <div style="color: #28a745; font-size: 48px; margin-bottom: 20px;">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <p>Status updated successfully!</p>
                                </div>
                            `,
                            confirmButtonColor: '#28a745',
                            confirmButtonText: 'OK',
                            timer: 2000,
                            timerProgressBar: true
                        }).then(() => {
                            location.reload();
                        });
                    },
                    error: function(xhr, status, error) {
                        Swal.fire({
                            title: 'Error!',
                            html: `
                                <div style="text-align: center; padding: 20px;">
                                    <div style="color: #dc3545; font-size: 48px; margin-bottom: 20px;">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <p>Failed to update status</p>
                                    <p style="color: #6c757d; font-size: 14px;">Please try again</p>
                                </div>
                            `,
                            confirmButtonColor: '#dc3545',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    });

    // Animate compact status panel on load
    $('.compact-status-panel').hide().fadeIn(600);

    // Add hover effects to status buttons
    $('.status-btn-group .btn').on('mouseenter', function() {
        $(this).addClass('shadow-sm');
    }).on('mouseleave', function() {
        $(this).removeClass('shadow-sm');
    });

    // Add click animation to update buttons
    $('.update-btn').on('click', function() {
        $(this).addClass('animate__animated animate__pulse');
        setTimeout(() => {
            $(this).removeClass('animate__animated animate__pulse');
        }, 1000);
    });

    // Add tooltips to status icons
    $('[data-bs-toggle="tooltip"]').tooltip();

    // Animate batch and pricing info cards on scroll
    $(window).on('scroll', function() {
        $('.batch-info, .pricing-info').each(function() {
            var elementTop = $(this).offset().top;
            var elementBottom = elementTop + $(this).outerHeight();
            var viewportTop = $(window).scrollTop();
            var viewportBottom = viewportTop + $(window).height();

            if (elementBottom > viewportTop && elementTop < viewportBottom) {
                $(this).addClass('animate__animated animate__fadeInUp');
            }
        });
    });
});
</script>
<?php $this->end(); ?>
