<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Partner $partner
 */
?>
<?php $this->append('style'); ?>
<!DOCTYPE html>
<html lang="en">
<?php $this->end(); ?>

<div class="main-content bg-container">
    <div class="row">
        <div class="col-12 col-xl-12">
            <div class="card card-primary">
                <div class="d-flex justify-content-between align-items-center">
                    <ul class="breadcrumb breadcrumb-style m-0">
                        <li class="breadcrumb-item">Partners</li>
                        <li class="breadcrumb-item">
                            <a href="<?= $this->Url->build(['controller' => 'Partners', 'action' => 'index']) ?>">Partners</a>
                        </li>
                        <li class="breadcrumb-item">View Partner</li>
                    </ul>
                    <a href="javascript:void(0);" class="d-flex align-items-center partner-back-button  breadcrumb m-0" id="back-button-mo" onclick="history.back();">
                        <span class="rotate me-2">⤣</span>
                        <small class="fw-bold" data-translate="back"><?= __("BACK") ?></small>
                    </a>
                </div>

                <div class="card-header">
                    <h4>View Partner</h4>
                </div>
                <div class="card-body">
                     <ul class="nav nav-tabs" id="partnerTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="basic-tab" data-bs-toggle="tab" href="#basic" role="tab">Basic Information</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="details-tab" data-bs-toggle="tab" href="#details" role="tab">Details </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="details-tab" data-bs-toggle="tab" href="#addresses_and_more" role="tab">Addresses & More </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="media-tab" data-bs-toggle="tab" href="#media" role="tab">Media</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="gst-tab" data-bs-toggle="tab" href="#gst" role="tab">GST</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="seo-tab" data-bs-toggle="tab" href="#seo" role="tab">SEO</a>
                        </li>
                        
                    </ul>

                    <div class="tab-content p-3 border border-top-0" id="partnerTabContent">
                        <!-- Basic Tab -->
                        <div class="tab-pane fade show active" id="basic" role="tabpanel">

                             <!-- Center/Teacher business name -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("Center/Teacher business name") ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= h($partner->name) ?>
                                </div>
                            </div>

                            <!-- Slug -->
                                <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label">Slug</label>
                                <div class="col-sm-9 main-field">
                                    <?= h($partner->slug) ?>
                                </div>
                            </div>

                            <!-- Type -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __('Type') ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= h($partner->type ?? '-') ?>
                                </div>
                            </div>

                            <!-- Partner Type -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("Partner Type") ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= h($partner->partner_type->name ?? '-') ?>
                                </div>
                            </div>
                            
                            <!-- Short Description -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("Short Description") ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= h($partner->short_description ?? '-') ?>
                                </div>
                            </div>


                           

                            <!-- First Name -->
                            <!-- <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("First Name") ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= !empty($partner->user) ? h($partner->user->first_name) : '-' ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("Last Name") ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= !empty($partner->user) ? h($partner->user->last_name) : '-' ?>
                                </div>
                            </div> -->
                            
                            <!-- Flags -->
                            <?php
                            $flags = [
                                // 'is_open' => 'Is Open',
                                'is_featured' => 'Is Featured',
                                'is_certified' => 'Is Certified',
                                'is_verified' => 'Is Verified'
                            ];
                            foreach ($flags as $field => $label) :
                            ?>
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __($label) ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= $partner->$field ? __('Yes') : __('No') ?>
                                </div>
                            </div>
                            <?php endforeach; ?>

                          <div class="form-group row mt-3 op">
                                <label class="col-sm-2 col-form-label"><?= __('Operating Months') ?></label>
                                <div class="col-sm-10">
                                    <?= !empty($operatingMonths) ? h(implode(', ', $operatingMonths)) : '-' ?>
                                </div>
                            </div>

                            <div class="form-group row mt-3 op">
                                <label class="col-sm-2 col-form-label"><?= __('Course Type') ?></label>
                                <div class="col-sm-10">
                                    <?php
                                    if (!empty($partner->course_types)) {
                                        $names = collection($partner->course_types)->extract('name')->toList();
                                        echo h(implode(', ', $names));
                                    } else {
                                        echo '-';
                                    }
                                    ?>
                                </div>
                            </div>


                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __('Yoga Styles') ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= !empty($partner->yoga_styles) ? h(implode(', ', collection($partner->yoga_styles)->extract('name')->toArray())) : '-' ?>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __('Special Needs') ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= !empty($partner->special_needs) ? h(implode(', ', collection($partner->special_needs)->extract('name')->toArray())) : '-' ?>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __('Techniques') ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= $techNames ? h(implode(', ', $techNames)) : '-' ?>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __('Level') ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= $levelNames ? h(implode(', ', $levelNames)) : '-' ?>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __('Language') ?></label>
                                <div class="col-sm-9 main-field">
                                    <?php
                                    if (!empty($partner->language)) {
                                        echo h(implode(', ', array_map('trim', explode(',', $partner->language))));
                                    } else {
                                        echo '-';
                                    }
                                    ?>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label">Modality</label>
                                <div class="col-sm-9 main-field">
                                    <?= $modeNames ? implode(', ', $modeNames) : '' ?>
                                </div>
                            </div>

                            <!-- Description -->
                            <!-- <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("Description") ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= h($partner->description ?? '-') ?>
                                </div>
                            </div> -->

                             <!-- Logo -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("Logo") ?></label>
                                <div class="col-sm-9 main-field">
                                    <?php if (!empty($partner->logo)) : ?>
                                        <img src="<?= $this->Url->build('/uploads/partners/logo/' . $partner->logo) ?>" alt="Logo" width="100">
                                    <?php else : ?>
                                        <?= __('-') ?>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Status -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("Status") ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= $partner->status === 'A' ? __('Active') : __('Inactive') ?>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Details Tab -->
                        <div class="tab-pane fade" id="details" role="tabpanel">
                            <div class="form-group row">
                                    <label for="first_name" class="col-sm-2 col-form-label">Description 1 Label</label>
                                    <div class="col-sm-9 main-field">
                                        <?php echo !empty($partner->desc1_label) ? ($partner->desc1_label) : '-'; ?>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="first_name" class="col-sm-2 col-form-label">Description 1 Text</label>
                                    <div class="col-sm-9 main-field">
                                        <?php echo !empty($partner->desc1_text) ? ($partner->desc1_text) : '-'; ?>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="first_name" class="col-sm-2 col-form-label">Description 2 Label</label>
                                    <div class="col-sm-9 main-field">
                                        <?php echo !empty($partner->desc2_label) ? ($partner->desc2_label) : '-'; ?>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="first_name" class="col-sm-2 col-form-label">Description 2 Text</label>
                                    <div class="col-sm-9 main-field">
                                        <?php echo !empty($partner->desc2_text) ? ($partner->desc2_text) : '-'; ?>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="first_name" class="col-sm-2 col-form-label">Description 3 Label</label>
                                    <div class="col-sm-9 main-field">
                                        <?php echo !empty($partner->desc3_label) ? ($partner->desc3_label) : '-'; ?>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="first_name" class="col-sm-2 col-form-label">Description 3 Text</label>
                                    <div class="col-sm-9 main-field">
                                        <?php echo !empty($partner->desc3_text) ? ($partner->desc3_text) : '-'; ?>
                                    </div>
                                </div>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("FAQ") ?></label>
                                <div class="col-sm-8 main-field">
                                    <?= nl2br(h($partner->faq ?? '-')) ?>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("Refund Policy") ?></label>
                                <div class="col-sm-8 main-field">
                                    <?= nl2br(h($partner->refund_policy ?? '-')) ?>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Tab -->
                        <div class="tab-pane fade" id="addresses_and_more" role="tabpanel">
                            <!-- Country -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("Country") ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= !empty($partner->country) ? h($partner->country->name) : '-' ?>
                                </div>
                            </div>

                            <!-- State -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("State") ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= !empty($partner->state) ? h($partner->state->name) : '-' ?>
                                </div>
                            </div>

                            <!-- City -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("City") ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= !empty($partner->city) ? h($partner->city->name) : '-' ?>
                                </div>
                            </div>

                            <!-- <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __('Is Main Address') ?></label>
                                <div class="col-sm-6 main-field">
                                    <?= isset($partner->is_main_address) ? ($partner->is_main_address ? __('Yes') : __('No')) : '-' ?>
                                </div>
                            </div> 

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __('Address 1') ?></label>
                                <div class="col-sm-6 main-field">
                                    <?= nl2br(h($partner->address1 ?? '-')) ?>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __('Address 2') ?></label>
                                <div class="col-sm-6 main-field">
                                    <?= nl2br(h($partner->address2 ?? '-')) ?>
                                </div>
                            </div> -->

                            <!-- Address -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("Address") ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= h($partner->address ?? '-') ?>
                                </div>
                            </div>

                            <!-- Zipcode -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("Zipcode") ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= h($partner->zipcode ?? '-') ?>
                                </div>
                            </div>

                            <!-- Latitude and Longitude -->
                            <!-- <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("Latitude") ?></label>
                                <div class="col-sm-2 main-field">
                                    <?= h($partner->latitude ?? '-') ?>
                                </div>
                                <label class="col-sm-2 col-form-label"><?= __("Longitude") ?></label>
                                <div class="col-sm-2 main-field">
                                    <?= h($partner->longitude ?? '-') ?>
                                </div>
                            </div> -->

                             <!-- <div class="form-group row">
                                <div class="col-md-4">
                                    <label class="col-form-label"><?= __('Search Your Area') ?></label>
                                    <div class="main-field">
                                        <?= h($partner->city ?? '-') ?>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label class="col-form-label"><?= __('Latitude') ?></label>
                                    <div class="main-field">
                                        <?= isset($partner->latitude) ? h($partner->latitude) : '-' ?>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label class="col-form-label"><?= __('Longitude') ?></label>
                                    <div class="main-field">
                                        <?= isset($partner->longitude) ? h($partner->longitude) : '-' ?>
                                    </div>
                                </div>
                            </div> -->

                            <!-- Phone -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __('Phone') ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= !empty($partner) ? '+' . h($partner->country_code) . ' ' . h($partner->phone) : '-' ?>
                                </div>
                            </div>

                            <!-- Email -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("Email") ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= !empty($partner) ? h($partner->email) : '-' ?>
                                </div>
                            </div>

                            <!-- Website -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("Website") ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= h($partner->website ?? '-') ?>
                                </div>
                            </div>

                            <!-- Social Links -->
                            <?php
                            $social = [
                                'whatsapp' => 'WhatsApp',
                                'facebook_url' => 'Facebook URL',
                                'instagram_url' => 'Instagram URL',
                                'youtube_url' => 'YouTube URL',
                                'x_url' => 'X URL',
                                'telegram_url' => 'Telegram URL'
                            ];
                            foreach ($social as $field => $label) :
                            ?>
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __($label) ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= h($partner->$field ?? '-') ?>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>

                        <!-- Media Tab -->
                        <div class="tab-pane fade" id="media" role="tabpanel" aria-labelledby="media-tab">
                            <!-- Partner Media -->
                            <div class="form-group row">
                                <label for="service_image" class="col-sm-2 col-form-label"><?= __("Media") ?></label>
                                <div class="col-sm-5 main-field">
                                    <div id="previeContainer">
                                        <ul id="imagePreviewContainer">
                                            <?php if (!empty($partner->partner_galleries)) : ?>
                                                <?php foreach ($partner->partner_galleries as $index => $gallery) :
                                                    $mediaPath = $gallery->media;
                                                    $mediaUrl = $media->displayImage($mediaPath); // This gives full path from MediaComponent
                                                    $extension = strtolower(pathinfo($mediaPath, PATHINFO_EXTENSION));
                                                    $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp']);
                                                    $isVideo = in_array($extension, ['mp4', 'webm', 'ogg', 'mov', 'mkv', 'avi']);
                                                ?>
                                                    <li class="image-thumbnail">
                                                        <?php if ($isImage): ?>
                                                            <img src="/<?= h($mediaUrl) ?>" alt="<?= __("Media Image") ?>" style="max-width: 100px; max-height: 100px;" />
                                                        <?php elseif ($isVideo): ?>
                                                            <video class="preview-video" width="120" height="100" controls>
                                                                <source src="/<?= h($mediaUrl) ?>" type="video/<?= h($extension) ?>">
                                                                <?= __("Your browser does not support the video tag.") ?>
                                                            </video>
                                                        <?php else: ?>
                                                            <span class="text-muted"><?= __("Unsupported media type") ?></span>
                                                        <?php endif; ?>
                                                        <!-- <span class="image-name" title="<?= h(basename($mediaPath)) ?>">
                                                            <?= h(basename($mediaPath)) ?>
                                                        </span> -->
                                                    </li>
                                                <?php endforeach; ?>


                                            <?php else : ?>
                                                <li><?= __("No Media Available") ?></li>
                                            <?php endif; ?>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- <div class="form-group row align-items-start">
                                <label class="col-sm-2 col-form-label"><?= __('Video Link') ?></label>
                                <div class="col-sm-8">
                                    <?php
                                    if (!empty($videoUrls)) {
                                        foreach ($videoUrls as $i => $link) {
                                            echo '<div class="mb-2">' . h(trim($link)) . '</div>';
                                        }
                                    } else {
                                        echo '-';
                                    }
                                    ?>
                                </div>
                            </div> -->

                            <!-- video link 1 -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("Video Link 1") ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= !empty($partner) ? h($partner->video_link_1) : '-' ?>
                                </div>
                            </div>
                            <!-- video link 2 -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("Video Link 2") ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= !empty($partner) ? h($partner->video_link_2) : '-' ?>
                                </div>
                            </div>
                            <!-- video link 3 -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("Video Link 3") ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= !empty($partner) ? h($partner->video_link_3) : '-' ?>
                                </div>
                            </div>
                        </div>
                       
                        <!-- GST Tab -->
                        <div class="tab-pane fade" id="gst" role="tabpanel" aria-labelledby="gst-tab">
                            <!-- Domestic Tax -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __('Is Tax Applicable for Domestic Bookings') ?></label>
                                <div class="col-sm-6 main-field">
                                    <?= !empty($partner->is_tax_domestic) ? __('Yes') : __('No') ?>
                                </div>
                            </div>
                            <?php if (!empty($partner->is_tax_domestic)) : ?>
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __('Domestic Tax Percentage') ?></label>
                                <div class="col-sm-6 main-field">
                                    <?= h($partner->domestic_tax_percentage ?: '-') ?>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- International Tax -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __('Is Tax Applicable for International Customers') ?></label>
                                <div class="col-sm-6 main-field">
                                    <?= !empty($partner->is_tax_international) ? __('Yes') : __('No') ?>
                                </div>
                            </div>
                            <?php if (!empty($partner->is_tax_international)) : ?>
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __('International Tax Percentage') ?></label>
                                <div class="col-sm-6 main-field">
                                    <?= h($partner->international_tax_percentage ?: '-') ?>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- Tax Number -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __('Tax Number') ?></label>
                                <div class="col-sm-6 main-field">
                                    <?= !empty($partner->tax_number) ? h($partner->tax_number) : '-' ?>
                                </div>
                            </div>

                        </div>

                         <!-- SEO Tab -->
                        <div class="tab-pane fade" id="seo" role="tabpanel" aria-labelledby="seo-tab">
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("Old URL") ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= h($partner->old_url ?? '-') ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __('Meta Title') ?></label>
                                <div class="col-sm-6 main-field">
                                    <?= h($partner->meta_title ?? '-') ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __('Meta Description') ?></label>
                                <div class="col-sm-6 main-field">
                                    <?= nl2br(h($partner->meta_description ?? '-')) ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __('Meta Keywords') ?></label>
                                <div class="col-sm-6 main-field">
                                    <?= nl2br(h($partner->meta_keywords ?? '-')) ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __('Meta Robots') ?></label>
                                <div class="col-sm-6 main-field">
                                    <?= !empty($partner->meta_robots) ? h($partner->meta_robots) : '-' ?>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


