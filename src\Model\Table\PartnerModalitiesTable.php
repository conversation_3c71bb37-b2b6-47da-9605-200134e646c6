<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * PartnerModalities Model
 *
 * @property \App\Model\Table\PartnersTable&\Cake\ORM\Association\BelongsTo $Partners
 * @property \App\Model\Table\ModalitiesTable&\Cake\ORM\Association\BelongsTo $Modalities
 *
 * @method \App\Model\Entity\PartnerModality newEmptyEntity()
 * @method \App\Model\Entity\PartnerModality newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\PartnerModality> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\PartnerModality get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\PartnerModality findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\PartnerModality patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\PartnerModality> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\PartnerModality|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\PartnerModality saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\PartnerModality>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\PartnerModality>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\PartnerModality>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\PartnerModality> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\PartnerModality>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\PartnerModality>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\PartnerModality>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\PartnerModality> deleteManyOrFail(iterable $entities, array $options = [])
 */
class PartnerModalitiesTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('partner_modalities');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->belongsTo('Partners', [
            'foreignKey' => 'partner_id',
            'joinType' => 'INNER',
        ]);
        $this->belongsTo('Modalities', [
            'foreignKey' => 'modality_id',
            'joinType' => 'INNER',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('partner_id')
            ->notEmptyString('partner_id');

        $validator
            ->integer('modality_id')
            ->notEmptyString('modality_id');

        $validator
            ->dateTime('created_at')
            ->allowEmptyDateTime('created_at');

        $validator
            ->dateTime('modified_at')
            ->allowEmptyDateTime('modified_at');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['partner_id'], 'Partners'), ['errorField' => 'partner_id']);
        $rules->add($rules->existsIn(['modality_id'], 'Modalities'), ['errorField' => 'modality_id']);

        return $rules;
    }
}
