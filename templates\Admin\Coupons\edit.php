<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Coupon $coupon
 */
?>
<?php $this->append('style'); ?>
<style>
/* Remove validation styling */
.form-control:valid,
.form-control:invalid,
.form-select:valid,
.form-select:invalid {
    border-color: #ced4da !important;
    background-image: none !important;
}

.was-validated .form-control:valid,
.was-validated .form-control:invalid,
.was-validated .form-select:valid,
.was-validated .form-select:invalid {
    border-color: #ced4da !important;
    background-image: none !important;
}

.valid-feedback,
.invalid-feedback {
    display: none !important;
}

.nav-tabs .nav-link.active {
    background: #fff;
    border-bottom: 2px solid #007bff;
    color: #007bff;
}
.tab-content {
    padding-top: 1.5rem;
}
</style>
<?php $this->end(); ?>

<div class="main-content bg-container">
    <div class="row">
        <div class="col-12 col-xl-12">
            <div class="card card-primary">
                <div class="d-flex justify-content-between align-items-center">
                    <ul class="breadcrumb breadcrumb-style m-0">
                        <li class="breadcrumb-item">Marketing</li>
                        <li class="breadcrumb-item">
                            <a href="<?= $this->Url->build(['action' => 'index']) ?>">Coupons</a>
                        </li>
                        <li class="breadcrumb-item">Edit Coupon</li>
                    </ul>

                    <a href="javascript:void(0);" class="d-flex align-items-center category-back-button breadcrumb m-0" id="back-button-mo" onclick="goBackToCoupons();">
                        <span class="rotate me-2">⤣</span>
                        <small class="fw-bold"><?= __("BACK") ?></small>
                    </a>
                </div>

                <div class="card-header">
                    <h4>Edit Coupon: <?= h($coupon->code) ?></h4>
                </div>
                <div class="card-body">
                    <?= $this->Form->create($coupon, ['id' => 'edit-coupon-form']) ?>

                    <?php if (!empty($paginationParams)): ?>
                        <?php foreach ($paginationParams as $key => $value): ?>
                            <?= $this->Form->hidden($key, ['value' => $value]) ?>
                        <?php endforeach; ?>
                    <?php endif; ?>

                    <ul class="nav nav-tabs" id="couponTab" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="basic-tab" data-bs-toggle="tab" href="#basic" role="tab" aria-controls="basic" aria-selected="true">
                                Basic Info
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="discount-tab" data-bs-toggle="tab" href="#discount" role="tab" aria-controls="discount" aria-selected="false">
                                Discount Settings
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="limits-tab" data-bs-toggle="tab" href="#limits" role="tab" aria-controls="limits" aria-selected="false">
                                Usage & Validity
                            </a>
                        </li>
                    </ul>
                    <div class="tab-content p-3 border border-top-0" id="couponTabContent">
                        <!-- Basic Information Tab -->
                        <div class="tab-pane fade show active" id="basic" role="tabpanel" aria-labelledby="basic-tab">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <?= $this->Form->control('code', [
                                                'label' => '<span class="form-label fw-bold text-dark">Coupon Code *</span>',
                                                'class' => 'form-control',
                                                'placeholder' => 'e.g., SAVE20, WELCOME10',
                                                'required' => true,
                                                'maxlength' => 50,
                                                'escape' => false
                                            ]) ?>
                                        </div>
                                        <div class="col-12">
                                            <?= $this->Form->control('title', [
                                                'label' => '<span class="form-label fw-bold text-dark">Title</span>',
                                                'class' => 'form-control',
                                                'placeholder' => 'e.g., Welcome Discount, Summer Sale',
                                                'maxlength' => 100,
                                                'escape' => false
                                            ]) ?>
                                        </div>
                                       
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <?= $this->Form->control('description', [
                                                'label' => '<span class="form-label fw-bold text-dark">Description</span>',
                                                'type' => 'textarea',
                                                'class' => 'form-control',
                                                'rows' => 6,
                                                'placeholder' => 'Describe the coupon and its terms...',
                                                'escape' => false
                                            ]) ?>
                                        </div>
                                    </div>
                                     <div class="col-12">
                                            <?= $this->Form->control('status', [
                                                'label' => '<span class="form-label fw-bold text-dark">Status *</span>',
                                                'type' => 'select',
                                                'options' => ['Active' => 'Active', 'Inactive' => 'Inactive'],
                                                'class' => 'form-select',
                                                'escape' => false
                                            ]) ?>
                                        </div>
                                </div>
                            </div>
                        </div>

                        <!-- Discount Settings Tab -->
                        <div class="tab-pane fade" id="discount" role="tabpanel" aria-labelledby="discount-tab">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <?= $this->Form->control('discount_type', [
                                                'label' => '<span class="form-label fw-bold text-dark">Discount Type *</span>',
                                                'type' => 'select',
                                                'options' => [
                                                    'percentage' => 'Percentage (%)',
                                                    'fixed' => 'Fixed Amount'
                                                ],
                                                'class' => 'form-select',
                                                'id' => 'discount-type',
                                                'required' => true,
                                                'escape' => false
                                            ]) ?>
                                        </div>
                                        <div class="col-12">
                                            <div class="row">
                                                <div class="col-12">
                                                    <?= $this->Form->control('discount_value', [
                                                        'label' => '<span class="form-label fw-bold text-dark">Discount Value *</span>',
                                                        'type' => 'number',
                                                        'step' => '0.01',
                                                        'min' => '0',
                                                        'class' => 'form-control',
                                                        'placeholder' => '0.00',
                                                        'required' => true,
                                                        'escape' => false
                                                    ]) ?>
                                                </div>
                                                <!-- <div class="col-4" id="currency-field">
                                                    <?= $this->Form->control('currency_code', [
                                                        'label' => '<span class="form-label fw-bold text-dark">Currency</span>',
                                                        'class' => 'form-control',
                                                        'placeholder' => 'USD',
                                                        'maxlength' => 10,
                                                        'escape' => false
                                                    ]) ?>
                                                </div> -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <?= $this->Form->control('min_cart_value', [
                                                'label' => '<span class="form-label fw-bold text-dark">Minimum booking Value</span>',
                                                'type' => 'number',
                                                'step' => '0.01',
                                                'min' => '0',
                                                'class' => 'form-control',
                                                'placeholder' => '0.00',
                                                'escape' => false
                                            ]) ?>
                                        </div>
                                        <!-- <div class="col-12" id="max-discount-field">
                                            <?= $this->Form->control('max_discount_value', [
                                                'label' => '<span class="form-label fw-bold text-dark">Maximum Discount Amount</span>',
                                                'type' => 'number',
                                                'step' => '0.01',
                                                'min' => '0',
                                                'class' => 'form-control',
                                                'placeholder' => '0.00',
                                                'escape' => false
                                            ]) ?>
                                        </div> -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Usage & Validity Tab -->
                        <div class="tab-pane fade" id="limits" role="tabpanel" aria-labelledby="limits-tab">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <h6 class="fw-bold text-dark mb-3">Usage Limits</h6>
                                        </div>
                                        <div class="col-12">
                                            <?= $this->Form->control('usage_limit', [
                                                'label' => '<span class="form-label fw-bold text-dark">Total Usage Limit</span>',
                                                'type' => 'number',
                                                'min' => '1',
                                                'class' => 'form-control',
                                                'placeholder' => 'Leave empty for unlimited',
                                                'escape' => false
                                            ]) ?>
                                            <small class="form-text text-muted">Maximum number of times this coupon can be used in total</small>
                                        </div>
                                        <div class="col-12">
                                            <?= $this->Form->control('per_user_limit', [
                                                'label' => '<span class="form-label fw-bold text-dark">Per User Limit</span>',
                                                'type' => 'number',
                                                'min' => '1',
                                                'class' => 'form-control',
                                                'placeholder' => 'Leave empty for unlimited',
                                                'escape' => false
                                            ]) ?>
                                            <small class="form-text text-muted">Maximum number of times a single user can use this coupon</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <h6 class="fw-bold text-dark mb-3">Validity Period</h6>
                                        </div>
                                        <div class="col-12">
                                            <?= $this->Form->control('start_date', [
                                                'label' => '<span class="form-label fw-bold text-dark">Start Date & Time</span>',
                                                'type' => 'datetime-local',
                                                'class' => 'form-control',
                                                'value' => $coupon->start_date ? $coupon->start_date->format('Y-m-d\TH:i') : '',
                                                'escape' => false
                                            ]) ?>
                                            <small class="form-text text-muted">Leave empty if the coupon should be active immediately</small>
                                        </div>
                                        <div class="col-12">
                                            <?= $this->Form->control('end_date', [
                                                'label' => '<span class="form-label fw-bold text-dark">End Date & Time</span>',
                                                'type' => 'datetime-local',
                                                'class' => 'form-control',
                                                'value' => $coupon->end_date ? $coupon->end_date->format('Y-m-d\TH:i') : '',
                                                'escape' => false
                                            ]) ?>
                                            <small class="form-text text-muted">Leave empty if the coupon should never expire</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between mt-4">
                        <?= $this->Html->link(__('Cancel'), ['action' => 'view', $coupon->id], ['class' => 'btn btn-secondary']) ?>
                        <div>
                            <?= $this->Form->button(__('Save Changes'), [
                                'type' => 'submit',
                                'class' => 'btn btn-primary'
                            ]) ?>
                        </div>
                    </div>

                    <?= $this->Form->end() ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $this->append('script'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const discountTypeSelect = document.getElementById('discount-type');
    const currencyField = document.getElementById('currency-field');
    const maxDiscountField = document.getElementById('max-discount-field');

    // function toggleFields() {
    //     const isPercentage = discountTypeSelect.value === 'percentage';

    //     // Show/hide currency field based on discount type
    //     if (isPercentage) {
    //         currencyField.style.display = 'none';
    //         maxDiscountField.style.display = 'block';
    //     } else {
    //         currencyField.style.display = 'block';
    //         maxDiscountField.style.display = 'none';
    //     }

    //     // Update placeholder and label for discount value
    //     const discountValueInput = document.querySelector('input[name="discount_value"]');

    //     if (isPercentage) {
    //         discountValueInput.placeholder = 'e.g., 20 (for 20%)';
    //         discountValueInput.max = '100';
    //     } else {
    //         discountValueInput.placeholder = 'e.g., 10.00';
    //         discountValueInput.removeAttribute('max');
    //     }
    // }

    // Initialize on page load
    //toggleFields();

    // Update when discount type changes
    // discountTypeSelect.addEventListener('change', toggleFields);
});

function goBackToCoupons() {
    // Try to get the referrer URL first
    if (document.referrer && document.referrer.includes('/admin/coupons') && !document.referrer.includes('/edit') && !document.referrer.includes('/view')) {
        window.location.href = document.referrer;
    } else {
        // Fallback to coupons index
        window.location.href = '/admin/coupons';
    }
}
</script>
<?php $this->end(); ?>
