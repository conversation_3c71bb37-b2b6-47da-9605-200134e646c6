<?php
declare(strict_types=1);

namespace App\Test\TestCase\Model\Table;

use App\Model\Table\PartnerModalitiesTable;
use Cake\TestSuite\TestCase;

/**
 * App\Model\Table\PartnerModalitiesTable Test Case
 */
class PartnerModalitiesTableTest extends TestCase
{
    /**
     * Test subject
     *
     * @var \App\Model\Table\PartnerModalitiesTable
     */
    protected $PartnerModalities;

    /**
     * Fixtures
     *
     * @var list<string>
     */
    protected array $fixtures = [
        'app.PartnerModalities',
        'app.Partners',
        'app.Modalities',
    ];

    /**
     * setUp method
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();
        $config = $this->getTableLocator()->exists('PartnerModalities') ? [] : ['className' => PartnerModalitiesTable::class];
        $this->PartnerModalities = $this->getTableLocator()->get('PartnerModalities', $config);
    }

    /**
     * tearDown method
     *
     * @return void
     */
    protected function tearDown(): void
    {
        unset($this->PartnerModalities);

        parent::tearDown();
    }

    /**
     * Test validationDefault method
     *
     * @return void
     * @uses \App\Model\Table\PartnerModalitiesTable::validationDefault()
     */
    public function testValidationDefault(): void
    {
        $this->markTestIncomplete('Not implemented yet.');
    }

    /**
     * Test buildRules method
     *
     * @return void
     * @uses \App\Model\Table\PartnerModalitiesTable::buildRules()
     */
    public function testBuildRules(): void
    {
        $this->markTestIncomplete('Not implemented yet.');
    }
}
