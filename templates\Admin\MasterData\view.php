<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\User $user
 */

 use Cake\I18n\FrozenTime;
?>
<?php $this->append('style'); ?>
<!DOCTYPE html>
<html lang="en">
<?php $this->end(); ?>

<div class="main-content bg-container">
    <div class="row">
        <div class="col-12 col-xl-12">
            <div class="card card-primary">
                <div class="d-flex justify-content-between align-items-center">
                    <ul class="breadcrumb breadcrumb-style m-0">
                        <li class="breadcrumb-item">
                            <a href="<?= $this->Url->build(['controller' => 'MasterData', 'action' => 'index']) ?>">Master Data</a>
                        </li>
                        <li class="breadcrumb-item">View Master Data</li>
                    </ul>

                    <a href="javascript:void(0);" class="d-flex align-items-center category-back-button  breadcrumb m-0" id="back-button-mo" onclick="history.back();">
                        <span class="rotate me-2">⤣</span>
                        <small class="fw-bold" data-translate="back"><?= __("BACK") ?></small>
                    </a>
                </div>

                <div class="card-header">
                    <h4>View Master Data</h4>
                </div>
                <div class="card-body">
                    <div id="view-user-form">
                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label"><?= __("Id") ?></label>
                            <div class="col-sm-6">
                                <?= h(trim($master->id)) ?>
                            </div>
                        </div>
                        <!-- Type -->
                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label"><?= __("Type") ?></label>
                            <div class="col-sm-6">
                                <?= h(trim($master->type)) ?>
                            </div>
                        </div>

                        <!-- Title -->
                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label"><?= __("Title") ?></label>
                            <div class="col-sm-6">
                                <?= h(trim($master->title)) ?>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label"><?= __("Description") ?></label>
                            <div class="col-sm-6">
                                <?= h($master->description) ?>
                            </div>
                        </div>

                        <!-- Status -->
                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label"><?= __("Status") ?></label>
                            <div class="col-sm-6 d-flex align-items-center">
                                <?php
                                    $statusMap = [
                                        'A' => ['label' => __('Active')],
                                        'I' => ['label' => __('Inactive')],
                                        'D' => ['label' => __('Deleted')]
                                    ];
                                    
                                    $status = $statusMap[$master->status] ?? ['label' => __('Unknown')];
                                ?>
                                <span>
                                    <?= h($status['label']) ?>
                                </span>
                            </div>
                        </div>

                    </div>
                    <?= $this->Form->end(); ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->append('script'); ?>
<?php $this->end(); ?>








