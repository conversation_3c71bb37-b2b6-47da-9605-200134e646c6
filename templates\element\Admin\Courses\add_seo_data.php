<div class="form-group row">
    <label class="col-sm-2 col-form-label">Old URL</label>
    <div class="col-sm-6 main-field">
            <?php echo $this->Form->control('old_url', [
            'type' => 'textarea',
            'class' => 'form-control',
            'id' => 'old_url',
            'placeholder' => __('Previous course slugs (comma-separated)'),
            'label' => false,
            'required' => false,
            'rows' => 2
        ]); ?>
        <small class="form-text text-muted">
            <strong>SEO Purpose:</strong> When course names/URLs change, old URLs are automatically saved here for SEO redirects.
            You can also manually add old slugs separated by commas.
        </small>
    </div>
</div>
<div class="form-group row">
    <label class="col-sm-2 col-form-label">Meta Title</label>
    <div class="col-sm-6 main-field">
            <?php echo $this->Form->control('meta_title', [
            'type' => 'text',
            'class' => 'form-control',
            'id' => 'meta_title',
            'placeholder' => __('Meta Title'),
            'label' => false,
            'required' => false
        ]); ?>
    </div>
</div>
<div class="form-group row">
    <label class="col-sm-2 col-form-label">Meta Description</label>
    <div class="col-sm-6 main-field">
            <?php echo $this->Form->control('meta_description', [
            'type' => 'textarea',
            'class' => 'form-control',
            'id' => 'meta_description',
            'placeholder' => __('Meta Description'),
            'label' => false,
            'required' => false
        ]); ?>
    </div>
</div>

<div class="form-group row">
    <label class="col-sm-2 col-form-label">Meta Keywords</label>
    <div class="col-sm-6 main-field">
        <!-- Hidden input to store the actual keywords value -->
        <?php echo $this->Form->control('meta_keywords', [
            'type' => 'hidden',
            'id' => 'meta_keywords',
            'label' => false
        ]); ?>

        <!-- Tag input container -->
        <div class="tag-input-container" id="keywords-tag-container">
            <div class="tag-input-wrapper">
                <div class="tags-display" id="keywords-tags-display"></div>
                <input type="text"
                       class="tag-input-field"
                       id="keywords-tag-input"
                       placeholder="Type keyword and press Enter..."
                       autocomplete="off">
            </div>
            <small class="form-text text-muted">Press Enter or comma to add keywords. Click on tags to remove them.</small>
        </div>
    </div>
</div>

<!-- <style>
.tag-input-container {
    position: relative;
}

.tag-input-wrapper {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    min-height: 38px;
    padding: 6px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background-color: #fff;
    cursor: text;
}

.tag-input-wrapper:focus-within {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.tags-display {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-right: 8px;
}

.keyword-tag {
    display: inline-flex;
    align-items: center;
    padding: 2px 8px;
    background-color: #007bff;
    color: white;
    border-radius: 12px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.keyword-tag:hover {
    background-color: #dc3545;
}

.keyword-tag .remove-tag {
    margin-left: 4px;
    cursor: pointer;
    font-weight: bold;
}

.tag-input-field {
    border: none;
    outline: none;
    flex: 1;
    min-width: 120px;
    padding: 4px 0;
    font-size: 14px;
}

.tag-input-field::placeholder {
    color: #6c757d;
}
</style> -->

<script>
document.addEventListener('DOMContentLoaded', function() {
    const keywordsTagInput = new TagInput('keywords-tag-input', 'meta_keywords');
});

class TagInput {
    constructor(inputId, hiddenFieldId) {
        this.input = document.getElementById(inputId);
        this.hiddenField = document.getElementById(hiddenFieldId);
        this.tagsDisplay = document.getElementById(inputId.replace('-input', 's-display'));
        this.container = document.getElementById(inputId.replace('-input', '-container'));
        this.wrapper = this.container.querySelector('.tag-input-wrapper');

        this.tags = [];

        this.init();
    }

    init() {
        // Load existing keywords if any
        if (this.hiddenField.value) {
            const existingKeywords = this.hiddenField.value.split(',').map(k => k.trim()).filter(k => k);
            this.tags = existingKeywords;
            this.renderTags();
        }

        // Event listeners
        this.input.addEventListener('keydown', (e) => this.handleKeyDown(e));
        this.input.addEventListener('blur', () => this.addCurrentTag());
        this.wrapper.addEventListener('click', () => this.input.focus());

        // Handle paste events
        this.input.addEventListener('paste', (e) => {
            setTimeout(() => this.handlePaste(), 10);
        });
    }

    handleKeyDown(e) {
        const value = this.input.value.trim();

        if (e.key === 'Enter' || e.key === ',') {
            e.preventDefault();
            this.addCurrentTag();
        } else if (e.key === 'Backspace' && !value && this.tags.length > 0) {
            this.removeTag(this.tags.length - 1);
        }
    }

    handlePaste() {
        const value = this.input.value;
        const keywords = value.split(/[,\n\r]+/).map(k => k.trim()).filter(k => k);

        if (keywords.length > 1) {
            this.input.value = '';
            keywords.forEach(keyword => this.addTag(keyword));
        }
    }

    addCurrentTag() {
        const value = this.input.value.trim();
        if (value) {
            this.addTag(value);
            this.input.value = '';
        }
    }

    addTag(keyword) {
        // Clean the keyword
        keyword = keyword.replace(/[,\n\r]/g, '').trim();

        if (keyword && !this.tags.includes(keyword)) {
            this.tags.push(keyword);
            this.renderTags();
            this.updateHiddenField();
        }
    }

    removeTag(index) {
        this.tags.splice(index, 1);
        this.renderTags();
        this.updateHiddenField();
    }

    renderTags() {
        this.tagsDisplay.innerHTML = '';
        this.tags.forEach((tag, index) => {
            const tagElement = document.createElement('span');
            tagElement.className = 'keyword-tag';
            tagElement.innerHTML = `${tag} <span class="remove-tag">&times;</span>`;
            tagElement.addEventListener('click', () => this.removeTag(index));
            this.tagsDisplay.appendChild(tagElement);
        });
    }

    updateHiddenField() {
        this.hiddenField.value = this.tags.join(', ');
    }
}
</script>

<div class="form-group row">
    <label class="col-sm-2 col-form-label">Meta Robots</label>
    <div class="col-sm-6 main-field">
          <?= $this->Form->control('meta_robots', [
                'type' => 'select',
                'options' => $meta_robot_options ?? [],
                'empty' => 'Select',
                'label' => false,
                'class' => 'form-control1 form-select',
                'id' => 'meta_robots'
            ]) ?>
    </div>
</div>