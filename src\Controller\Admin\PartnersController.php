<?php

declare(strict_types=1);

namespace App\Controller\Admin;

use Authentication\PasswordHasher\DefaultPasswordHasher;
use Cake\Core\Configure;
use Cake\ORM\TableRegistry;
use Cake\Routing\Router;
use Cake\Utility\Security;
use Cake\I18n\FrozenTime;
use Cake\Log\Log;
use Cake\Event\EventInterface;
use Cake\Utility\Text;
use Laminas\Diactoros\UploadedFile;

/**
 * Partners Controller
 *
 * @property \App\Model\Table\PartnersTable $Partners
 */
class PartnersController extends AppController
{
    protected $Countries;
    protected $States;
    protected $Cities;
    protected $Localities;
    protected $Users;
    protected $PartnerGalleries;
    protected $MasterData;
    protected $CourseTypes;
    protected $PartnerYogaStyles;
    protected $PartnerSpecialNeeds;
    protected $PartnerCourseTypes;
    protected $PartnerModalities;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->Countries = $this->fetchTable('Countries');
        $this->States = $this->fetchTable('States');
        $this->Cities = $this->fetchTable('Cities');
        $this->Localities = $this->fetchTable('Localities');
        $this->Users = $this->fetchTable('Users');
        $this->PartnerGalleries = $this->fetchTable('PartnerGalleries');
        $this->MasterData = $this->fetchTable('MasterData');
        $this->CourseTypes = $this->fetchTable('CourseTypes');
        $this->PartnerYogaStyles = $this->fetchTable('PartnerYogaStyles');
        $this->PartnerSpecialNeeds = $this->fetchTable('PartnerSpecialNeeds');
        $this->PartnerCourseTypes = $this->fetchTable('PartnerCourseTypes');
        $this->Modalities = $this->fetchTable('Modalities');
        $this->PartnerModalities = $this->fetchTable('PartnerModalities');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
    }

    // Index
    public function index()
    {
        $query = $this->Partners->find()
            ->contain([
                'Users',
                'Countries',
                'States',
                'Cities',
                'PartnerTypes'
            ])
            ->where(
                [
                  'Partners.status' => 'A'
                   // 'PartnerTypes.name !=' =>'Teachers'
                ])
            ->orderBy(['Partners.id' => 'DESC']);

        $partners = $query->toArray();
        $this->set(compact('partners'));
    }

    // View
    // public function view($id = null)
    // {
    //     $partner = $this->Partners->get($id, [
    //         'contain' => [
    //             'Users',
    //             'PartnerGalleries',
    //             'Countries',
    //             'States',
    //             'Cities',
    //             'PartnerTypes'
    //         ]
    //     ]);
    //     $media = $this->Media;
    //     $this->set(compact('partner', 'media'));
    // }

    // View
    public function view($id = null)
    {
        $partner = $this->Partners->get($id, [
            'contain' => [
                // 'Users',
                'PartnerGalleries',
                'Countries',
                'States',
                'Cities',
                'PartnerTypes',
                'YogaStyles',
                'SpecialNeeds',
                'CourseTypes',
                'Modalities'
            ]
        ]);

        // Media Helper
        $media = $this->Media;

        // Extract image gallery
        $mediaImages = [];
        if (!empty($partner->partner_galleries)) {
            $webroot = $this->request->getAttribute('webroot');
            foreach ($partner->partner_galleries as $gallery) {
                if ($gallery->media_type === 'image') {
                    $pathUrl = $this->Media->displayImage($gallery->media);
                    $imageUrl = $webroot . $pathUrl;
                    $mediaImages[] = [
                        'id' => $gallery->id,
                        'displayname' => basename($gallery->media),
                        'path' => $gallery->media,
                        'url' => $imageUrl,
                    ];
                }
            }
        }

        // // Extract video URLs
        // $videoUrls = [];
        // if (!empty($partner->partner_galleries)) {
        //     foreach ($partner->partner_galleries as $gallery) {
        //         if ($gallery->media_type === 'video') {
        //             $videoUrls[] = $gallery->url ?? '';
        //         }
        //     }
        // }

        $levelNames = [];
        if (!empty($partner->level)) {
            $levels = explode(',', $partner->level);
            $levelNames = $this->MasterData->getNamesList($levels);
        }

        $techNames = [];
        if (!empty($partner->techniques)) {
            $techniques = explode(',', $partner->techniques);
            $techNames = $this->MasterData->getNamesList($techniques);
        }
        
        // Extract associated yoga styles and special needs IDs
        $selectedYogaStyles = [];
        if (!empty($partner->yoga_styles)) {
            foreach ($partner->yoga_styles as $style) {
                $selectedYogaStyles[] = $style->id;
            }
        }
        
        $selectedSpecialNeeds = [];
        if (!empty($partner->special_needs)) {
            foreach ($partner->special_needs as $sn) {
                $selectedSpecialNeeds[] = $sn->id;
            }
        }
        $operatingMonths = !empty($partner->operating_months) ? explode(',', $partner->operating_months) : [];

        // Fetch tax percent master data for reference (if needed)
        $domesticTaxPercentList = $this->MasterData->find('list', [
            'keyField' => 'title',
            'valueField' => 'title',
        ])->where(['type' => 'domestic_tax_percentage', 'status' => 'A'])->toArray();

        $internationalTaxPercentList = $this->MasterData->find('list', [
            'keyField' => 'title',
            'valueField' => 'title',
        ])->where(['type' => 'international_tax_percentage', 'status' => 'A'])->toArray();
        
        // Fetch course types
        // $courseTypes = $this->CourseTypes->find('list')->where(['status' => 'A'])->orderAsc('name')->toArray();
        $operatingMonths = !empty($partner->operating_months) ? explode(',', $partner->operating_months) : [];

        $modeNames = [];
        if (!empty($partner->modalities)) {
            foreach ($partner->modalities as $modality) {
                $modeNames[] = $modality->name;
            }
        }

        $this->set(compact(
            'partner',
            'media',
            'mediaImages',
            // 'videoUrls',
            'techNames',
            'levelNames',
            'operatingMonths',
            'selectedYogaStyles',
            'selectedSpecialNeeds',
            // 'courseTypes',
            'domesticTaxPercentList',
            'internationalTaxPercentList',
            'modeNames'
        ));
    }

    // Add
    public function add()
    {
        $partner = $this->Partners->newEmptyEntity();

        $modalities = $this->Modalities->selectInputOptions();
        $partner->meta_robots = $partner->meta_robots ?? 'index, follow';
        $partnetypes = $this->Partners->PartnerTypes->find('list')
            ->where(['PartnerTypes.status' => 'A'])
            ->all();

        $countries = $this->Countries->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->where(['status' => 'A'])->toArray();

        $yogaStyles = $this->MasterData->findByType('yoga_style')->toArray();
        $specialNeeds = $this->MasterData->findByType('special_need')->toArray();
        $techniques = $this->MasterData->findByType('technique')->toArray();
        $yogaStyles['n/a']  = 'N/A';
        $specialNeeds['n/a']= 'N/A';   
        $techniques['n/a']  = 'N/A';
        $level = $this->MasterData->findByType('level');

        $domesticTaxPercent = $this->MasterData->findByType('domestic_tax_percentage');
        $internationalTaxPercent = $this->MasterData->findByType('international_tax_percentage');
        $languages = Configure::read('Constants.Language');

        
        $courseTypes = $this->CourseTypes->find('list')->where(['CourseTypes.status' => 'A'])->all();

        if ($this->request->is(['post'])) {
            $data = $this->request->getData();
            $connection = $this->Partners->getConnection();
            $connection->begin();
            $phoneNumber = str_replace(' ', '', trim($data['phone']));
            $country_code = trim(str_replace(' ', '', $data['country_code']));
            try {
                // $existingUser = $this->Partners->Users->find()
                //     ->where([
                //         'email' => $data['email'],
                //         'status !=' => 'D'
                //     ])
                //     ->first();

                // if ($existingUser) {
                //     $this->Flash->error(__('A user with this email already exists.'), [
                //         'key' => 'partner_error'
                //     ]);
                //     return $this->redirect(['action' => 'add']);
                // }

                // $plainPassword = Security::hash(Security::randomBytes(8), 'sha256', true);
                // $hashedPassword = (new DefaultPasswordHasher())->hash($plainPassword);

                // $userData = [
                //     'first_name' => $data['first_name'],
                //     'last_name' => $data['last_name'],
                //     'email' => $data['email'],
                //     'password' => $hashedPassword,
                //     'mobile' => $phoneNumber,
                //     'country_code' => $country_code ?? '91',
                //     'user_type' => 'Partner',
                //     'status' => 'A',
                //     'created_at' => date('Y-m-d H:i:s')
                // ];

                // $user = $this->Partners->Users->newEntity($userData, ['validate' => true]);
                // if ($user->getErrors()) {
                //     throw new \Exception('User validation failed: ' . json_encode($user->getErrors()));
                // }

                // if (!$this->Partners->Users->save($user)) {
                //     throw new \Exception('Failed to save user: ' . json_encode($user->getErrors()));
                // }
                
                // Prepare Partner Data
                // unset($data['first_name'], $data['last_name'], $data['password'], $data['phone'], $data['country_code']);
                // $data['user_id'] = $user->id;
                $data['slug'] = str_replace(' ', '-', $data['name']);

                // Convert level and techniques arrays into comma-separated strings
                $data['level'] = !empty($data['level']) && is_array($data['level']) ? implode(',', $data['level']) : null;
                // $data['techniques'] = !empty($data['techniques']) && is_array($data['techniques']) ? implode(',', $data['techniques']) : null;
                $data['domesticTaxPercent'] = !empty($data['domesticTaxPercent']) && is_array($data['domesticTaxPercent']) ? implode(',', $data['domesticTaxPercent']) : null;
                $data['internationalTaxPercent'] = !empty($data['internationalTaxPercent']) && is_array($data['internationalTaxPercent']) ? implode(',', $data['internationalTaxPercent']) : null;

                // For techniques, handle 'n/a' skip
                if (!empty($data['techniques']) && is_array($data['techniques'])) {
                    if (in_array('n/a', $data['techniques'])) {
                        // Remove 'n/a' from array if present
                        $data['techniques'] = array_filter($data['techniques'], function ($val) {
                            return $val !== 'n/a';
                        });
                    }
                    $data['techniques'] = !empty($data['techniques']) ? implode(',', $data['techniques']) : null;
                } else {
                    $data['techniques'] = null;
                }
                
                $data['language'] = !empty($data['language']) && is_array($data['language']) 
                    ? implode(',', $data['language']) 
                    : null;

                // Upload logo
                $logoPath = WWW_ROOT . 'uploads/partners/logo/';
                if (!file_exists($logoPath)) {
                    mkdir($logoPath, 0775, true);
                }

                if (!empty($data['logo']) && $data['logo'] instanceof UploadedFile && $data['logo']->getClientFilename()) {
                    $logoName = time() . '_' . $data['logo']->getClientFilename();
                    $data['logo']->moveTo($logoPath . $logoName);
                    $data['logo'] = $logoName;
                } else {
                    unset($data['logo']);
                }
                $data['phone'] = $phoneNumber;
                $data['country_code'] = $country_code;
                
                // Convert selected months array to comma-separated string
                $data['operating_months'] = !empty($data['operating_months']) && is_array($data['operating_months']) 
                    ? implode(',', $data['operating_months']) 
                    : null;
                    
                $partner = $this->Partners->patchEntity($partner, $data);
                if ($partner->hasErrors()) {
                    throw new \Exception('Partner validation failed: ' . json_encode($partner->getErrors()));
                }

                if (!$this->Partners->save($partner)) {
                    throw new \Exception('Failed to save partner');
                }

                $partnerId = $partner->id;

                // Update slug with partner ID
                $partner->slug = $partnerId.'-'.$partner->slug;
                $this->Partners->save($partner);

                // Modalities
                $modalities = !empty($data['modality']) ? $data['modality'] : [];
                if (!empty($modalities)) {
                    foreach ($modalities as $modalityId) {
                        $partnerModality = $this->PartnerModalities->newEmptyEntity();
                        $partnerModality->partner_id = $partnerId;
                        $partnerModality->modality_id = $modalityId;
                        $this->PartnerModalities->save($partnerModality);
                    }
                }

                // // Save Yoga Styles
                // $YogaStyleIds = !empty($data['yoga_style_id']) ? $data['yoga_style_id'] : [];

                // if (!empty($YogaStyleIds)) {
                //     foreach ($YogaStyleIds as $YogaStyleId) {
                //         $partnerYogaStyle = $this->PartnerYogaStyles->newEmptyEntity();
                //         $partnerYogaStyle->partner_id = $partnerId;
                //         $partnerYogaStyle->yoga_style_id = $YogaStyleId;
                //         $this->PartnerYogaStyles->save($partnerYogaStyle);
                //     }
                // }

                // // Save Special Needs
                // $SpecialNeedsIds = !empty($data['special_need_id']) ? $data['special_need_id'] : [];

                // if (!empty($SpecialNeedsIds)) {
                //     foreach ($SpecialNeedsIds as $SpecialNeedId) {
                //         $partnerSpecialNeed = $this->PartnerSpecialNeeds->newEmptyEntity();
                //         $partnerSpecialNeed->partner_id = $partnerId;
                //         $partnerSpecialNeed->special_need_id = $SpecialNeedId;
                //         $this->PartnerSpecialNeeds->save($partnerSpecialNeed);
                //     }
                // }

                
                // Save Yoga Styles, skipping 'n/a'
                $YogaStyleIds = !empty($data['yoga_style_id']) ? $data['yoga_style_id'] : [];
                if (!empty($YogaStyleIds)) {
                    foreach ($YogaStyleIds as $YogaStyleId) {
                        if ($YogaStyleId === 'n/a') {
                            continue; // skip 'n/a'
                        }
                        $partnerYogaStyle = $this->PartnerYogaStyles->newEmptyEntity();
                        $partnerYogaStyle->partner_id = $partnerId;
                        $partnerYogaStyle->yoga_style_id = $YogaStyleId;
                        $this->PartnerYogaStyles->save($partnerYogaStyle);
                    }
                }

                // Save Special Needs, skipping 'n/a'
                $SpecialNeedsIds = !empty($data['special_need_id']) ? $data['special_need_id'] : [];
                if (!empty($SpecialNeedsIds)) {
                    foreach ($SpecialNeedsIds as $SpecialNeedId) {
                        if ($SpecialNeedId === 'n/a') {
                            continue; // skip 'n/a'
                        }
                        $partnerSpecialNeed = $this->PartnerSpecialNeeds->newEmptyEntity();
                        $partnerSpecialNeed->partner_id = $partnerId;
                        $partnerSpecialNeed->special_need_id = $SpecialNeedId;
                        $this->PartnerSpecialNeeds->save($partnerSpecialNeed);
                    }
                }


                // Save Course Types
                $courseTypeIds = !empty($data['course_type_id']) ? $data['course_type_id'] : [];

                if (!empty($courseTypeIds)) {
                    foreach ($courseTypeIds as $courseTypeId) {
                        $partnerCourseType = $this->PartnerCourseTypes->newEmptyEntity();
                        $partnerCourseType->partner_id = $partnerId;
                        $partnerCourseType->course_type_id = $courseTypeId;
                        $this->PartnerCourseTypes->save($partnerCourseType);
                    }
                }

                // Handle new file uploads (both images and videos)
                $uploadedImages = !empty($this->request->getData('image')) ? $this->handleFileUploads('image') : [];
                if (!empty($uploadedImages)) {
                    foreach ($uploadedImages as $mediaData) {
                        $PartnerImage = $this->Partners->PartnerGalleries->newEmptyEntity();
                        $PartnerImage->partner_id = $partner->id;
                        $PartnerImage->media = $mediaData['path'];
                        $PartnerImage->media_type = $mediaData['type']; 
                        $PartnerImage->status = 'A';
                        $this->Partners->PartnerGalleries->save($PartnerImage);
                    }
                }

                // $videoUrls = $this->request->getData('video_url');
                // if (!empty($videoUrls)) {
                //     foreach ($videoUrls as $url) {
                //         $trimmedUrl = trim($url);
                //         if (!empty($trimmedUrl)) {
                //             $PartnerVideo = $this->Partners->PartnerGalleries->newEmptyEntity();
                //             $PartnerVideo->partner_id = $partner->id;
                //             $PartnerVideo->media = $trimmedUrl;
                //             $PartnerVideo->media_type = 'url'; // indicates it's a video URL
                //             $PartnerVideo->status = 'A';
                //             $this->Partners->PartnerGalleries->save($PartnerVideo);
                //         }
                //     }
                // }

                $connection->commit();
                $this->Flash->success(__('The partner has been saved.'), [
                    'key' => 'partner_success'
                ]);

                return $this->redirect(['action' => 'index']);

            } catch (\Exception $e) {
                $connection->rollback();
                Log::error('Partner creation failed: ' . $e->getMessage());
                Log::error('Stack trace: ' . $e->getTraceAsString());
                $this->Flash->error(__('Failed to create partner: ' . $e->getMessage()), [
                    'key' => 'partner_error'
                ]);
            }
        }

        $states = [];
        if (!empty($partner->country_id)) {
           $statesAll = $this->States->find()
                ->select(['id', 'name'])
                ->where([
                    'country_id' => $partner->country_id,
                    'status' => 'A'
                ])
                ->orderAsc('name')
                ->all()
                ->toArray();

            $states = [];

            if(!empty($statesAll)){
                foreach ($statesAll as $state) {
                    $states[$state->id] = $state->name;
                }
            }
           
        }

        $cities = [];
        if (!empty($partner->state_id)) {
            $citiesAll = $this->Cities->find()
            ->select(['id', 'name'])
            ->where([
                'state_id' => $partner->state_id,
                'status' => 'A'
            ])
            ->orderAsc('name')
            ->all()
            ->toArray();

            $cities = [];

            if(!empty($citiesAll)){
                foreach ($citiesAll as $city) {
                    $cities[$city->id] = $city->name;
                }
            }
        }

        $media = $this->Media;
        $meta_robot_options = Configure::read('Constants.META_ROBOT_OPTIONS');
        $languages = Configure::read('Constants.Language');
        $this->set(compact('partner', 'partnetypes', 'countries', 'states', 'cities', 'media', 'yogaStyles', 'specialNeeds', 'techniques', 'level', 'languages', 'courseTypes', 'modalities', 'meta_robot_options', 'domesticTaxPercent', 'internationalTaxPercent'));
    }

    // Check Duplicate Phone
    public function checkDuplicatePhone()
    {
        $this->request->allowMethod(['post']);
        $phoneNumber = str_replace(' ', '', trim($this->request->getData('phone_number')));
        $country_code = trim($this->request->getData('country_code'));
        $country_code = str_replace(' ', '', $country_code);
        $currentId = $this->request->getData('id');
        $isDuplicate = false;

        $query = $this->Partners->find()
            ->where(['phone' => $phoneNumber])
            ->where(['country_code' => $country_code])
            ->where(['status !=' => 'D']);

        if (!empty($currentId)) {
            $query->where(['id !=' => $currentId]);
        }
        
        if ($query->count() > 0) {
            $isDuplicate = true;
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode(['isDuplicate' => $isDuplicate]));
    }

    // Check Duplicate Email
    public function checkDuplicateEmail()
    {
        $this->request->allowMethod(['post']);
        $email = $this->request->getData('email');
        $currentId = $this->request->getData('id');
        $isDuplicate = false;

        $query = $this->Partners->find()
            ->where(['email' => $email])
            ->where(['status !=' => 'D']);

        if (!empty($currentId)) {
            $query->where(['id !=' => $currentId]);
        }

        if ($query->count() > 0) {
            $isDuplicate = true;
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode(['isDuplicate' => $isDuplicate]));
    }

    // Edit
    public function edit($id = null)
    {
        $partner = $this->Partners->get($id, [
            'contain' => ['PartnerGalleries', 'YogaStyles', 'SpecialNeeds', 'Modalities']
        ]);

        if (!empty($partner->language)) {
            $partner->language = explode(',', $partner->language);
        }

        // Pull first_name and last_name from User for form filling
        // if (!empty($partner->user)) {
        //     $partner->first_name = $partner->user->first_name;
        //     $partner->last_name = $partner->user->last_name;
        // }

        $partnetypes = $this->Partners->PartnerTypes->find('list')->where(['PartnerTypes.status' => 'A'])->all();
        $countries = $this->Countries->find('list', ['keyField' => 'id', 'valueField' => 'name'])->where(['status' => 'A'])->toArray();

        if ($this->request->is(['patch', 'post', 'put'])) {
            $data = $this->request->getData();

            $connection = $this->Partners->getConnection();
            $connection->begin();
            $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
            $phoneNumber = str_replace(' ', '', trim($data['phone']));
            $country_code = trim(str_replace(' ', '', $data['country_code']));
            try {
                // Update related User record
                // if (!empty($partner->user_id)) {
                //     $user = $this->Partners->Users->get($partner->user_id);
                //     $user = $this->Partners->Users->patchEntity($user, [
                //         'first_name' => $data['first_name'],
                //         'last_name' => $data['last_name'],
                //     ]);

                //     if ($user->hasErrors()) {
                //         throw new \Exception('User validation failed: ' . json_encode($user->getErrors()));
                //     }

                //     if (!$this->Partners->Users->save($user)) {
                //         throw new \Exception('Failed to update user: ' . json_encode($user->getErrors()));
                //     }
                // }
                // unset($data['first_name'], $data['last_name'], $data['password'], $data['phone'], $data['country_code']);
                $data['phone'] = $phoneNumber;
                $data['country_code'] = $country_code;

                // Handle logo upload
                $logoPath = WWW_ROOT . 'uploads/partners/logo/';
                if (!file_exists($logoPath)) {
                    mkdir($logoPath, 0775, true);
                }

                if (!empty($data['logo']) && $data['logo'] instanceof UploadedFile && $data['logo']->getClientFilename()) {
                    $logoName = time() . '_' . preg_replace('/[^a-zA-Z0-9._-]/', '', $data['logo']->getClientFilename());
                    $data['logo']->moveTo($logoPath . $logoName);
                    $data['logo'] = $logoName;
                    if (!empty($partner->logo) && file_exists($logoPath . $partner->logo)) {
                        unlink($logoPath . $partner->logo);
                    }
                } else {
                    unset($data['logo']); // Do not overwrite logo if no new upload
                }
                $deletedMediaIds = !empty($data['deletedImages']) ? json_decode($data['deletedImages'], true) : [];
                unset($data['deletedImages']);

                // Convert multi-select values to string
                // $data['techniques'] = !empty($data['techniques']) ? implode(',', $data['techniques']) : null;
                $data['level'] = !empty($data['level']) ? implode(',', $data['level']) : null;
                $data['operating_months'] = !empty($data['operating_months']) ? implode(',', $data['operating_months']) : null;

                // Handle associations
                // if (!empty($data['yoga_style_id'])) {
                //     $data['yoga_styles']['_ids'] = $data['yoga_style_id'];
                //     unset($data['yoga_style_id']);
                // }
                // if (!empty($data['special_need_id'])) {
                //     $data['special_needs']['_ids'] = $data['special_need_id'];
                //     unset($data['special_need_id']);
                // }

                $partnerId = $partner->id;
                if (!empty($data['yoga_style_id'])) {
                    $this->Partners->PartnerYogaStyles->deleteAll(['partner_id' => $partnerId]);

                    foreach ($data['yoga_style_id'] as $yogaStyleId) {
                        if ($yogaStyleId == 'n/a') {
                            continue; // Skip 'n/a'
                        }

                        $partnerYogaStyle = $this->Partners->PartnerYogaStyles->newEmptyEntity();
                        $partnerYogaStyle->partner_id = $partnerId;
                        $partnerYogaStyle->yoga_style_id = $yogaStyleId;

                        $this->Partners->PartnerYogaStyles->save($partnerYogaStyle);
                    }
                }

                if (!empty($data['special_need_id'])) {
                    $this->Partners->PartnerSpecialNeeds->deleteAll(['partner_id' => $partnerId]);

                    foreach ($data['special_need_id'] as $specialNeedId) {
                        if ($specialNeedId == 'n/a') {
                            continue; // Skip 'n/a'
                        }

                        $partnerSpecialNeed = $this->Partners->PartnerSpecialNeeds->newEmptyEntity();
                        $partnerSpecialNeed->partner_id = $partnerId;
                        $partnerSpecialNeed->special_need_id = $specialNeedId;

                        $this->Partners->PartnerSpecialNeeds->save($partnerSpecialNeed);
                    }
                }

                if (!empty($data['techniques']) && is_array($data['techniques']) && !(in_array('n/a', $data['techniques']))) {
                    $data['techniques'] = implode(',', $data['techniques']);
                }

                // Map 'modality' (form field) to 'modalities._ids' for belongsToMany save
                if (!empty($data['modality'])) {
                    $data['modalities']['_ids'] = $data['modality'];
                    unset($data['modality']);
                }

                // Lookup domestic tax percent title from MasterData using the submitted ID
                if (!empty($data['domestic_tax_percentage'])) {
                    $domesticTax = $this->MasterData->get($data['domestic_tax_percentage']);
                    $data['domestic_tax_percentage'] = $domesticTax->title;  // Replace ID with title like '5%'
                }

                // Lookup international tax percent title from MasterData using the submitted ID
                if (!empty($data['international_tax_percentage'])) {
                    $internationalTax = $this->MasterData->get($data['international_tax_percentage']);
                    $data['international_tax_percentage'] = $internationalTax->title;
                }
                
                $partnerId = $partner->id;
                $slugName = '';

                if (!empty($data['name'])) {
                    $slugName = $this->slugify($data['name']);
                } elseif (!empty($data['slug'])) {
                    $providedSlug = trim($data['slug']);
                    $slugName = $this->slugify(preg_replace('/^\d+-/', '', $providedSlug));
                } else {
                    $slugName = $this->slugify($data['name']);
                }

                $newSlugWithId = $partnerId . '-' . $slugName;

                // Optional validation to prevent tampering with ID in slug
                if (!preg_match('/^' . $partnerId . '-/', $newSlugWithId)) {
                    throw new \Exception('Invalid slug format. Partner ID mismatch detected.');
                }

                // Check if slug is already taken
                $existingPartner = $this->Partners->find()
                    ->where(['slug' => $newSlugWithId, 'id !=' => $partnerId])
                    ->first();

                if ($existingPartner) {
                    throw new \Exception('Slug already exists for another partner.');
                }

                // Handle old_url for SEO tracking
                $oldSlug = $partner->slug;
                if ($oldSlug && $newSlugWithId !== $oldSlug) {
                    $oldUrls = [];
                    if (!empty($partner->old_url)) {
                        $oldUrls = array_map('trim', explode(',', $partner->old_url));
                    }

                    if (!in_array($oldSlug, $oldUrls)) {
                        $oldUrls[] = $oldSlug;
                    }

                    $oldUrls = array_slice($oldUrls, -5);
                    $data['old_url'] = implode(', ', $oldUrls);
                }

                $data['slug'] = $newSlugWithId;

                // Convert multi-select values to string
                $data['language'] = !empty($data['language']) && is_array($data['language']) 
                    ? implode(',', $data['language']) 
                    : null;
                    
                $partner = $this->Partners->patchEntity($partner, $data, [
                    'associated' => ['YogaStyles', 'SpecialNeeds', 'Modalities']
                ]);

                if ($partner->hasErrors()) {
                    throw new \Exception('Partner validation failed: ' . json_encode($partner->getErrors()));
                }





                if (!$this->Partners->save($partner)) {
                    throw new \Exception('Failed to update partner');
                }

                // Save Course Types
                $courseTypeIds = !empty($data['course_type_id']) ? $data['course_type_id'] : [];

                $this->PartnerCourseTypes->deleteAll(['partner_id' => $partner->id]); // clear old links

                if (!empty($courseTypeIds)) {
                    foreach ($courseTypeIds as $courseTypeId) {
                        $partnerCourseType = $this->PartnerCourseTypes->newEmptyEntity();
                        $partnerCourseType->partner_id = $partner->id;
                        $partnerCourseType->course_type_id = $courseTypeId;
                        $this->PartnerCourseTypes->save($partnerCourseType);
                    }
                }

                // Delete selected media from DB and folder
                if (!empty($deletedMediaIds)) {
                    foreach ($deletedMediaIds as $mediaId) {
                        $mediaEntity = $this->Partners->PartnerGalleries->get($mediaId);
                        if ($mediaEntity) {
                            $this->Media->deleteMedia($uploadFolder . $mediaEntity->media);
                            $this->Partners->PartnerGalleries->delete($mediaEntity);
                        }
                    }
                }

                // Handle uploaded files
                $uploadedImages = !empty($this->request->getData('image')) ? $this->handleFileUploads('image') : [];
                if (!empty($uploadedImages)) {
                    foreach ($uploadedImages as $mediaData) {
                        $PartnerImage = $this->Partners->PartnerGalleries->newEmptyEntity();
                        $PartnerImage->partner_id = $partner->id;
                        $PartnerImage->media = $mediaData['path'];
                        $PartnerImage->media_type = $mediaData['type']; // 'image' or 'video'
                        $PartnerImage->status = 'A';
                        $this->Partners->PartnerGalleries->save($PartnerImage);
                    }
                }

                // // Delete old video URLs
                // $this->Partners->PartnerGalleries->deleteAll([
                //     'partner_id' => $partner->id,
                //     'media_type' => 'video'
                // ]);

                // // Save new video URLs
                // if (!empty($this->request->getData('video_url'))) {
                //     $videoUrls = $this->request->getData('video_url');

                //     foreach ($videoUrls as $url) {
                //         $url = trim($url);
                //         if (!empty($url)) {
                //             $video = $this->Partners->PartnerGalleries->newEmptyEntity();
                //             $video->partner_id = $partner->id;
                //             $video->media_type = 'video';
                //             $video->media = ''; // Not used when saving just URL
                //             $video->url = $url;
                //             $video->status = 'A';

                //             $this->Partners->PartnerGalleries->save($video);
                //         }
                //     }
                // }

                $connection->commit();
                $this->Flash->success(__('The partner has been updated.'), [
                    'key' => 'partner_success'
                ]);

                return $this->redirect(['action' => 'index']);
                
            } catch (\Exception $e) {
                $connection->rollback();
                Log::error('Partner update failed: ' . $e->getMessage());
                Log::error('Stack trace: ' . $e->getTraceAsString());
                $this->Flash->error(__('Failed to update partner: ' . $e->getMessage()), [
                    'key' => 'partner_error'
                ]);
            }
        }

        $states = [];
        if (!empty($partner->country_id)) {
           $statesAll = $this->States->find()
                ->select(['id', 'name'])
                ->where([
                    'country_id' => $partner->country_id,
                    'status' => 'A'
                ])
                ->orderAsc('name')
                ->all()
                ->toArray();

            $states = [];

            if(!empty($statesAll)){
                foreach ($statesAll as $state) {
                    $states[$state->id] = $state->name;
                }
            }
           
        }

        $cities = [];
        if (!empty($partner->state_id)) {
            $citiesAll = $this->Cities->find()
            ->select(['id', 'name'])
            ->where([
                'state_id' => $partner->state_id,
                'status' => 'A'
            ])
            ->orderAsc('name')
            ->all()
            ->toArray();

            $cities = [];

            if(!empty($citiesAll)){
                foreach ($citiesAll as $city) {
                    $cities[$city->id] = $city->name;
                }
            }
        }

        $localities = [];
        if (!empty($partner->city_id)) {
            $localitiesAll = $this->Localities->find()
                ->select(['id', 'name'])
                ->where([
                    'city_id' => $partner->city_id
                ])
                ->orderAsc('name')
                ->all()
                ->toArray();

            foreach ($localitiesAll as $locality) {
                $localities[$locality->id] = $locality->name;
            }
        }

        $mediaImages = [];
        if (!empty($partner->partner_galleries)) {
            $webroot = $this->request->getAttribute('webroot');

            foreach ($partner->partner_galleries as $media) {
                $pathUrl = $this->Media->displayImage($media->media);
                $imageUrl = $webroot . $pathUrl;
                $mediaImages[] = [
                    'id' => $media->id,
                    'displayname' => basename($media->media),
                    'path' => $media->media,
                    'url' => $imageUrl,
                ];
            }
        }
        $media = $this->Media;

        $yogaStyles = $this->MasterData->findByType('yoga_style')->toArray();
        $specialNeeds = $this->MasterData->findByType('special_need')->toArray();
        $techniques = $this->MasterData->findByType('technique')->toArray();
        $yogaStyles['n/a']  = 'N/A';
        $specialNeeds['n/a']= 'N/A';   
        $techniques['n/a']  = 'N/A';
        $level = $this->MasterData->findByType('level');

        $domesticTaxPercentList = $this->MasterData->findByType('domestic_tax_percentage');
        $domesticTaxPercent = $domesticTaxPercentList->toArray(); 

        // Convert value '5%' to its ID (16)
        $selectedDomesticTaxKey = array_search($partner->domestic_tax_percentage, $domesticTaxPercent);
        $partner->domestic_tax_percentage = $selectedDomesticTaxKey;

        // Same for international if needed
        $internationalTaxPercentList = $this->MasterData->findByType('international_tax_percentage');
        $internationalTaxPercent = $internationalTaxPercentList->toArray();

        $selectedInternationalTaxKey = array_search($partner->international_tax_percentage, $internationalTaxPercent);
        $partner->international_tax_percentage = $selectedInternationalTaxKey;

        $languages = Configure::read('Constants.Language');
        $courseTypes = $this->CourseTypes->find('list')->where(['CourseTypes.status' => 'A'])->all();
        $meta_robot_options = Configure::read('Constants.META_ROBOT_OPTIONS');
      
        // Convert level from string to array for multi-select
        if (!empty($partner->level)) {
            $partner->level = explode(',', $partner->level);
        }

        // if (!empty($data['techniques'])) {
        //     $techniquesArray = is_array($data['techniques']) ? $data['techniques'] : explode(',', $data['techniques']);

        //     $filteredTechniques = array_filter($techniquesArray, function($t) {
        //         return strtolower(trim($t)) !== 'n/a';
        //     });

        //     $data['techniques'] = !empty($filteredTechniques) ? implode(',', $filteredTechniques) : null;
        // }

        // if (!empty($partner->techniques)) {
        //     $partner->techniques = explode(',', $partner->techniques);
        // }

        $selectedTechniques = [];
        if (!empty($partner->techniques)) {
            $techniquesArr = explode(',', $partner->techniques);
            foreach ($techniquesArr as $val) {
                $selectedTechniques[] = $val;
            }
        }
        if (empty($selectedTechniques)) {
            $selectedTechniques[] = 'n/a'; 
        }
        $partner->techniques = $selectedTechniques;


        $selectedYogaStyles = [];
        if (!empty($partner->yoga_styles)) {
            foreach ($partner->yoga_styles as $style) {
                $selectedYogaStyles[] = $style->id;
            }
        }
        // If no saved yoga styles, show only 'n/a'
        if (empty($selectedYogaStyles)) {
            $selectedYogaStyles[] = 'n/a';
        }
        $partner->yoga_style_id = $selectedYogaStyles;


        $selectedSpecialNeeds = [];
        if (!empty($partner->special_needs)) {
            foreach ($partner->special_needs as $sn) {
                $selectedSpecialNeeds[] = $sn->id;
            }
        }
        if (empty($selectedSpecialNeeds)) {
            $selectedSpecialNeeds[] = 'n/a';
        }
        $partner->special_need_id = $selectedSpecialNeeds;

        
        // $videoUrls = [];

        // if (!empty($partner->partner_galleries)) {
        //     foreach ($partner->partner_galleries as $media) {
        //         if ($media->media_type === 'video') {
        //             $videoUrls[] = $media->url ?? '';
        //         }
        //     }
        // }

        if (!empty($partner->operating_months)) {
            $partner->operating_months = explode(',', $partner->operating_months);
        }

        // Fetch existing course_type_ids for this partner
        $selectedCourseTypes = $this->PartnerCourseTypes->find()
            ->select(['course_type_id'])
            ->where(['partner_id' => $partner->id])
            ->all()
            ->extract('course_type_id')
            ->toList();

        $partner->course_type_id = $selectedCourseTypes;

        // Prepare selected modalities for the form (extract IDs)
        $selectedModalities = $partner->modalities ? collection($partner->modalities)->extract('id')->toList() : [];

        // Fetch all modalities for the dropdown
        $modalities = $this->Partners->Modalities->find('list')->toArray();

        $this->set(compact('partner', 'partnetypes', 'countries', 'states', 'cities', 'localities', 'media','mediaImages', 'yogaStyles', 'specialNeeds', 'techniques', 'level', 'languages', 'courseTypes', 'modalities', 'selectedModalities', 'meta_robot_options', 'domesticTaxPercent', 'internationalTaxPercent'));
    }

    private function handleFileUploads($fileInputName = '')
    {
        $files = $this->request->getData($fileInputName);
        $uploadedFiles = [];
        if (!empty($files) && is_array($files)) {
            foreach ($files as $file) {
                if ($file->getError() === UPLOAD_ERR_OK) {
                    $fileName = preg_replace('/[^A-Za-z0-9_\-\.]/', '_', trim($file->getClientFilename()));
                    if (!empty($fileName)) {
                        $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                        $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                        $filePath = 'partners/images/';
                        $folderPath = $uploadFolder . $filePath;
                        $ext = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                        $newFileName = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                        $uploadResult = $this->Media->uploadMedia($file, $newFileName, $folderPath);

                        if ($uploadResult !== 'Success') {
                            $this->Flash->error(__($fileInputName . ' ' . $fileName . ' could not be uploaded.'));
                        } else {
                            $mediaType = in_array($ext, ['mp4', 'mov', 'avi', 'mkv']) ? 'video' : 'image';
                            $uploadedFiles[] = [
                                'path' => $filePath . $newFileName,
                                'type' => $mediaType
                            ];
                        }
                    }
                }
            }
        }
        return $uploadedFiles;
    }
    
    // Delete
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $response = ['success' => false, 'key' => 'partner_error'];

        try {
            // Get the Partner
            $partner = $this->Partners->get($id);

            if ($partner) {
                // Soft delete the Partner
                $partner->status = 'D';

                if ($this->Partners->save($partner)) {
                    // Get user_id from partner
                    $userId = $partner->user_id;

                    if (!empty($userId)) {

                        // Soft delete user linked to this partner
                        $updateResult = $this->Users->updateAll(
                            ['status' => 'D'], // Set status = D
                            ['id' => $userId]  // Where id = user_id
                        );
                    }

                    $response = ['success' => true, 'key' => 'partner_success'];
                }
            }
        } catch (\Exception $e) {
            $response = ['success' => false, 'key' => 'partner_error'];
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success(__($response['key']));
            } else {
                $this->Flash->error(__($response['key']));
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    // PartnersController.php
    public function hasCourses($id = null)
    {
        $this->request->allowMethod(['get']);
        $response = ['hasCourses' => false];

        if ($id) {
            $coursesCount = $this->Partners->Courses->find()
                ->where(['partner_id' => $id, 'status' => 'A'])
                ->count();

            if ($coursesCount > 0) {
                $response['hasCourses'] = true;
            }
        }

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));
        return $this->response;
    }


    // Filer
    public function filterSearch()
    {
        $this->request->allowMethod(['get']);
        $status = $this->request->getQuery('filterStatus');
        if ($this->request->is('ajax')) {

            $query = $this->Partners->find()
                ->contain([
                    'Users',
                    'Countries',
                    'States',
                    'Cities',
                    'PartnerTypes'
                ])
                ->orderBy(['Partners.id' => 'DESC']);

            if ($status) {
                $query->where(['Partners.status' => $status]);
            } else {
                $query->where(['Partners.status !=' => 'D']);
            }

            $statusMap = [
                'A' => ['label' => __('Active'), 'class' => 'badge-outline col-green'],
                'I' => ['label' => __('Inactive'), 'class' => 'badge-outline col-red'],
                'D' => ['label' => __('Deleted'), 'class' => 'badge-outline col-red']
            ];

            $partners = [];
            $i = 1;
            foreach ($query as $partner) {
                $status = $statusMap[$partner->status] ?? ['label' => __('Unknown'), 'class' => 'badge-outline col-red'];
                $statusToggleUrl = Router::url(['controller' => 'Partners', 'action' => 'approve', $partner->id], true);
                $approveClass = ' btn-sm approve approve ms-2';
                $approveTitle = $partner->status === 'A' ? 'Mark as Inactive' : 'Mark as Active';

                // Access first_name and last_name from the Users table
                $firstName = !empty($partner->user->first_name) ? h($partner->user->first_name) : '-';
                $lastName = !empty($partner->user->last_name) ? h($partner->user->last_name) : '-';
                $fullName = $firstName . ' ' . $lastName;

                $partners[] = [
                    'actions' => '<a href="' . Router::url(['controller' => 'Partners', 'action' => 'view', $partner->id], true) . '" class="btn-view" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a> ' .
                        '<a href="' . Router::url(['controller' => 'Partners', 'action' => 'edit', $partner->id], true) . '" class="btn-edit" data-toggle="tooltip" title="Edit"><i class="fas fa-edit m-r-10"></i></a>' .
                        ' <a href="javascript:void(0);" class="delete-button" data-id="<?= $partner->id ?>" data-toggle="tooltip" title="Delete"
                                                    data-bs-toggle="modal" data-bs-target="#exampleModalCenter"> <i class="far fa-trash-alt"></i>
                                                </a>' .
                        '<a href="' . $statusToggleUrl . '" class="' . $approveClass . '" data-toggle="tooltip" title="' . $approveTitle . '"><i class="fas fa-check"></i></a>',
                    'id' => $partner->id,
                    'name' => !empty($partner->name) ? h(strlen($partner->name) > 40 ? substr($partner->name, 0, 40) . '...' : $partner->name) : '-',
                    'first_name' => $fullName,
                    'email' => !empty($partner->email) ? h($partner->email) : '-',
                    'phone' => !empty($partner->phone) ?  '+' . h($partner->country_code) . ' ' . h($partner->phone) : '-',
                    'address' => !empty($partner->address) ? h(strlen($partner->address) > 40 ? substr($partner->address, 0, 40) . '...' : $partner->address) : '-',
                    'status' => '<div class="badge-outline ' . h($status['class']) . '">' . h($status['label']) . '</div>',
                ];
                $i++;
            }

            $this->set([
                'partners' => $partners,
                '_serialize' => ['partners'],
            ]);

            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['data' => $partners]));
        }

        return null;
    }

    // CSV Export
    public function exportPartners()
    {
        $status = $this->request->getQuery('status');
        $query = $this->Partners->find()
            ->contain(['Users']) // Join Users table to get first_name and last_name
            ->where(['Partners.status !=' => 'D']);

        if (!empty($status)) {
            $query->where(['Partners.status' => $status]);
        }

        $partners = $query->all();
        $filename = "partners_list_" . date('Y-m-d') . ".csv";
        $this->response = $this->response->withDownload($filename);
        $this->response = $this->response->withType('text/csv');
        $csvData = fopen('php://output', 'w');

        // CSV Header
        fputcsv($csvData, ['S.No', 'Partner Name', 'Name', 'Email', 'Phone', 'Address', 'Status']);

        $counter = 1;
        // Status mapping
        $statusMap = [
            'A' => 'Active',
            'I' => 'Inactive',
            'D' => 'Deleted'
        ];

        foreach ($partners as $partner) {
            // Handle missing country_code or phone
            $phone = !empty($partner->country_code) && !empty($partner->phone)
                ? '+' . $partner->country_code . ' ' . $partner->phone
                : 'None';

            // Get user's first and last name from the Users table
            $fullName = !empty($partner->user)
                ? $partner->user->first_name . ' ' . $partner->user->last_name
                : 'None';

            // Convert status to readable format
            $status = isset($statusMap[$partner->status]) ? $statusMap[$partner->status] : 'Unknown';

            // Write partner data to CSV
            fputcsv($csvData, [
                $counter++,
                $partner->name,
                $fullName,
                $partner->user->email ?? 'None', // Email from the Users table
                $phone,
                $partner->address,
                $status
            ]);
        }

        fclose($csvData);
        return $this->response;
    }

    public function approve($id)
    {
        $partner = $this->Partners->get($id);

        // Toggle status: If Active -> set to Inactive, else set to Active
        $partner->status = ($partner->status === 'A') ? 'I' : 'A';

        if ($this->Partners->save($partner)) {

            $this->Flash->success(__('Partner status updated successfully.'), [
                'key' => 'partner_success'
            ]);
        } else {
            $this->Flash->error(__('Unable to update partner status. Please try again.'), [
                'key' => 'partner_error'
            ]);
        }

        return $this->redirect(['action' => 'index']);
    }

    // Contry based states
    public function getStates()
    {
        $this->request->allowMethod(['ajax']);
        $countryId = $this->request->getQuery('country_id');

        $states = $this->States->find()
            ->select(['id', 'name'])
            ->where(['country_id' => $countryId])
            ->orderAsc('name')
            ->all()
            ->toArray();
      
        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode(['states' => $states]));
        return $this->response;
    }

    // State based cities
    public function getCities()
    {
        $this->request->allowMethod(['ajax']);
        $stateId = $this->request->getQuery('state_id');

        $cities = $this->Cities->find()
            ->select(['id', 'name'])
            ->where(['state_id' => $stateId])
            ->orderAsc('name')
            ->all()
            ->toArray();

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode(['cities' => $cities]));
        return $this->response;
    }

    public function getLocalities()
    {
        $this->request->allowMethod(['ajax']);
        $cityId = $this->request->getQuery('city_id');
        $localities = $this->fetchTable('localities')->getListByCity($cityId);

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode(['localities' => $localities]));
        return $this->response;
    }

    protected function _sendWelcomeEmail($user, $plainPassword)
    {
        try {
            $email = new Mailer('default');
            $email->setFrom([Configure::read('App.adminEmail') => Configure::read('App.name')])
                ->setTo($user->email)
                ->setSubject('Welcome to ' . Configure::read('App.name'))
                ->setViewVars([
                    'firstName' => $user->first_name,
                    'email' => $user->email,
                    'password' => $plainPassword,
                    'loginUrl' => Router::url(['controller' => 'Users', 'action' => 'login', 'prefix' => 'Partner'], true)
                ])
                ->setTemplate('partner_welcome')
                ->setEmailFormat('both');

            $email->send();
        } catch (\Exception $e) {
            Log::error('Failed to send welcome email: ' . $e->getMessage());
        }
    }

    private function slugify($string)
    {
        return strtolower(str_replace([' ', '_'], '-', trim($string)));
    }

}




