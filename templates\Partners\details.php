<?php $this->assign('title', h(!empty($partner->meta_title) ? $partner->meta_title : $partner->name)); ?>
<?php $this->assign('meta_desc', h(!empty($partner->meta_description) ? $partner->meta_description : $partner->short_description)); ?>
<?php $this->assign('keywords', h($partner->meta_keywords)); ?>
<!-- <?php $this->assign('meta_robots', h($partner->meta_robots)); ?> -->

<?php
// for og tags
ob_start();
foreach ($ogTags as $property => $content) {
    echo '<meta property="' . h($property) . '" content="' . h($content) . '">' . "\n";
}
$this->assign('og_tags', ob_get_clean());
?>
<!-- end of og tags -->
<!--  schema generator -->
<?php
ob_start();
echo '<script type="application/ld+json">' . json_encode($schemaJson, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . '</script>';
$this->assign('json_ld_schema', ob_get_clean());
?>

<link rel="stylesheet" href="<?= $this->Url->webroot('css/center.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/testimonial.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/card.css') ?>">
<script src="<?= $this->Url->webroot('js/center.js') ?>"></script>

<section class="course-detail-container">
    <div class="px-6 md:px-10 lg:px-25 xl:pl-40 xl:pr-21 2xl:px-60">
        <nav class="flex breadcrumb-nav desktop-view" aria-label="Breadcrumb">
            <ol class="pt-4 flex items-center space-x-1 md:space-x-0 rtl:space-x-reverse">
                <li>
                    <a href="<?= $this->Url->build('/') ?>" class="flex items-center text-sm text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">Home</a>
                </li>
                <li class="flex items-center">
                    <svg class="rtl:rotate-180 w-[5px] h-3 text-[#1C1B1F] mx-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                    </svg>
                    <a href="<?=  $this->Url->build([
                                'lang' => $this->request->getParam('lang'),
                                'controller' => 'Partners',
                                'action' => 'index',
                                'country' => $this->request->getParam('country') ?? 'india'
                            ]) ?>" class="flex items-center text-sm text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white ">Centers</a>
                </li>
                <?php if (!empty($country)): ?>
                    <li>
                        <div class="flex items-center">
                            <svg class="rtl:rotate-180 w-[5px] h-3 text-[#1C1B1F] mx-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="flex items-center text-sm text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white "><?= h($country->name) ?></span>
                        </div>
                    </li>
                <?php endif; ?>
                <?php if (!empty($state)): ?>
                    <li>
                        <div class="flex items-center">
                            <svg class="rtl:rotate-180 w-[5px] h-3 text-[#1C1B1F] mx-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="flex items-center text-sm text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white "><?= h($state->name) ?></span>
                        </div>
                    </li>
                <?php endif; ?>
                <?php if (!empty($city)): ?>
                    <li>
                        <div class="flex items-center">
                            <svg class="rtl:rotate-180 w-[5px] h-3 text-[#1C1B1F] mx-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="flex items-center text-sm text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white "><?= h($city->name) ?></span>
                        </div>
                    </li>
                <?php endif; ?>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-[5px] h-3 text-[#1C1B1F] mx-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="flex items-center text-sm text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white "><?= h($partner->name) ?></span>
                    </div>
                </li>
            </ol>
        </nav>
        <h1 class="heading"><?= h($partner->name) ?>
            <span class='rating'>
                <span class="rate-point">4.5</span>
                <i class='fas fa-star'></i>
                <span class="review-info">(36 reviews)</span>
            </span>
        </h1>
        <div class="yoga-detail-wrapper">
            <div class="block lg:flex lg:items-start">
                <div x-data="{  
                            autoplayIntervalTime: 4000,
                            slides: [
                                <?php
                                $hasGalleries = false;
                                if (!empty($partner->partner_galleries)) {
                                    $hasGalleries = true;
                                    foreach ($partner->partner_galleries as $gallery):
                                        $mediaPath = $gallery->media;
                                        $mediaUrl = $this->Url->webroot("uploads/" . h($mediaPath));
                                        $extension = strtolower(pathinfo($mediaPath, PATHINFO_EXTENSION));
                                        $isVideo = in_array($extension, ['mp4', 'webm', 'ogg', 'mov', 'mkv', 'avi']);
                                ?>
                                    {
                                        type: '<?= $isVideo ? 'video' : 'image' ?>',
                                        src: '<?= $mediaUrl ?>',
                                        alt: '<?= h($partner->name) ?>'
                                    },
                                <?php endforeach;
                                }
                                if (!$hasGalleries): ?>
                                    {
                                        type: 'image',
                                        src: '<?= $this->Url->webroot("img/default-course.png") ?>',
                                        alt: '<?= h($partner->name) ?>'
                                    },
                                <?php endif; ?>
                            ],
                            currentSlideIndex: 0,
                            isPaused: false,
                            autoplayInterval: null,
                            isSingleSlide: false,
                            
                            init() {
                                // Check if there's only one slide
                                this.isSingleSlide = this.slides.length === 1;
                                
                                // Only start autoplay if there are multiple slides
                                if (!this.isSingleSlide) {
                                    this.autoplay();
                                }
                            },
                            
                            previous() {
                                if (this.isSingleSlide) return;
                                this.currentSlideIndex = this.currentSlideIndex > 0 ? this.currentSlideIndex - 1 : this.slides.length - 1;
                            },
                            
                            next() {
                                if (this.isSingleSlide) return;
                                this.currentSlideIndex = this.currentSlideIndex < this.slides.length - 1 ? this.currentSlideIndex + 1 : 0;
                            },
                            
                            autoplay() {
                                if (this.isSingleSlide) return;
                                this.autoplayInterval = setInterval(() => {
                                    if (!this.isPaused) this.next();
                                }, this.autoplayIntervalTime);
                            },
                            
                            setAutoplayInterval(newIntervalTime) {
                                if (this.isSingleSlide) return;
                                clearInterval(this.autoplayInterval);
                                this.autoplayIntervalTime = newIntervalTime;
                                this.autoplay();
                            }
                }" x-init="init()" class="relative w-full detail-slider">

                    <!-- Slider Area -->
                    <div class="relative w-full min-h-[289px] lg:min-h-[494px] yoga-retreat-slider overflow-hidden">
                        <template x-for="(slide, index) in slides" :key="index">
                            <div x-show="isSingleSlide || currentSlideIndex === index" class="absolute inset-0" x-transition.opacity.duration.1000ms>

                                <!-- Image Slide -->
                                <template x-if="slide.type === 'image'">
                                    <img
                                        class="absolute w-full h-full object-cover rounded-[10px]"
                                        :src="slide.src"
                                        :alt="slide.alt" />
                                </template>

                                <!-- Video Slide -->
                                <template x-if="slide.type === 'video'">
                                    <video
                                        class="absolute w-full h-full object-cover rounded-[10px]"
                                        :src="slide.src"
                                        autoplay muted playsinline
                                        @playing="isPaused = true"
                                        @ended="isPaused = false"
                                        loop></video>
                                </template>
                            </div>
                        </template>
                    </div>

                    <!-- Indicators - Only show if there are multiple slides -->
                    <div x-show="!isSingleSlide" class="indicator absolute bottom-3 md:bottom-5 left-1/2 z-20 flex -translate-x-1/2 gap-4 md:gap-3 px-1.5 py-1 md:px-2" role="group" aria-label="slides">
                        <template x-for="(slide, index) in slides" :key="index">
                            <button
                                class="size-3 rounded-full transition"
                                :class="currentSlideIndex === index ? 'bg-[#D87A61]' : 'bg-[#D87A61]/40'"
                                @click="currentSlideIndex = index"
                                :aria-label="'Slide ' + (index + 1)">
                            </button>
                        </template>
                    </div>
                </div>
                <div class="yoga-details-content" x-data="{ expanded: false }">
                    <div  :class="{ 'max-h-[310px] overflow-hidden': !expanded }" class="transition-all duration-300 ease-in-out lg:max-h-none lg:overflow-visible">
                        <div class="location-content">
                            <label class="course-title">Where and When</label>
                            <p><i class="fas fa-map-marker-alt"></i> <span>
                                    <?php
                                    $location = [];
                                    if (!empty($city)) $location[] = h($city->name);
                                    if (!empty($state)) $location[] = h($state->name);
                                    if (!empty($country)) $location[] = h($country->name);
                                    echo !empty($location) ? 'On-site in <a>' . implode(', ', $location) . '</a>' : '-';
                                    ?>
                                </span></p>
                            <p><i class='fas fa-calendar-alt'></i> <span><?= $partner->is_open ? 'Open' : 'Closed' ?></span></p>
                        </div>

                        <?php if (!empty($partner->courses)): ?>
                            <?php
                            $courseTypes = [];
                            $yogaStyles = [];
                            $specialNeeds = [];

                            foreach ($partner->courses as $course) {
                                if (!empty($course->course_type)) {
                                    $courseTypes[$course->course_type->id] = $course->course_type->name;
                                }
                            }
                        ?>

                            <?php if (!empty($courseTypes)): ?>
                                <div class="course-contents">
                                    <label class="course-title">Courses</label>
                                    <p><?= h(implode('| ', array_values($courseTypes))) ?></p>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($yoga_styles)): ?>
                                <div class="course-level block lg:flex lg:items-start">
                                    <div class="left-contents">
                                        <label class="course-title">Yoga Styles</label>
                                        <p><?= $yoga_styles->title; ?></p>
                                    </div>
                                    <div class="right-contents">
                                        <label class="course-title">Levels</label>
                                        <p><span class="v-line">Beginner</span><span>Intermediate</span></p>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($special_needs)): ?>
                                <div class="special-needs">
                                    <label class="course-title">Special Needs</label>
                                    <p><?= $special_needs->title; ?></p>
                                </div>
                            <?php endif; ?>
                    <?php endif; ?>

                        <?php if (!empty($partner->courses)): ?>
                            <?php
                            $accommodationTypes = [];
                            $foodOptions = [];

                            foreach ($partner->courses as $course) {
                                if (!empty($course->accommodation_type)) {
                                    $types = array_map('trim', explode(',', $course->accommodation_type));
                                    foreach ($types as $type) {
                                        if (!empty($type)) {
                                            $accommodationTypes[] = $type;
                                        }
                                    }
                                }

                                if (!empty($course->food_options)) {
                                    $options = array_map('trim', explode(',', $course->food_options));
                                    foreach ($options as $option) {
                                        if (!empty($option)) {
                                            $foodOptions[] = $option;
                                        }
                                    }
                                }
                            }

                            $accommodationTypes = array_unique($accommodationTypes);
                            $foodOptions = array_unique($foodOptions);

                            $accommodationTypesStr = !empty($accommodationTypes) ? implode(' | ', $accommodationTypes) : '';
                            $foodOptionsStr = !empty($foodOptions) ? implode(' | ', $foodOptions) : '';
                            ?>

                            <?php if (!empty($accommodationTypesStr) || !empty($foodOptionsStr)): ?>
                                <div class="accomodation-food block lg:flex lg:items-start">
                                    <?php if (!empty($accommodationTypesStr)): ?>
                                        <div class="left-contents">
                                            <label class="course-title">Accommodation</label>
                                            <p><?= ucfirst(h($accommodationTypesStr)) ?></p>
                                        </div>
                                    <?php endif; ?>

                                    <?php if (!empty($foodOptionsStr)): ?>
                                        <div class="right-contents">
                                            <label class="course-title">Food</label>
                                            <p><?= ucfirst(h($foodOptionsStr)) ?></p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>

                        <?php if (!empty($partner->courses)): ?>
                            <?php
                            $languages = [];
                            foreach ($partner->courses as $course) {
                                if (!empty($course->language)) {
                                    $courseLangs = array_map('trim', explode(',', $course->language));
                                    foreach ($courseLangs as $lang) {
                                        if (!empty($lang)) {
                                            $languages[] = isset($lang) ? $lang : $lang;
                                        }
                                    }
                                }
                            }
                            $languages = array_unique($languages);
                            $languagesStr = !empty($languages) ? implode(' | ', $languages) : '';
                            ?>

                            <?php if (!empty($languagesStr)): ?>
                                <div class="language-contents">
                                    <label class="course-title">Languages</label>
                                    <p><?= h($languagesStr) ?></p>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                
                    <div class="view-more mt-3 block lg:hidden text-center">
                        <button @click="expanded = !expanded" class="text-blue-600 text-sm">
                            <span x-text="expanded ? 'See Less' : 'See More'"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="detail-offer-container">
    <div class="px-6 md:px-10 lg:px-25 xl:pl-40 xl:pr-21 2xl:px-60">
        <div class="yoga-detail-wrapper">
            <div class="block lg:flex lg:items-start">
                <div class="relative w-full detail-content">
                    <h2 class="heading">Center Details</h2>
                    <?php echo !empty($partner->description) ? ($partner->description) : '-'; ?>
                </div>
                <div class="offer-content w-[520px]">
                    <div class="info">
                        <label>Address</label>
                        <p>
                            <?= h($partner->address ?: $partner->street_address) ?>
                            <?php if (!empty($city)) : ?>
                                , <?= h($city->name) ?>
                            <?php endif; ?>
                            <?php if (!empty($state)) : ?>
                                , <?= h($state->name) ?>
                            <?php endif; ?>
                            <?php if (!empty($country)) : ?>
                                , <?= h($country->name) ?>
                            <?php endif; ?>
                        </p>
                    </div>
                    <div class="info">
                        <label>Telephone</label>
                        <p><?php echo !empty($partner->phone) ? ($partner->country_code ? '+' . h($partner->country_code) . ' ' : '') . ($partner->phone) : '-'; ?></p>
                    </div>
                    <div class="info">
                        <label>Email</label>
                        <p><?php echo !empty($partner->email) ? ($partner->email) : '-'; ?></p>
                    </div>
                    <?php if (!empty($partner->website)): ?>
                        <div class="info">
                            <label>Website</label>
                            <p><a href="<?= h($partner->website) ?>" target="_blank"><?= h($partner->website) ?></a></p>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($partner->facebook_url) || !empty($partner->instagram_url) || !empty($partner->youtube_url)): ?>
                        <div class="follow">
                            <label>Follow</label>
                            <div class="social-media-icons">
                                <?php if (!empty($partner->facebook_url)): ?>
                                    <a href="<?= h($partner->facebook_url) ?>" target="_blank"><img src="<?= $this->Url->webroot('img/fb.png') ?>" alt="Facebook Icon"></a>
                                <?php endif;
                                if (!empty($partner->instagram_url)): ?>
                                    <a href="<?= h($partner->instagram_url) ?>" target="_blank"><img src="<?= $this->Url->webroot('img/insta.png') ?>" alt="Instagram Icon"></a>
                                <?php endif;
                                if (!empty($partner->youtube_url)): ?>
                                    <a href="<?= h($partner->youtube_url) ?>" target="_blank"><img src="<?= $this->Url->webroot('img/youtube-icon.png') ?>" alt="Youtube Icon"></a>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    
                </div>
            </div>
        </div>
    </div>
</section>

<section class="course-curriculum">
    <div class="px-6 md:px-10 lg:px-25 xl:pl-40 xl:pr-21 2xl:px-60">
        <h2>Accommodation and Food</h2>
        <div class="curriculum-content">
            <?php if (!empty($accommodationTypesStr)): ?>
                <h4>Accommodation</h4>
                <p><?= ucfirst(h($accommodationTypesStr)) ?></p>
            <?php endif; ?>

            <?php if (!empty($foodOptionsStr)): ?>
                <h4>Food</h4>
                <p><?= ucfirst(h($foodOptionsStr)) ?></p>
            <?php endif; ?>
        </div>
    </div>
</section>

<section class="more-courses px-6 md:px-10 lg:px-25 xl:px-40 2xl:px-60">
    <h2>Courses by <span class="text-[#D87A61]"><?= h($partner->name) ?></span></h2>
    <div class="mx-auto">
        <div class="featured-yoga course-card-container relative grid grid-cols-1 lg:grid-cols-3 gap-6 desktop-view">
            <?php if (!empty($moreCourses) && count($moreCourses) > 0): ?>
                <?php foreach ($moreCourses as $moreCourse): ?>
                     <a href="<?= $this->Url->build('/') ?><?= h($this->request->getParam('lang')) ?>/yoga-courses/<?= !empty($moreCourse->country) ? h($moreCourse->country->name) : 'india' ?>/region/<?= !empty($moreCourse->state) ? h($moreCourse->state->name) : '' ?>/<?= !empty($moreCourse->city) ? h($moreCourse->city->name) : '' ?>/<?= !empty($moreCourse->slug) ? h($moreCourse->slug) : '' ?>" class="bg-white rounded-lg card-container block">
                    <!-- <a href="<?= $this->Url->build('/courses/details/' . h($moreCourse->slug)) ?>" class="bg-white rounded-lg card-container block"> -->
                        <div class="course-card bg-white rounded-lg card-container flex flex-col min-h-[450px] h-[600px]">
                            <img src="<?= $moreCourse->image_url ?? $this->Url->webroot('img/default-course.png') ?>" alt="<?= h($moreCourse->name) ?>" class="w-100 h-[157px] object-cover yoga-img">
                            <div class="card-body">
                                <p class="info"><?= !empty($moreCourse->course_type) ? h($moreCourse->course_type->name) : 'Course' ?></p>
                                <h3 class="yoga-name line-clamp-2"><?= h($moreCourse->name) ?> <span class='rating-wrapper'><span class='rating'>4.5</span> <i class='fas fa-star'></i></span></h3>
                                <p class="line-clamp-4 yoga-description"><?= h($moreCourse->short_description ?? 'No description available.') ?></p>
                                <?php if (!empty($moreCourse->start_date)): ?>
                                    <p class="time"><i class="fas fa-calendar-alt"></i>
                                        <?= date('d-m-y', strtotime($moreCourse->start_date)) ?>
                                        <?php if (!empty($moreCourse->end_date)): ?>
                                            to <?= date('d-m-y', strtotime($moreCourse->end_date)) ?>
                                        <?php endif; ?>
                                    </p>
                                <?php endif; ?>
                                <?php if (!empty($moreCourse->mode)): ?>
                                    <p class="mode"><i class="fas fa-laptop-code"></i> <?= h($moreCourse->mode) ?></p>
                                <?php endif; ?>
                                <?php if (!empty($moreCourse->language)): ?>
                                    <p class="lang"><i class="fas fa-globe"></i> <?= h($moreCourse->language) ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </a>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-span-3 text-center py-8">
                    <p>No courses available from this partner at the moment.</p>
                </div>
            <?php endif; ?>
        </div>
        <?php if (!empty($moreCourses) && count($moreCourses) > 0): ?>
            <!-- <div x-data="{  
                autoplayIntervalTime: 4000,          
                slides: [
                    <?php foreach ($moreCourses as $index => $moreCourse): ?>
                    {
                        imgSrc: '<?= !empty($moreCourse->image) ? $this->Url->webroot('uploads/' . h($moreCourse->image)) : $this->Url->webroot('img/default-course.png') ?>',
                        imgAlt: '<?= h($moreCourse->name) ?>',
                        info: '@ <?= isset($moreCourse->partner) ? h($moreCourse->partner->name) : h($course->partner->name ?? '') ?>',
                        title: '<?= h($moreCourse->name) ?>',
                        desc: '<?= !empty($moreCourse->short_description) ? h($moreCourse->short_description) : substr(strip_tags($moreCourse->description ?? ''), 0, 120) . '...' ?>',
                        date: '<?= !empty($moreCourse->start_date) ? $moreCourse->start_date->format('d-m-y') . (!empty($moreCourse->end_date) ? ' to ' . $moreCourse->end_date->format('d-m-y') : '') : '' ?>',
                        mode: '<?= !empty($moreCourse->mode) ? h($moreCourse->mode) : (!empty($moreCourse->on_site) && $moreCourse->on_site ? (!empty($moreCourse->city) ? h($moreCourse->city->name) : 'On-site') : (!empty($moreCourse->online_live) && $moreCourse->online_live ? 'Online - Live' : (!empty($moreCourse->online_vod) && $moreCourse->online_vod ? 'Online - VOD' : ''))) ?>',
                        lang: '<?= !empty($moreCourse->language) ? h($moreCourse->language) : '' ?>',
                        url: '<?= $this->Url->build('/') ?><?= h($this->request->getParam('lang')) ?>/yoga-courses/<?=  !empty($moreCourse->country) ? h($moreCourse->country->name) : '' ?>/region/<?= !empty($moreCourse->state) ? h($moreCourse->state->name) : '' ?>/<?= !empty($moreCourse->city) ? h($moreCourse->city->name) : '' ?>/<?= !empty($moreCourse->slug) ? h($moreCourse->slug) : '' ?>'
                    },
                    <?php endforeach; ?>
                ],            
                currentSlideIndex: 1,
                isPaused: false,
                autoplayInterval: null,
                touchStartX: 0,
                touchEndX: 0,
                handleSwipeStart(event) {
                    this.touchStartX = event.type.includes('mouse') ? event.clientX : event.touches[0].clientX;
                },
                handleSwipeEnd(event) {
                    this.touchEndX = event.type.includes('mouse') ? event.clientX : event.changedTouches[0].clientX;
                    if (this.touchEndX < this.touchStartX - 40) {
                        this.next();
                    } else if (this.touchEndX > this.touchStartX + 40) {
                        this.previous();
                    }
                },
                previous() {                
                    if (this.currentSlideIndex > 1) {                    
                        this.currentSlideIndex = this.currentSlideIndex - 1                
                    } else {   
                        // If it's the first slide, go to the last slide           
                        this.currentSlideIndex = this.slides.length                
                    }            
                },            
                next() {                
                    if (this.currentSlideIndex < this.slides.length) {                    
                        this.currentSlideIndex = this.currentSlideIndex + 1                
                    } else {                 
                        // If it's the last slide, go to the first slide    
                        this.currentSlideIndex = 1                
                    }            
                },  
                autoplay() {
                    this.autoplayInterval = setInterval(() => {
                        if (! this.isPaused) {
                            this.next()
                        }
                    }, this.autoplayIntervalTime)
                },
                // Updates interval time   
                setAutoplayInterval(newIntervalTime) {
                    clearInterval(this.autoplayInterval)
                    this.autoplayIntervalTime = newIntervalTime
                    this.autoplay()
                },       
            }" x-init="autoplay" class="relative w-full featured-slider mobile-view">

                <!-- slides -->
                <!--<div class="featured-yoga course-card-container relative w-full  min-h-[547px] h-100" @touchstart="handleSwipeStart" @touchend="handleSwipeEnd" @mousedown="handleSwipeStart"
                    @mouseup="handleSwipeEnd">
                    <template x-for="(slide, index) in slides">
                        <a :href="slide.url" class="block h-full">
                            <div x-show="currentSlideIndex == index + 1" class="absolute inset-0"
                                x-transition.opacity.duration.1000ms>
                                <div class="course-card bg-white rounded-lg shadow-lg h-auto sm:h-100">
                                    <img class="w-full h-56 object-cover yoga-img" x-bind:src="slide.imgSrc"
                                        x-bind:alt="slide.imgAlt">
                                    <div class="card-body">
                                        <p class="info">
                                            <?= $moreCourse->course_type->name ? h($moreCourse->course_type->name) : '' ?>
                                        </p>
                                        <h3 class="yoga-name line-climp-2">
                                            <?= h($moreCourse->name) ?>
                                            <span class='rating-wrapper'><span class='rating'>4.5</span> <i class='fas fa-star'></i></span>
                                        </h3>
                                        <p class="yoga-description line-climp-4">
                                            <?= !empty($moreCourse->short_description) ? h($moreCourse->short_description) : substr(strip_tags($moreCourse->description ?? ''), 0, 120) . '...' ?>
                                        </p>

                                        <?php if (!empty($moreCourse->start_date)): ?>
                                            <p class="time"><i class="fas fa-calendar-alt"></i>
                                                <?= $moreCourse->start_date->format('d-m-y') ?>
                                                <?php if (!empty($moreCourse->end_date)): ?>
                                                    to <?= $moreCourse->end_date->format('d-m-y') ?>
                                                <?php endif; ?>
                                            </p>
                                        <?php endif; ?>

                                        <?php if (!empty($moreCourse->mode)): ?>
                                            <p class="mode"><i class="fas fa-laptop-code"></i> <?= h($moreCourse->mode) ?></p>
                                        <?php elseif (!empty($moreCourse->on_site) && $moreCourse->on_site): ?>
                                            <p class="mode"><i class="fas fa-map-marker-alt"></i>
                                                <?= !empty($moreCourse->city) ? h($moreCourse->city->name) : 'On-site' ?></p>
                                        <?php elseif (!empty($moreCourse->online_live) && $moreCourse->online_live): ?>
                                            <p class="mode"><i class="fas fa-laptop-code"></i> Online - Live</p>
                                        <?php elseif (!empty($moreCourse->online_vod) && $moreCourse->online_vod): ?>
                                            <p class="mode"><i class="fas fa-video"></i> Online - VOD</p>
                                        <?php endif; ?>

                                        <?php if (!empty($moreCourse->language)): ?>
                                            <p class="lang"><i class="fas fa-globe"></i> <?= h($moreCourse->language) ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </template>
                </div>

                <!-- indicators -->
                <!-- <div class="indicator absolute rounded-radius bottom-3 md:bottom-5 left-1/2 z-20 flex -translate-x-1/2 gap-4 md:gap-3 bg-transparent px-1.5 py-1 md:px-2"
                    role="group" aria-label="slides">
                    <template x-for="(slide, index) in slides">
                        <button class="size-3 rounded-full transition bg-on-surface dark:bg-on-surface-dark"
                            x-on:click="currentSlideIndex = index + 1"
                            x-bind:class="[currentSlideIndex === index + 1 ? 'bg-[#D87A61]' : 'bg-[#D87A61]/40']"
                            x-bind:aria-label="'slide ' + (index + 1)"></button>
                    </template>
                </div> -->
            <!--</div> -->

            <!-- New Slider -->
            <div x-data="{  
                autoplayIntervalTime: 4000,          
                slides:[
                     <?php foreach ($moreCourses as $index => $moreCourse): ?>
                    {
                        imgSrc: '<?= !empty($moreCourse->image) ? $this->Url->webroot('uploads/' . h($moreCourse->image)) : $this->Url->webroot('img/default-course.png') ?>',
                        imgAlt: '<?= h($moreCourse->name) ?>',
                        info: '@ <?= isset($moreCourse->partner) ? h($moreCourse->partner->name) : h($course->partner->name ?? '') ?>',
                        title: '<?= h($moreCourse->name) ?>',
                        desc: '<?= !empty($moreCourse->short_description) ? h($moreCourse->short_description) : substr(strip_tags($moreCourse->desc1_text ?? ''), 0, 120) . '...' ?>',
                        date: '<?= !empty($moreCourse->start_date) ? $moreCourse->start_date->format('d-m-y') . (!empty($moreCourse->end_date) ? ' to ' . $moreCourse->end_date->format('d-m-y') : '') : '' ?>',
                        mode: '<?= !empty($moreCourse->mode) ? h($moreCourse->mode) : (!empty($moreCourse->on_site) && $moreCourse->on_site ? (!empty($moreCourse->city) ? h($moreCourse->city->name) : 'On-site') : (!empty($moreCourse->online_live) && $moreCourse->online_live ? 'Online - Live' : (!empty($moreCourse->online_vod) && $moreCourse->online_vod ? 'Online - VOD' : ''))) ?>',
                        lang: '<?= !empty($moreCourse->language) ? h($moreCourse->language) : '' ?>',
                        url: '<?= $this->Url->build('/') ?><?= h($this->request->getParam('lang')) ?>/yoga-courses/<?=  !empty($moreCourse->country) ? h($moreCourse->country->name) : '' ?>/region/<?= !empty($moreCourse->state) ? h($moreCourse->state->name) : '' ?>/<?= !empty($moreCourse->city) ? h($moreCourse->city->name) : '' ?>/<?= !empty($moreCourse->slug) ? h($moreCourse->slug) : '' ?>'
                    },
                    <?php endforeach; ?>
                ],        
                currentSlideIndex: 1,
                isPaused: false,
                autoplayInterval: null,

                startX: 0,
                endX: 0,
                handleSwipeStart(event) {
                    this.startX = event.type.includes('mouse') ? event.clientX : event.touches[0].clientX;
                },
                handleSwipeEnd(event) {
                    this.endX = event.type.includes('mouse') ? event.clientX : event.changedTouches[0].clientX;
                    const diff = this.startX - this.endX;

                    if (Math.abs(diff) > 50) {
                        if (diff > 0) {
                            this.next();
                        } else {
                            this.previous();
                        }
                    }
                },

                previous() {                
                    if (this.currentSlideIndex > 1) {                    
                        this.currentSlideIndex = this.currentSlideIndex - 1                
                    } else {           
                        this.currentSlideIndex = this.slides.length                
                    }            
                },            
                next() {                
                    if (this.currentSlideIndex < this.slides.length) {                    
                        this.currentSlideIndex = this.currentSlideIndex + 1                
                    } else {                   
                        this.currentSlideIndex = 1                
                    }            
                },  
                autoplay() {
                    this.autoplayInterval = setInterval(() => {
                        if (! this.isPaused) {
                            this.next()
                        }
                    }, this.autoplayIntervalTime)
                },
                setAutoplayInterval(newIntervalTime) {
                    clearInterval(this.autoplayInterval)
                    this.autoplayIntervalTime = newIntervalTime
                    this.autoplay()
                },
                generateSeoLink(item) {
                    const parts = [baseUrl + lang + '/yoga-courses'];

                    if (item.country) {
                        parts.push(item.country.replace(/\s+/g, '-').toLowerCase());
                    }
                
                    if (item.state) {
                        parts.push('region', item.state.replace(/\s+/g, '-').toLowerCase());
                    }

                    if (item.city) {
                        parts.push(item.city.replace(/\s+/g, '-').toLowerCase());
                    }

                    if (item.slug) {
                        parts.push(item.slug);
                    }

                    return parts.join('/');
                }
            }" x-init="currentSlideIndex = 1" class="relative w-full featured-slider mobile-view">

                <!-- SLIDES -->
                <div class="card-container relative w-full mb-[20px] min-h-[634px] overflow-hidden"
                    @touchstart="handleSwipeStart" @touchend="handleSwipeEnd" @mousedown="handleSwipeStart"
                    @mouseup="handleSwipeEnd">
                    <div class="flex transition-transform duration-500 ease-linear" :style="`transform: translateX(-${(currentSlideIndex - 1) * 100}%);`">
                        <template x-for="(slide, index) in slides" :key="index">
                                <!-- for clickable -->
                                <a :href="generateSeoLink(slide)" class="link w-full flex-shrink-0">

                                <!-- <div x-show="currentSlideIndex == index + 1" class="absolute inset-0"
                                    x-transition.opacity.duration.1000ms> -->
                                    <!-- <img class="absolute w-full h-full inset-0 object-cover text-on-surface dark:text-on-surface-dark" x-bind:src="slide.imgSrc" x-bind:alt="slide.imgAlt" /> -->
                                    <div class="course-card bg-white rounded-lg h-auto">
                                        <img class="w-full h-56 object-cover rounded-md yoga-img" x-bind:src="slide.imgSrc"
                                        x-bind:alt="slide.imgAlt">
                                        <div class="card-body">
                                            <!-- <div class="card-star">
                                                <span class="star on"></span>
                                                <span class="star on"></span>
                                                <span class="star on"></span>
                                                <span class="star half"></span>
                                                <span class="star"></span>
                                            </div> -->
                                            <p class="info line-clamp-2 h-[40px]" x-text="slide.info"></p>
                                            <h3 class="text-xl font-semibold yoga-name line-clamp-2 h-[68px]">
                                                <span x-html="slide.title" class="line-clamp-2"></span>
                                                <span class='rating-wrapper'><span class='rating'>4.5</span> <i class='fas fa-star'></i></span>
                                            </h3>
                                            <p class="text-gray-600 mt-2 line-clamp-4 yoga-description line-clamp-4 h-[72px]" x-text="slide.desc"></p>
                                            <p class="time" x-html="`<i class='fas fa-calendar-alt'></i> `+slide.date"></p>
                                            <p class="mode" x-html="`<i class='fas fa-laptop-code'></i> `+slide.mode"></p>
                                            <p class="lang mt-2" x-html="`<i class='fas fa-globe'></i> `+slide.lang"></p>
                                            <!-- <button class="mt-4 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Learn More</button> -->
                                        </div>
                                    </div>
                                <!-- </div> -->
                            </a>
                        </template>
                    </div>
                </div>

                <!-- indicators -->
                <!-- <div class="indicator absolute rounded-radius bottom-3 md:bottom-5 left-1/2 z-20 flex -translate-x-1/2 gap-4 md:gap-3 bg-transparent px-1.5 py-1 md:px-2" role="group" aria-label="slides" >
                    <template x-for="(slide, index) in slides">
                        <button class="size-3 rounded-full transition bg-on-surface dark:bg-on-surface-dark" x-on:click="currentSlideIndex = index + 1" x-bind:class="[currentSlideIndex === index + 1 ? 'bg-[#D87A61]' : 'bg-[#D87A61]/40']" x-bind:aria-label="'slide ' + (index + 1)"></button>
                    </template>
                </div> -->
                <button type="button" @click="previous()" :disabled="currentSlideIndex === 1" class="absolute top-[20%] start-[10px] end-[0px] z-30 flex items-center justify-center px-0 cursor-pointer group focus:outline-none disabled:opacity-50 prev">
                    <img src="<?= $this->Url->webroot('img/arrow-back-black.png') ?>" alt="Arrow Back">
                </button>
                <button type="button" @click="next()" :disabled="currentSlideIndex === slides.length" class="absolute top-[20%] end-[10px] z-30 flex items-center justify-center px-0 cursor-pointer group focus:outline-none disabled:opacity-50 next">
                    <img src="<?= $this->Url->webroot('img/arrow-next-black.png') ?>" alt="Arrow Next">
                </button>
            </div>
        <?php else: ?>
            <div class="text-center py-8 mobile-view">
                <p>There are no other courses available from this center at the moment.</p>
            </div>
        <?php endif; ?>
    </div>
</section>
<section class="testimonial">

    <div class="px-6 md:px-10 lg:px-25 xl:pl-40 xl:pr-21 2xl:px-60">
        <h2>Testimonials</h2>
        <div x-data="testimonialCarousel()" x-init="init()" class="mx-auto relative">
            <div class="relative overflow-hidden">
                <div class="flex transition-transform duration-500 w-100"
                    :style="`transform: translateX(-${current * (100 / itemsPerView)}%); `" @touchstart="handleTouchStart" @touchend="handleTouchEnd">
                <template x-for="testimonial in testimonials" :key="testimonial.name">
                    <div class="w-full sm:w-1/2 flex-none p-2">
                        <div class="bg-transparent py-6 rounded-lg h-full">
                            <div class="flex items-center testi-wrapper">
                            <img :src="testimonial.image" alt="Avatar" class="rounded-full mr-4 testi-img">
                            <div class="testi-name-rating">
                                <p class="text-lg font-semibold" x-text="testimonial.name"></p>
                                <span class='rating'>4.5</span> <i class='fas fa-star'></i>
                            </div>
                            </div>
                            <p class="text-gray-700 testi-desc" x-text="testimonial.quote"></p>
                        </div>
                    </div>
                </template>
                </div>
            
                <!-- Dots -->
                <div class="mt-4 flex justify-center space-x-2 indicator desktop-view">
                    <template x-for="(testimonial, index) in testimonials.length - 1" :key="index">
                    <button @click="current = index"
                            :class="{'active': current === index, 'testimonial-dots': current !== index}"
                            class="w-3 h-3 rounded-full"></button>
                    </template>
                </div>
                <div class="mt-4 flex justify-center space-x-2 indicator mobile-view">
                    <template x-for="(testimonial, index) in testimonials.length" :key="index">
                    <button @click="current = index"
                            :class="{'active': current === index, 'testimonial-dots': current !== index}"
                            class="w-3 h-3 rounded-full"></button>
                    </template>
                </div>
            </div>
            <!-- Navigation Buttons -->
            <div class="absolute inset-y-0 left-[-40px] flex items-center nav-arrows desktop-view">
                <button @click="prev" :disabled="current <= 0" :class="{
                'opacity-50 cursor-not-allowed': current <= 0
                }" class="text-gray-500 hover:text-gray-800 p-2 bg-white rounded-full shadow btn-left"><img src="../../../../../../img/arrow-back-black.png" alt="left arrow" /></button>
            </div>
            <div class="absolute inset-y-0 right-[-20px] flex items-center nav-arrows desktop-view">
                <button @click="next" :disabled="current >= testimonials.length - 2" :class="{
                'opacity-50 cursor-not-allowed': current >= testimonials.length - 2
                }" class="text-gray-500 hover:text-gray-800 p-2 bg-white rounded-full shadow btn-right"><img src="../../../../../../img/arrow-next-black.png" alt="right arrow" /></button>
            </div>
            <div class="absolute inset-y-0 left-[-20px] flex items-center nav-arrows mobile-view">
                <button @click="prev" :disabled="current <= 0" :class="{
                'opacity-50 cursor-not-allowed': current <= 0
                }" class="text-gray-500 hover:text-gray-800 p-2 bg-white rounded-full shadow btn-left"><img src="../../../../../../img/arrow-back-black.png" alt="left arrow" /></button>
            </div>
            <div class="absolute inset-y-0 right-[-20px] flex items-center nav-arrows mobile-view">
                <button @click="next" :disabled="current >= testimonials.length - 1" :class="{
                'opacity-50 cursor-not-allowed': current >= testimonials.length - 1
                }" class="text-gray-500 hover:text-gray-800 p-2 bg-white rounded-full shadow btn-right"><img src="../../../../../../img/arrow-next-black.png" alt="right arrow" /></button>
            </div>
        </div>
    </div>
</section>
<script>
    let featuredSliderContainer = document.getElementById("featured-carousel");
    let featuredSlider;
    let featuredCards;
    let featuredElementToShow = 4;
    let featuredCardWidth;
    // let autoSlideInterval1;

    if (featuredSliderContainer) {
        featuredSlider = document.getElementById("featured-slider");
        featuredCards = document.querySelectorAll(".featured-center-carousel");

        if(document.body.clientWidth < 768){
            featuredElementToShow = 1;
        }
        else if(document.body.clientWidth < 992){
            featuredElementToShow = 2;
        } 

        let featuredSliderContainerWidth = featuredSliderContainer.clientWidth;
        featuredCardWidth = featuredSliderContainerWidth / featuredElementToShow;
        featuredSlider.style.width = featuredCards.length * featuredCardWidth + "px";
        featuredSlider.style.transition = "margin";
        featuredSlider.style.transitionDuration = "1s";
        
        for (let i = 0; i < featuredCards.length; i++) {
            featuredCards[i].style.width = featuredCardWidth + "px";
        }

        let currentIndex = 0;

        const maxIndex = featuredCards.length - featuredElementToShow;
        console.log(featuredCards.length, featuredElementToShow);
        // function featuredAutoSlide() {
        //     autoSlideInterval1 = setInterval(() => {
        //         currentIndex = (currentIndex >= maxIndex) ? 0 : currentIndex + 1;
        //         featuredSlider.style.marginLeft = "-" + (featuredCardWidth * currentIndex) + "px";
        //     }, 3000);
        // }

        function featuredAutoSlide() {
            //autoSlideInterval1 = setInterval(() => {
                if (currentIndex >= maxIndex) {
                    // Instantly reset without animation
                    featuredSlider.style.transition = 'none';
                    currentIndex = 0;
                    featuredSlider.style.marginLeft = "0px";

                    // Force reflow to apply transition again
                    void featuredSlider.offsetWidth;

                    // Re-enable transition
                    featuredSlider.style.transition = 'margin-left 0.5s ease';
                } else {
                    currentIndex++;
                    featuredSlider.style.transition = 'margin-left 0.5s ease';
                    featuredSlider.style.marginLeft = "-" + (featuredCardWidth * currentIndex) + "px";
                }
            //}, 3000);
        }
        featuredAutoSlide();
    }

    function featuredPrev() {
        if (+featuredSlider.style.marginLeft.slice(0, -2) !== 0) {
            featuredSlider.style.marginLeft = ((+featuredSlider.style.marginLeft.slice(0, -2)) + featuredCardWidth) + 'px';
        }
    }

    function featuredNext() {
        if (+featuredSlider.style.marginLeft.slice(0, -2) !== -featuredCardWidth * (featuredCards.length - featuredElementToShow)) {
            featuredSlider.style.marginLeft = ((+featuredSlider.style.marginLeft.slice(0, -2)) - featuredCardWidth) + 'px';
        }
    }
</script>



