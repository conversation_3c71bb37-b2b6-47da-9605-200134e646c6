<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Coupon $coupon
 */
?>
<?php $this->append('style'); ?>
<style>
.nav-tabs {
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 0;
}
.nav-tabs .nav-link {
    border: none;
    border-bottom: 2px solid transparent;
    color: #6c757d;
    padding: 12px 24px;
    font-weight: 500;
}
.nav-tabs .nav-link.active {
    background: transparent;
    border-bottom: 2px solid #007bff;
    color: #007bff;
    font-weight: 600;
}
.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: #007bff;
}
.tab-content {
    padding: 24px 0;
    border: none;
}
.info-row {
    display: flex;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
}
.info-row:last-child {
    border-bottom: none;
}
.info-label {
    font-weight: 600;
    color: #333;
    width: 200px;
    flex-shrink: 0;
    font-size: 14px;
}
.info-value {
    color: #666;
    flex: 1;
    font-size: 14px;
}
.badge {
    font-size: 12px;
    padding: 4px 8px;
}
</style>
<?php $this->end(); ?>

<div class="main-content bg-container">
    <div class="row">
        <div class="col-12 col-xl-12">
            <div class="card card-primary">
                <div class="d-flex justify-content-between align-items-center">
                    <ul class="breadcrumb breadcrumb-style m-0">
                        <li class="breadcrumb-item">Marketing</li>
                        <li class="breadcrumb-item">
                            <a href="<?= $this->Url->build(['action' => 'index']) ?>">Coupons</a>
                        </li>
                        <li class="breadcrumb-item">View Coupon</li>
                    </ul>

                    <a href="javascript:void(0);" class="d-flex align-items-center category-back-button breadcrumb m-0" id="back-button-mo" onclick="goBackToCoupons();">
                        <span class="rotate me-2">⤣</span>
                        <small class="fw-bold"><?= __("BACK") ?></small>
                    </a>
                </div>

                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center w-100">
                        <h4>Coupon Details: <?= h($coupon->code) ?></h4>
                        <!-- <div class="d-flex gap-2">
                            <a href="<?= $this->Url->build(['action' => 'edit', $coupon->id]) ?>" class="btn btn-primary btn-sm">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            <a href="<?= $this->Url->build(['action' => 'index']) ?>" class="btn btn-secondary btn-sm">
                                <i class="fas fa-list"></i> Back to List
                            </a>
                        </div> -->
                    </div>
                </div>
                <div class="card-body">
                    <!-- Status Alert -->
                    <div class="alert alert-<?= $coupon->is_active ? 'success' : 'warning' ?> mb-4">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-<?= $coupon->is_active ? 'check-circle' : 'exclamation-triangle' ?> me-2"></i>
                            <div>
                                <strong>Status: <?= h($coupon->status) ?></strong>
                                <?php if (!$coupon->is_active && $coupon->status === 'Active'): ?>
                                    <br><small>This coupon is not currently active due to date restrictions.</small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <ul class="nav nav-tabs" id="couponViewTab" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="basic-info-tab" data-bs-toggle="tab" href="#basic-info" role="tab" aria-controls="basic-info" aria-selected="true">
                                Basic Information
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="details-tab" data-bs-toggle="tab" href="#details" role="tab" aria-controls="details" aria-selected="false">
                                Details
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="usage-tab" data-bs-toggle="tab" href="#usage" role="tab" aria-controls="usage" aria-selected="false">
                                Usage & Validity
                            </a>
                        </li>
                    </ul>
                    <div class="tab-content" id="couponViewTabContent">

                        <!-- Basic Information Tab -->
                        <div class="tab-pane fade show active" id="basic-info" role="tabpanel" aria-labelledby="basic-info-tab">
                            <div class="info-row">
                                <div class="info-label">Id</div>
                                <div class="info-value"><?= $this->Number->format($coupon->id) ?></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Code</div>
                                <div class="info-value"><code><?= h($coupon->code) ?></code></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Title</div>
                                <div class="info-value"><?= h($coupon->title) ?: '<em class="text-muted">No title</em>' ?></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Description</div>
                                <div class="info-value"><?= h($coupon->description) ?: '<em class="text-muted">No description</em>' ?></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Status</div>
                                <div class="info-value">
                                    <span class="badge <?= $coupon->status_badge_class ?>">
                                        <?= h($coupon->status) ?>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Details Tab -->
                        <div class="tab-pane fade" id="details" role="tabpanel" aria-labelledby="details-tab">
                            <div class="info-row">
                                <div class="info-label">Discount Type</div>
                                <div class="info-value">
                                    <span class="badge bg-<?= $coupon->discount_type === 'percentage' ? 'success' : 'primary' ?>">
                                        <?= ucfirst(h($coupon->discount_type)) ?>
                                    </span>
                                </div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Discount Value</div>
                                <div class="info-value"><strong><?= $coupon->formatted_discount ?></strong></div>
                            </div>
                            <?php if ($coupon->min_cart_value): ?>
                            <div class="info-row">
                                <div class="info-label">Minimum Booking Value</div>
                                <div class="info-value"><?= $this->Number->currency($coupon->min_cart_value) ?></div>
                            </div>
                            <?php endif; ?>
                            <div class="info-row">
                                <div class="info-label">Created</div>
                                <div class="info-value"><?= $coupon->created_at->format('M d, Y H:i:s') ?></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Last Updated</div>
                                <div class="info-value"><?= $coupon->updated_at->format('M d, Y H:i:s') ?></div>
                            </div>
                        </div>

                        <!-- Usage & Validity Tab -->
                        <div class="tab-pane fade" id="usage" role="tabpanel" aria-labelledby="usage-tab">
                            <div class="info-row">
                                <div class="info-label">Total Usage Limit</div>
                                <div class="info-value">
                                    <?php if ($coupon->usage_limit): ?>
                                        <?= $this->Number->format($coupon->usage_limit) ?>
                                    <?php else: ?>
                                        <span class="text-success">Unlimited</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Per User Limit</div>
                                <div class="info-value">
                                    <?php if ($coupon->per_user_limit): ?>
                                        <?= $this->Number->format($coupon->per_user_limit) ?>
                                    <?php else: ?>
                                        <span class="text-success">Unlimited</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Valid From</div>
                                <div class="info-value">
                                    <?php if ($coupon->start_date): ?>
                                        <?= $coupon->start_date->format('M d, Y H:i:s') ?>
                                        <?php if ($coupon->start_date > new DateTime()): ?>
                                            <span class="badge bg-warning ms-2">Not yet active</span>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="text-muted">No start date limit</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Valid Until</div>
                                <div class="info-value">
                                    <?php if ($coupon->end_date): ?>
                                        <?= $coupon->end_date->format('M d, Y H:i:s') ?>
                                        <?php if ($coupon->end_date < new DateTime()): ?>
                                            <span class="badge bg-danger ms-2">Expired</span>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="text-muted">No expiry date</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $this->append('script'); ?>
<script>
function goBackToCoupons() {
    // Try to get the referrer URL first
    if (document.referrer && document.referrer.includes('/admin/coupons') && !document.referrer.includes('/edit') && !document.referrer.includes('/view')) {
        window.location.href = document.referrer;
    } else {
        // Fallback to coupons index
        window.location.href = '/admin/coupons';
    }
}
</script>
<?php $this->end(); ?>
