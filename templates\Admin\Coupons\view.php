<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Coupon $coupon
 */
?>
<?php $this->append('style'); ?>
<style>
.nav-tabs .nav-link.active {
    background: #fff;
    border-bottom: 2px solid #007bff;
    color: #007bff;
}
.tab-content {
    padding-top: 1.5rem;
}
.info-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #e9ecef;
}
.info-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}
.info-value {
    color: #212529;
    font-size: 1rem;
    padding: 0.5rem;
    background: white;
    border-radius: 4px;
    border: 1px solid #ced4da;
    min-height: 38px;
    display: flex;
    align-items: center;
}
.row.g-3 .col-md-6 {
    margin-bottom: 1rem;
}
</style>
<?php $this->end(); ?>

<div class="main-content bg-container">
    <div class="row">
        <div class="col-12 col-xl-12">
            <div class="card card-primary">
                <div class="d-flex justify-content-between align-items-center">
                    <ul class="breadcrumb breadcrumb-style m-0">
                        <li class="breadcrumb-item">Marketing</li>
                        <li class="breadcrumb-item">
                            <a href="<?= $this->Url->build(['action' => 'index']) ?>">Coupons</a>
                        </li>
                        <li class="breadcrumb-item">View Coupon</li>
                    </ul>

                    <a href="javascript:void(0);" class="d-flex align-items-center category-back-button breadcrumb m-0" id="back-button-mo" onclick="goBackToCoupons();">
                        <span class="rotate me-2">⤣</span>
                        <small class="fw-bold"><?= __("BACK") ?></small>
                    </a>
                </div>

                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center w-100">
                        <h4>Coupon Details: <?= h($coupon->code) ?></h4>
                        <!-- <div class="d-flex gap-2">
                            <a href="<?= $this->Url->build(['action' => 'edit', $coupon->id]) ?>" class="btn btn-primary btn-sm">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            <a href="<?= $this->Url->build(['action' => 'index']) ?>" class="btn btn-secondary btn-sm">
                                <i class="fas fa-list"></i> Back to List
                            </a>
                        </div> -->
                    </div>
                </div>
                <div class="card-body">
                    <!-- Status Alert -->
                    <div class="alert alert-<?= $coupon->is_active ? 'success' : 'warning' ?> mb-4">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-<?= $coupon->is_active ? 'check-circle' : 'exclamation-triangle' ?> me-2"></i>
                            <div>
                                <strong>Status: <?= h($coupon->status) ?></strong>
                                <?php if (!$coupon->is_active && $coupon->status === 'Active'): ?>
                                    <br><small>This coupon is not currently active due to date restrictions.</small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <ul class="nav nav-tabs" id="couponViewTab" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="overview-tab" data-bs-toggle="tab" href="#overview" role="tab" aria-controls="overview" aria-selected="true">
                                Overview
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="settings-tab" data-bs-toggle="tab" href="#settings" role="tab" aria-controls="settings" aria-selected="false">
                                Settings
                            </a>
                        </li>
                        <!-- <li class="nav-item">
                            <a class="nav-link" id="statistics-tab" data-bs-toggle="tab" href="#statistics" role="tab" aria-controls="statistics" aria-selected="false">
                                Statistics
                            </a>
                        </li> -->
                    </ul>
                    <div class="tab-content p-3 border border-top-0" id="couponViewTabContent">

                        <!-- Overview Tab -->
                        <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="info-card">
                                        <div class="info-label">Coupon Code</div>
                                        <div class="info-value">
                                            <code class="bg-light p-2 rounded fs-5"><?= h($coupon->code) ?></code>
                                        </div>
                                    </div>
                                    <div class="info-card">
                                        <div class="info-label">Title</div>
                                        <div class="info-value"><?= h($coupon->title) ?: '<em class="text-muted">No title</em>' ?></div>
                                    </div>
                                    <div class="info-card">
                                        <div class="info-label">Status</div>
                                        <div class="info-value">
                                            <span class="badge <?= $coupon->status_badge_class ?> fs-6">
                                                <?= h($coupon->status) ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-card">
                                        <div class="info-label">Discount</div>
                                        <div class="info-value">
                                            <span class="text-success fs-4 fw-bold"><?= $coupon->formatted_discount ?></span>
                                            <span class="badge bg-<?= $coupon->discount_type === 'percentage' ? 'success' : 'primary' ?> ms-2">
                                                <?= ucfirst(h($coupon->discount_type)) ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="info-card">
                                        <div class="info-label">Created</div>
                                        <div class="info-value"><?= $coupon->created_at->format('M d, Y H:i:s') ?></div>
                                    </div>
                                    <div class="info-card">
                                        <div class="info-label">Last Updated</div>
                                        <div class="info-value"><?= $coupon->updated_at->format('M d, Y H:i:s') ?></div>
                                    </div>
                                </div>
                                <?php if ($coupon->description): ?>
                                <div class="col-12">
                                    <div class="info-card">
                                        <div class="info-label">Description</div>
                                        <div class="info-value"><?= $this->Text->autoParagraph(h($coupon->description)) ?></div>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Settings Tab -->
                        <div class="tab-pane fade" id="settings" role="tabpanel" aria-labelledby="settings-tab">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <h6 class="fw-bold text-dark mb-3">Discount Configuration</h6>
                                    <?php if ($coupon->currency_code): ?>
                                    <!-- <div class="info-card">
                                        <div class="info-label">Currency</div>
                                        <div class="info-value"><?= h($coupon->currency_code) ?></div>
                                    </div> -->
                                    <?php endif; ?>
                                    <?php if ($coupon->min_cart_value): ?>
                                    <div class="info-card">
                                        <div class="info-label">Minimum booking Value</div>
                                        <div class="info-value"><?= $this->Number->currency($coupon->min_cart_value, $coupon->currency_code) ?></div>
                                    </div>
                                    <?php endif; ?>
                                    <?php if ($coupon->max_discount_value): ?>
                                    <!-- <div class="info-card">
                                        <div class="info-label">Maximum Discount</div>
                                        <div class="info-value"><?= $this->Number->currency($coupon->max_discount_value, $coupon->currency_code) ?></div>
                                    </div> -->
                                    <?php endif; ?>
                                     <div class="info-card">
                                        <div class="info-label">Valid From</div>
                                        <div class="info-value">
                                            <?php if ($coupon->start_date): ?>
                                                <?= $coupon->start_date->format('M d, Y H:i:s') ?>
                                                <?php if ($coupon->start_date > new DateTime()): ?>
                                                    <br><small class="text-warning">Not yet active</small>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-muted">No start date limit</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="info-card">
                                        <div class="info-label">Valid Until</div>
                                        <div class="info-value">
                                            <?php if ($coupon->end_date): ?>
                                                <?= $coupon->end_date->format('M d, Y H:i:s') ?>
                                                <?php if ($coupon->end_date < new DateTime()): ?>
                                                    <br><small class="text-danger">Expired</small>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-muted">No expiry date</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="fw-bold text-dark mb-3">Usage & Validity</h6>
                                    <div class="info-card">
                                        <div class="info-label">Total Usage Limit</div>
                                        <div class="info-value">
                                            <?php if ($coupon->usage_limit): ?>
                                                <?= $this->Number->format($coupon->usage_limit) ?>
                                                <small class="text-muted">(Remaining: <?= $coupon->remaining_usage ?? 'N/A' ?>)</small>
                                            <?php else: ?>
                                                <span class="text-success">Unlimited</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="info-card">
                                        <div class="info-label">Per User Limit</div>
                                        <div class="info-value">
                                            <?php if ($coupon->per_user_limit): ?>
                                                <?= $this->Number->format($coupon->per_user_limit) ?>
                                            <?php else: ?>
                                                <span class="text-success">Unlimited</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                   
                                    <?php if ($coupon->start_date && $coupon->end_date): ?>
                                    <div class="info-card">
                                        <div class="info-label">Duration</div>
                                        <div class="info-value">
                                            <?php
                                            $duration = $coupon->start_date->diff($coupon->end_date);
                                            echo $duration->days . ' days';
                                            ?>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Statistics Tab -->
                        <!-- <div class="tab-pane fade" id="statistics" role="tabpanel" aria-labelledby="statistics-tab">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <div class="info-card text-center">
                                        <h4 class="text-primary mb-1">0</h4>
                                        <div class="info-label">Total Uses</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-card text-center">
                                        <h4 class="text-success mb-1">$0.00</h4>
                                        <div class="info-label">Total Discount Given</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-card text-center">
                                        <h4 class="text-info mb-1">0</h4>
                                        <div class="info-label">Unique Users</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-card text-center">
                                        <h4 class="text-warning mb-1">$0.00</h4>
                                        <div class="info-label">Average Order Value</div>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="info-card text-center">
                                        <small class="text-muted">Usage statistics will be available when the coupon tracking system is implemented.</small>
                                    </div>
                                </div>
                            </div>
                        </div> -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $this->append('script'); ?>
<script>
function goBackToCoupons() {
    // Try to get the referrer URL first
    if (document.referrer && document.referrer.includes('/admin/coupons') && !document.referrer.includes('/edit') && !document.referrer.includes('/view')) {
        window.location.href = document.referrer;
    } else {
        // Fallback to coupons index
        window.location.href = '/admin/coupons';
    }
}
</script>
<?php $this->end(); ?>
