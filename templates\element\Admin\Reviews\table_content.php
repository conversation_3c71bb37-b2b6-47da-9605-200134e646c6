<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Review> $reviews
 * @var array $pagination
 */
?>

<div class="table-responsive">
    <table class="table table-striped dataTable no-footer display nowrap table-hover border" id="review-table">
        <thead>
            <tr>
                <th class="no-sort"><?= __("Actions") ?></th>
                <th><?= __("ID") ?></th>
                <th><?= __("Customer ID") ?></th>
                <th><?= __("Type") ?></th>
                <th><?= __("Rating") ?></th>
                <th><?= __("Comment") ?></th>
                <th><?= __("Status") ?></th>
                <th><?= __("Created") ?></th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($reviews as $review): ?>
            <tr>
                <td class="no-sort">
                    <a href="<?= $this->Url->build(['action' => 'view', $review->id]) ?>"
                       class="" title="View">
                        <i class="fas fa-eye"></i>
                    </a>
                    <a href="<?= $this->Url->build(['action' => 'edit', $review->id]) ?>"
                       class="" title="Edit">
                        <i class="fas fa-edit"></i>
                    </a>
                </td>
                <td><?= $this->Number->format($review->id) ?></td>
                <td><?= $this->Number->format($review->customer_id) ?></td>
                <td>
                    <?php
                    $typeLabels = [
                        'course' => 'Course Review',
                        'teacher' => 'Teacher Review',
                        'center' => 'Center Review'
                    ];
                    ?>
                    <span class="badge badge-info">
                        <?= h($typeLabels[$review->review_type] ?? $review->review_type) ?>
                    </span>
                </td>
                <td>
                    <div class="rating-stars">
                        <?php for ($i = 1; $i <= 5; $i++): ?>
                            <i class="fas fa-star <?= $i <= $review->rating ? 'text-warning' : 'text-muted' ?>"></i>
                        <?php endfor; ?>
                        <span class="ms-1">(<?= h($review->rating) ?>)</span>
                    </div>
                </td>
                <td>
                    <div class="comment-preview">
                        <?= h(strlen($review->comment) > 50 ? substr($review->comment, 0, 50) . '...' : $review->comment) ?>
                    </div>
                </td>
                <td>
                    <?php
                    $statusClass = match($review->status) {
                        'Approved' => 'success',
                        'Rejected' => 'danger',
                        'Pending' => 'warning',
                        default => 'secondary'
                    };
                    ?>
                    <span class="badge badge-<?= $statusClass ?>">
                        <?= h($review->status) ?>
                    </span>
                </td>
                <td><?= h($review->created_at->format('M d, Y H:i')) ?></td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>

<!-- Pagination -->
<div class="d-flex justify-content-between align-items-center mt-3">
    <div class="pagination-info">
        <?= $this->Paginator->counter(__('Showing {{start}} to {{end}} of {{count}} entries')) ?>
    </div>

    <nav aria-label="Reviews pagination">
        <ul class="pagination pagination-sm mb-0">
            <!-- Previous Page -->
            <?= $this->Paginator->prev('<i class="fas fa-chevron-left"></i>', [
                'escape' => false,
                'class' => 'page-link'
            ], null, [
                'class' => 'page-item disabled',
                'escape' => false,
                'disabledTag' => 'span',
                'disabledClass' => 'page-link'
            ]) ?>

            <!-- Page Numbers -->
            <?= $this->Paginator->numbers([
                'before' => '',
                'after' => '',
                'class' => 'page-link',
                'currentClass' => 'page-item active',
                'currentTag' => 'span'
            ]) ?>

            <!-- Next Page -->
            <?= $this->Paginator->next('<i class="fas fa-chevron-right"></i>', [
                'escape' => false,
                'class' => 'page-link'
            ], null, [
                'class' => 'page-item disabled',
                'escape' => false,
                'disabledTag' => 'span',
                'disabledClass' => 'page-link'
            ]) ?>
        </ul>
    </nav>
</div>

<style>
.rating-stars .fa-star {
    font-size: 14px;
}

.comment-preview {
    max-width: 200px;
    word-wrap: break-word;
}

.badge {
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 12px;
}

.badge-info { background: #17a2b8; color: white; }
.badge-success { background: #28a745; color: white; }
.badge-warning { background: #ffc107; color: #212529; }
.badge-danger { background: #dc3545; color: white; }
.badge-secondary { background: #6c757d; color: white; }
</style>
