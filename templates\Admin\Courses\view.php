<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Offer $offer
 */

use Cake\I18n\FrozenTime;
?>
<?php $this->append('style'); ?>
<!DOCTYPE html>
<html lang="en">
<?php $this->end(); ?>

<div class="main-content bg-container">
    <div class="row">
        <div class="col-12 col-xl-12">
            <div class="card card-primary">
                <div class="d-flex justify-content-between align-items-center">
                    <ul class="breadcrumb breadcrumb-style m-0">
                        <li class="breadcrumb-item">Partner Offerings</li>
                        <li class="breadcrumb-item">
                            <a href="<?= $this->Url->build(['controller' => 'Courses', 'action' => 'index']) ?>">Partner Offerings</a>
                        </li>
                        <li class="breadcrumb-item">View Partner Offering</li>
                    </ul>
                    <a href="javascript:void(0);" class="d-flex align-items-center course-back-button  breadcrumb m-0" id="back-button-mo" onclick="history.back();">
                        <span class="rotate me-2">⤣</span>
                        <small class="fw-bold" data-translate="back"><?= __("BACK") ?></small>
                    </a>
                </div>

                <div class="card-header">
                    <h4>View Partner Offering</h4>
                </div>
                <div class="card-body">
                    <ul class="nav nav-tabs" id="courseTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="basic-tab" data-bs-toggle="tab" href="#basic" role="tab">Basic Information</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="details-tab" data-bs-toggle="tab" href="#details" role="tab">Details </a>
                        </li>
                         <li class="nav-item">
                            <a class="nav-link" id="teachers-tab" data-bs-toggle="tab" href="#teachers" role="tab">Teachers </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link" id="batches-tab" data-bs-toggle="tab" href="#batches" role="tab">Schedule & Bacthes</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="pricing-tab" data-bs-toggle="tab" href="#pricing" role="tab">Pricing</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="media-tab" data-bs-toggle="tab" href="#media" role="tab"> Media</a>
                        </li>
                        <!-- <li class="nav-item">
                            <a class="nav-link" id="review-tab" data-bs-toggle="tab" href="#review" role="tab">Review & Rating</a>
                        </li> -->
                        <li class="nav-item">
                            <a class="nav-link" id="seo-tab" data-bs-toggle="tab" href="#seo" role="tab">SEO</a>
                        </li>
                         <li class="nav-item">
                            <a class="nav-link" id="review-tab" data-bs-toggle="tab" href="#review" role="tab">Review & Rating</a>
                        </li>
                    </ul>
                    <div class="tab-content p-3 border border-top-0" id="courseTabContent">
                        <!-- Basic Info -->
                        <div class="tab-pane fade show active" id="basic" role="tabpanel">
                            <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label">Id</label>
                                <div class="col-sm-9 main-field">
                                    <?= h($course->id) ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label">Name</label>
                                <div class="col-sm-9 main-field">
                                    <?= h($course->name) ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label">Slug</label>
                                <div class="col-sm-9 main-field">
                                    <?= h($course->slug) ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label"> Type</label>
                                <div class="col-sm-9 main-field">
                                    <?= h($course->course_type->name) ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label">Offered By</label>
                                <div class="col-sm-9 main-field">
                                    <?= h($course->partner->name) ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label">Short Description</label>
                                <div class="col-sm-9 main-field">
                                    <?= h($course->short_description) ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label">Language</label>
                                <div class="col-sm-9 main-field">
                                    <?= h($course->language) ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label">Yoga Styles</label>
                                <div class="col-sm-9 main-field">
                                    <?php if (!empty($course->yoga_style_master_data)): ?>
                                        <?php
                                        $styleNames = [];
                                        foreach ($course->yoga_style_master_data as $style) {
                                            $styleNames[] = h($style->title);
                                        }
                                        echo !empty($styleNames) ? implode(', ', $styleNames) : 'N/A';
                                        ?>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label">Special Needs</label>
                                <div class="col-sm-9 main-field">
                                    <?= h(!empty($specialNeeds) ? implode(', ', $specialNeeds) : 'N/A') ?>

                                </div>
                            </div>
                      
                            <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label">Techniques</label>
                                <div class="col-sm-9 main-field">
                                    <?= $techNames ? implode(', ', $techNames) : 'N/A' ?>
                                </div>
                            </div>
                             <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label">Levels</label>
                                <div class="col-sm-9 main-field">
                                    <?= $levelNames ? implode(', ', $levelNames) : '' ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label">Modality</label>
                                <div class="col-sm-9 main-field">
                                    <?= $modeNames ? implode(', ', $modeNames) : '' ?>
                                </div>
                            </div>
                           
                            <!-- Country -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("Country") ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= !empty($course->country) ? h($course->country->name) : '' ?>
                                </div>
                            </div>

                            <!-- State -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("State") ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= !empty($course->state) ? h($course->state->name) : '' ?>
                                </div>
                            </div>

                            <!-- City -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("City") ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= !empty($course->city) ? h($course->city->name) : '' ?>
                                </div>
                            </div>

                            <!-- Locality -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label"><?= __("Locality") ?></label>
                                <div class="col-sm-9 main-field">
                                    <?= !empty($course->locality) ? h($course->locality->name) : '' ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label"> Address</label>
                                <div class="col-sm-9 main-field">   
                                    <?= h($course->address) ?? '-' ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label"> Latitude</label>
                                <div class="col-sm-2 main-field">   
                                    <?= h($course->latitude) ?>
                                </div>
                                <label for="first_name" class="col-sm-2 col-form-label"> Longitude</label>
                                <div class="col-sm-2 main-field">   
                                    <?= h($course->longitude) ?>
                                </div>
                            </div>
                        
                            <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label">Women Only</label>
                                <div class="col-sm-2 main-field">
                                    <?= h($course->women_only == 1 ? __('Yes') : __('No')) ?>
                                </div>
                                <label for="first_name" class="col-sm-2 col-form-label">Is Featured</label>
                                <div class="col-sm-2 main-field">
                                    <?= h($course->is_featured == 1 ? __('Yes') : __('No')) ?>
                                </div>
                            </div>
                            
                            <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label">Accommodation Included</label>
                                <div class="col-sm-9 main-field">
                                    <?= h($course->is_accommodation_included == 1 ? __('Yes') : __('No')) ?>
                                </div>
                                 <label for="first_name" class="col-sm-2 col-form-label">Food Included</label>
                                <div class="col-sm-9 main-field">
                                    <?= h($course->is_food_included == 1 ? __('Yes') : __('No')) ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label">Accommodation Options</label>
                                <div class="col-sm-9 main-field">
                                    <?= h($course->is_accommodation_included == 1 ?  $course->accommodation_options  : __('No')) ?>
                                </div>
                                 <label for="first_name" class="col-sm-2 col-form-label">Food Included</label>
                                <div class="col-sm-9 main-field">
                                    <?= h($course->is_food_included == 1 ?$course->food_options : __('No')) ?>
                                </div>
                            </div>
                        
                            <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label">Has International Certification</label>
                                <div class="col-sm-9 main-field">
                                    <?= h($course->has_certification == 1 ? __('Yes') : __('No')) ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="image" class="col-sm-2 col-form-label"><?= __("Banner Image") ?></label>
                                <div class="col-sm-5 main-field">
                                    <?php
                                    if (!empty($course->banner_image)) :
                                        $pathUrl = $media->displayImage($course->banner_image); // Get image path from MediaComponent
                                        $imageUrl = $this->Url->webroot($pathUrl); // Convert to full URL
                                    ?>
                                        <img src="<?= h($imageUrl) ?>" alt="<?= __("Banner Image") ?>" style="max-width: 100px; max-height: 100px;">
                                    <?php else : ?>
                                        <li><?= __("No Banner Image Available") ?></li>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="name" class="col-sm-2 col-form-label"><?= __("Status") ?></label>
                                <div class="col-sm-1 main-field  view-label">
                                    <?php
                                    $statusMap = [
                                        'A' => ['label' => __('Active'), 'class' => 'badge-outline col-green'],
                                        'I' => ['label' => __('Inactive'), 'class' => 'badge-outline col-red'],
                                        'D' => ['label' => __('Deleted'), 'class' => 'badge-outline col-red']
                                    ];
                                    $status = $statusMap[$course->status] ?? ['label' => __('Unknown'), 'class' => 'col-red'];
                                    ?>
                                    <span>
                                        <?= h($status['label']) ?>
                                    </span>
                                </div>
                            </div>
                          
                        </div>
                        <div class="tab-pane fade" id="details" role="tabpanel">
                             <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label">Description 1 Label</label>
                                <div class="col-sm-9 main-field">
                                    <?php echo !empty($course->desc1_label) ? ($course->desc1_label) : '-'; ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label">Description 1 Text</label>
                                <div class="col-sm-9 main-field">
                                    <?php echo !empty($course->desc1_text) ? ($course->desc1_text) : '-'; ?>
                                </div>
                            </div>
                             <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label">Description 2 Label</label>
                                <div class="col-sm-9 main-field">
                                    <?php echo !empty($course->desc2_label) ? ($course->desc2_label) : '-'; ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label">Description 2 Text</label>
                                <div class="col-sm-9 main-field">
                                    <?php echo !empty($course->desc2_text) ? ($course->desc2_text) : '-'; ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label">Description 3 Label</label>
                                <div class="col-sm-9 main-field">
                                    <?php echo !empty($course->desc3_label) ? ($course->desc3_label) : '-'; ?>
                                </div>
                            </div>
                             <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label">Description 3 Text</label>
                                <div class="col-sm-9 main-field">
                                    <?php echo !empty($course->desc3_text) ? ($course->desc3_text) : '-'; ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label">FAQ</label>
                                <div class="col-sm-9 main-field">
                                    <?php echo !empty($course->faq) ? ($course->faq) : '-'; ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="first_name" class="col-sm-2 col-form-label">Refund Policy</label>
                                <div class="col-sm-9 main-field">
                                    <?php echo !empty($course->refund_policy) ? ($course->refund_policy) : '-'; ?>
                                </div>
                            </div>
                        </div>
                   
                        <div class="tab-pane fade" id="teachers" role="tabpanel">
                        <?php if (!empty($course->teachers)): ?>
                            <div class="table-responsive">
                                <table class="table table-striped align-middle">
                                    <thead class="table-primary">
                                        <tr>
                                            <th>#</th>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th>Phone</th>
                                            <th>Short Description</th>
                                            <th>Full Description</th>
                                            <th>Image</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($course->teachers as $index => $cteacher):
                                      
                                                $name  = $cteacher->name;
                                                $email = $cteacher->email;
                                                $phone = $cteacher->phone;
                                                $short_desc = $cteacher->short_desc;
                                                $description= $cteacher->description;
                                                $image = 'uploads/teachers/'.$cteacher->image; 
                                                if(!empty($cteacher->partner)){
                                                    $name  = $cteacher->partner->name;
                                                    $email = $cteacher->partner->email;
                                                    $phone = $cteacher->partner->phone;
                                                    $short_desc = $cteacher->partner->short_desc;
                                                    $description= $cteacher->partner->description;
                                                    $image = 'uploads/partners/logo/'.$cteacher->partner->logo; 
                                                }
                                            
                                            ?>
                                            <tr>
                                                <td><?= $index + 1 ?></td>
                                                <td><?= h($name) ?></td>
                                                <td><?= h($email) ?></td>
                                                <td><?= h($phone) ?></td>
                                                <td><?= h($short_desc) ?></td>
                                                <td>  <a href="#"
                                                    class="view-desc-btn"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#descModal"
                                                    data-id="<?= $cteacher->id ?>">
                                                    View
                                                    </a>
                                                      <!-- Hidden element with HTML -->
                                                    <div id="desc-html-<?= $cteacher->id ?>" style="display: none;">
                                                        <?= $description ?>
                                                    </div>
                                                <td>
                                                    <?php if (!empty($image)): ?>
                                                        <img src="<?= $this->Url->webroot($image) ?>" alt="<?=  h($name) ?>" class="preview-img">
                                                    <?php else: ?>
                                                        <span class="text-muted">No Image</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                                <div class="modal fade" id="descModal" tabindex="-1" aria-labelledby="descModalLabel" aria-hidden="true">
                                <div class="modal-dialog modal-md">
                                    <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="descModalLabel">Description</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div id="descModalBody" class="mb-0"></div>
                                    </div>
                                    </div>
                                </div>
                                </div>
                            </div>
                            <?php else: ?>
                                <p class="text-muted">No teachers available for this course.</p>
                            <?php endif; ?>
                        </div>
                     
                        <div class="tab-pane fade" id="batches" role="tabpanel">
                           <?php if (!empty($course->course_batches)): ?>
                            <div class="form-group row">
                                <label for="end_date" class="col-sm-2 col-form-label">Duration Details</label>
                                <div class="col-sm-6 main-field">
                                    <?= $course->duration_details; ?>
                                </div>
                            </div>

                            <h4>Batches</h4>
                            <div class="table-responsive">
                                <table class="table table-striped align-middle">
                                    <thead class="table-primary">
                                        <tr>
                                            <th>#</th>
                                            <th>Name</th>
                                            <th>Start Date</th>
                                            <th>End Date</th>
                                            <th>Duration Details</th>
                                            <th>Start Time</th>
                                            <th>Capacity</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($course->course_batches as $index => $batch): ?>
                                            <tr>
                                                <td><?= $index + 1 ?></td>
                                                <td><?= h($batch->name) ?></td>
                                                <td><?= $batch->start_date ? $batch->start_date->format("m-d-Y") : '—' ?></td>
                                                <td><?= $batch->end_date ? $batch->end_date->format("m-d-Y") : '—' ?></td>
                                                <td><?= h($batch->duration_details) ?></td>
                                                <td><?= h($batch->start_time) ?></td>
                                                <td><?= h($batch->capacity) ?></td>
                                                <td><?= h(ucfirst($batch->status)) ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="text-muted">No batches available for this course.</p>
                        <?php endif; ?>

                        </div>

                        <div class="tab-pane fade" id="pricing" role="tabpanel">
                            <h4>Base Pricing</h4>
                            <?php if (!empty($course->course_base_prices)): ?>
                                <div class="table-responsive">
                                    <table class="table table-striped align-middle">
                                        <thead class="table-primary">
                                            <tr>
                                                <th>#</th>
                                                <th>Name</th>
                                                <th>Currency</th>
                                                <th>Price</th>
                                                <th>Total Count</th>
                                                <th>Sort Order</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($course->course_base_prices as $index => $price): ?>
                                                <tr>
                                                    <td><?= $index + 1 ?></td>
                                                    <td><?= h($price->name) ?></td>
                                                    <td><?= !empty($price->currency) ? h($price->currency->name) : '' ?></td>
                                                    <td><?= h($price->price) ?></td>
                                                    <td><?= h($price->total_count) ?></td>
                                                    <td><?= h($price->sort_order) ?></td>
                                                    <td><?= h(ucfirst($price->status)) ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <p class="text-muted">No base prices available for this course.</p>
                            <?php endif; ?>

                      
                            <h4>Addons</h4>
                            <?php if (!empty($course->course_addons)): ?>
                            <div class="table-responsive">
                                <table class="table table-striped align-middle">
                                    <thead class="table-primary">
                                        <tr>
                                            <th>#</th>
                                            <th>Addon Type</th>
                                            <th>Addon Name</th>
                                            <th>Custom Name</th>
                                            <th>Custom Category</th>
                                            <th>Max Count</th>
                                            <th>Sort Order</th>
                                            <th>Status</th>
                                            <th>Pricing</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($course->course_addons as $index => $addon): ?>
                                            <tr>
                                                <td><?= $index + 1 ?></td>
                                                <td><?= !empty($addon->master_data) ? h($addon->master_data->type) : '' ?></td>
                                                <td><?= !empty($addon->master_data) ? h($addon->master_data->title) : '' ?></td>
                                                <td><?= h($addon->custom_name) ?></td>
                                                <td><?= h($addon->custom_category) ?></td>
                                                <td><?= h($addon->total_slots) ?></td>
                                                <td><?= h($addon->sort_order) ?></td>
                                                <td><?= h(ucfirst($addon->status)) ?></td>
                                                <td>
                                                    <?php if (!empty($addon->course_addon_pricing)): ?>
                                                        <table class="table table-bordered table-sm mb-0">
                                                            <thead>
                                                                <tr>
                                                                    <th>Currency</th>
                                                                    <th>Price</th>
                                                                    <th>Status</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <?php foreach ($addon->course_addon_pricing as $addonp): ?>
                                                                    <tr>
                                                                        <td><?= !empty($addonp->currency) ? h($addonp->currency->name) : '' ?></td>
                                                                        <td><?= h($addonp->price) ?></td>
                                                                        <td><?= h(ucfirst($addonp->status)) ?></td>
                                                                    </tr>
                                                                <?php endforeach; ?>
                                                            </tbody>
                                                        </table>
                                                    <?php else: ?>
                                                        <span class="text-muted">No pricing</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="text-muted">No addons available for this course.</p>
                        <?php endif; ?>

                        </div>
                        <div class="tab-pane fade" id="media" role="tabpanel">
                             <div class="form-group row">
                                <label for="service_image" class="col-sm-2 col-form-label"><?= __("Media") ?></label>
                                <div class="col-sm-5 main-field">
                                    <div id="previeContainer">
                                           <ul id="imagePreviewContainer" class="list-unstyled d-flex flex-wrap gap-2 mt-2">
                                            <?php if (!empty($course->course_galleries)) : ?>
                                                <?php foreach ($course->course_galleries as $index => $gallery) :
                                                if($gallery->media_type == 'url'): ?>
                                                    <li class="d-flex justify-content-between align-items-center"><?=  $gallery->media ?></li>

                                               <?php else:
                                                    $mediaUrl = $media->displayImage($gallery->media); // This gives full path from MediaComponent
                                                  
                                                ?>
                                                    <li class="media-thumbnail position-relative">
                                                        <?php if ($gallery->media_type == 'image'): ?>
                                                            <img src="<?= $this->Url->webroot($mediaUrl) ?>" alt="<?= __("Media Image") ?>" style="max-width: 100px; max-height: 100px;" />
                                                        <?php elseif ($gallery->media_type == 'video'): ?>
                                                            <video class="preview-video" width="120" height="100" controls>
                                                                <source src="<?= $this->Url->webroot($mediaUrl) ?>" type="video/<?= h($extension) ?>">
                                                                <?= __("Your browser does not support the video tag.") ?>
                                                            </video>
                                                        <?php else: ?>
                                                            <span class="text-muted"><?= __("Unsupported media type") ?></span>
                                                        <?php endif; ?>
                                                        <!-- <span class="image-name" title="<?= h(basename($mediaPath)) ?>">
                                                            <?= h(basename($mediaPath)) ?>
                                                        </span> -->
                                                    </li>

                                                <?php  endif; endforeach; ?>

                                            <?php else : ?>
                                                <li><?= __("No Media Available") ?></li>
                                            <?php endif; ?>
                                        </ul>
                                     
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="seo" role="tabpanel">
                            <div class="form-group row">
                                <label for="old_url" class="col-sm-2 col-form-label">Old URL</label>
                                <div class="col-sm-9 main-field">
                                    <?= h($course->old_url) ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="meta_title" class="col-sm-2 col-form-label">Meta Title</label>
                                <div class="col-sm-9 main-field">
                                    <?= h($course->meta_title) ?>
                                </div>
                            </div>
                              <div class="form-group row">
                                <label for="description" class="col-sm-2 col-form-label">Meta Description</label>
                                <div class="col-sm-9 main-field">
                                    <?= h($course->meta_description) ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="keywords" class="col-sm-2 col-form-label">Meta Keywords</label>
                                <div class="col-sm-9 main-field">
                                    <?= h($course->meta_keywords) ?>
                                </div>
                            </div>
                             <div class="form-group row">
                                <label for="keywords" class="col-sm-2 col-form-label">Meta Robots</label>
                                <div class="col-sm-9 main-field">
                                    <?= h($course->meta_robots) ?>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="review" role="tabpanel">
                        </div>
                    </div>
                </div>
                <?= $this->Form->end(); ?>
            </div>
        </div>
    </div>
</div>
</div>
<?php $this->append('script'); ?>
<script>
document.addEventListener('DOMContentLoaded', () => {
    document.querySelectorAll('.view-desc-btn').forEach(btn => {
        btn.addEventListener('click', function () {
            const id = this.dataset.id;
            const html = document.getElementById(`desc-html-${id}`).innerHTML;
            document.getElementById('descModalBody').innerHTML = html;
        });
    });
});
</script>
<?php $this->end(); ?>