<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * CourseModalities Model
 *
 * @property \App\Model\Table\CoursesTable&\Cake\ORM\Association\BelongsTo $Courses
 * @property \App\Model\Table\ModalitiesTable&\Cake\ORM\Association\BelongsTo $Modalities
 *
 * @method \App\Model\Entity\CourseModality newEmptyEntity()
 * @method \App\Model\Entity\CourseModality newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\CourseModality> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\CourseModality get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\CourseModality findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\CourseModality patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\CourseModality> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\CourseModality|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\CourseModality saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\CourseModality>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\CourseModality>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\CourseModality>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\CourseModality> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\CourseModality>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\CourseModality>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\CourseModality>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\CourseModality> deleteManyOrFail(iterable $entities, array $options = [])
 */
class CourseModalitiesTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('course_modalities');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->belongsTo('Courses', [
            'foreignKey' => 'course_id',
            'joinType' => 'INNER',
        ]);
        $this->belongsTo('Modalities', [
            'foreignKey' => 'modality_id',
            'joinType' => 'INNER',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('course_id')
            ->notEmptyString('course_id');

        $validator
            ->integer('modality_id')
            ->notEmptyString('modality_id');

        $validator
            ->integer('created_at')
            ->requirePresence('created_at', 'create')
            ->notEmptyString('created_at');

        $validator
            ->integer('modified_at')
            ->requirePresence('modified_at', 'create')
            ->notEmptyString('modified_at');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['course_id'], 'Courses'), ['errorField' => 'course_id']);
        $rules->add($rules->existsIn(['modality_id'], 'Modalities'), ['errorField' => 'modality_id']);

        return $rules;
    }

    public function getExistingIds($courseId){
        $res = $this->find()
            ->where(['course_id' => $courseId])
            ->all()
            ->extract('modality_id')
            ->toList();

        return $res;
    }
}
